#!/usr/bin/env python3
"""
Debug script to compare original vs refactored gamma wall calculations
"""

import sys
import os
sys.path.append('src')

# Import original implementation
from checklist_driver import OptionSignalGenerator as OriginalOptionGen

# Import refactored implementation  
from analytics.option_analyzer import OptionAnalyzer
from market_data import PolygonDataFetcher

def compare_gamma_calculations():
    """Compare gamma calculations between original and refactored versions"""
    print("=== GAMMA WALL COMPARISON ===")
    
    # Initialize both implementations
    print("\n1. Initializing both implementations...")
    original = OriginalOptionGen(ticker="^GSPC", days_back=30)
    
    polygon_client = PolygonDataFetcher()
    refactored = OptionAnalyzer()
    
    # Fetch data with original
    print("\n2. Fetching data with original implementation...")
    original.fetch_price_data()
    original_chain = original.fetch_option_chain()
    original_current_price = original.get_current_price()
    
    print(f"Original current price: {original_current_price}")
    if original_chain:
        print(f"Original chain - Calls: {len(original_chain.get('calls', []))}, Puts: {len(original_chain.get('puts', []))}")
    
    # Analyze with original
    print("\n3. Analyzing with original implementation...")
    original_strike_levels = original.identify_strike_levels()
    
    if original_strike_levels:
        print("Original Strike Levels:")
        print(f"  - Call walls: {len(original_strike_levels.get('call_walls', []))}")
        print(f"  - Put walls: {len(original_strike_levels.get('put_walls', []))}")
        print(f"  - Net gamma: {original_strike_levels.get('net_gamma', 0):,.0f}")
        print(f"  - Gamma trend: {original_strike_levels.get('gamma_trend', 'UNKNOWN')}")
        
        # Show call wall details
        call_walls = original_strike_levels.get('call_walls', [])
        for i, wall in enumerate(call_walls):
            print(f"    Call Wall {i+1}: Strike {wall['strike']:.0f}, OI {wall['openInterest']:,}")
            
        # Show put wall details  
        put_walls = original_strike_levels.get('put_walls', [])
        for i, wall in enumerate(put_walls):
            print(f"    Put Wall {i+1}: Strike {wall['strike']:.0f}, OI {wall['openInterest']:,}")
    
    # Use same data for refactored
    print("\n4. Analyzing with refactored implementation...")
    refactored.set_option_chain(original_chain)
    refactored_strike_levels = refactored.identify_strike_levels()
    
    if refactored_strike_levels:
        print("Refactored Strike Levels:")
        print(f"  - Call walls: {len(refactored_strike_levels.get('call_walls', []))}")
        print(f"  - Put walls: {len(refactored_strike_levels.get('put_walls', []))}")
        print(f"  - Net gamma: {refactored_strike_levels.get('net_gamma', 0):,.0f}")
        print(f"  - Gamma trend: {refactored_strike_levels.get('gamma_trend', 'UNKNOWN')}")
        
        # Show call wall details
        call_walls = refactored_strike_levels.get('call_walls', [])
        for i, wall in enumerate(call_walls):
            print(f"    Call Wall {i+1}: Strike {wall['strike']:.0f}, OI {wall['openInterest']:,}")
            
        # Show put wall details  
        put_walls = refactored_strike_levels.get('put_walls', [])
        for i, wall in enumerate(put_walls):
            print(f"    Put Wall {i+1}: Strike {wall['strike']:.0f}, OI {wall['openInterest']:,}")
    
    # Compare results
    print("\n5. COMPARISON:")
    if original_strike_levels and refactored_strike_levels:
        orig_net_gamma = original_strike_levels.get('net_gamma', 0)
        refact_net_gamma = refactored_strike_levels.get('net_gamma', 0)
        
        print(f"Net Gamma - Original: {orig_net_gamma:,.0f}, Refactored: {refact_net_gamma:,.0f}")
        print(f"Difference: {abs(orig_net_gamma - refact_net_gamma):,.0f}")
        
        orig_call_walls = len(original_strike_levels.get('call_walls', []))
        refact_call_walls = len(refactored_strike_levels.get('call_walls', []))
        print(f"Call Walls - Original: {orig_call_walls}, Refactored: {refact_call_walls}")
        
        orig_put_walls = len(original_strike_levels.get('put_walls', []))
        refact_put_walls = len(refactored_strike_levels.get('put_walls', []))
        print(f"Put Walls - Original: {orig_put_walls}, Refactored: {refact_put_walls}")
        
        if orig_net_gamma == refact_net_gamma and orig_call_walls == refact_call_walls and orig_put_walls == refact_put_walls:
            print("✅ MATCH: Results are identical!")
        else:
            print("❌ MISMATCH: Results differ!")
    
    # Generate plots for comparison
    print("\n6. Generating comparison plots...")
    original_plot = original.plot_gamma_wall("original_gamma_wall.png")
    refactored_plot = refactored.plot_gamma_wall("refactored_gamma_wall.png")
    
    if original_plot and refactored_plot:
        print("✅ Both plots generated successfully")
        print(f"Original: {original_plot}")
        print(f"Refactored: {refactored_plot}")
    else:
        print("❌ Plot generation failed")

if __name__ == "__main__":
    compare_gamma_calculations() 