#!/usr/bin/env python3
"""
Debug script to compare gamma calculations between original and new implementations
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
from checklist_driver import OptionSignalGenerator as OriginalOptionGen
from analytics.option_analyzer import OptionAnalyzer
from market_data.polygon_client import PolygonDataFetcher

def debug_gamma_calculation():
    """Debug gamma calculation differences"""
    print("=== GAMMA CALCULATION DEBUG ===")
    
    # Get the same option chain for both implementations
    polygon_client = PolygonDataFetcher()
    option_chain = polygon_client.fetch_option_chain("SPX", price_range=None)
    
    if not option_chain:
        print("❌ Failed to get option chain")
        return
    
    current_price = option_chain.get("current_price")
    calls = option_chain.get("calls", [])
    puts = option_chain.get("puts", [])
    
    print(f"Option chain: {len(calls)} calls, {len(puts)} puts")
    print(f"Current price: {current_price}")
    
    # Test 1: Original implementation calculation
    print("\n1. Testing Original Implementation Gamma Calculation...")
    
    # Create original analyzer and set the same option chain
    original = OriginalOptionGen(ticker="^GSPC", days_back=30)
    original.option_chain = option_chain  # Use same chain
    original_strike_levels = original.identify_strike_levels()
    
    if original_strike_levels:
        original_net_gamma = original_strike_levels.get("net_gamma", 0)
        print(f"Original Net Gamma: {original_net_gamma:,.0f}")
    else:
        print("❌ Original calculation failed")
        return
    
    # Test 2: New implementation calculation
    print("\n2. Testing New Implementation Gamma Calculation...")
    
    new_analyzer = OptionAnalyzer()
    new_analyzer.set_option_chain(option_chain)
    new_strike_levels = new_analyzer.identify_strike_levels()
    
    if new_strike_levels:
        new_net_gamma = new_strike_levels.get("net_gamma", 0)
        print(f"New Net Gamma: {new_net_gamma:,.0f}")
    else:
        print("❌ New calculation failed")
        return
    
    # Test 3: Manual calculation to verify
    print("\n3. Manual Gamma Calculation Verification...")
    
    # Convert to DataFrames
    calls_df = pd.DataFrame(calls)
    puts_df = pd.DataFrame(puts)
    
    # Check if we have the required columns
    required_cols = ["strike", "impliedVolatility", "openInterest"]
    for col in required_cols:
        if col not in calls_df.columns:
            print(f"❌ Missing column in calls: {col}")
            return
        if col not in puts_df.columns:
            print(f"❌ Missing column in puts: {col}")
            return
    
    # Sample a few options to check calculations
    print("\nSample Gamma Calculations:")
    print("Strike | IV | OI | Gamma | Gamma Exposure")
    print("-" * 50)
    
    # Check first 5 calls
    for i in range(min(5, len(calls_df))):
        row = calls_df.iloc[i]
        strike = row["strike"]
        iv = row["impliedVolatility"]
        oi = row["openInterest"]
        
        # Calculate gamma using original method
        from checklist_driver import calculate_gamma, get_risk_free_rate
        time_to_expiry = 7/365.25
        risk_free_rate = get_risk_free_rate()
        
        gamma = calculate_gamma(current_price, strike, time_to_expiry, risk_free_rate, iv)
        gamma_exposure = oi * gamma * 100
        
        print(f"{strike:6.0f} | {iv:4.1%} | {oi:4.0f} | {gamma:6.4f} | {gamma_exposure:8.0f}")
    
    # Calculate total gamma exposure manually
    total_call_gamma = 0
    total_put_gamma = 0
    
    for _, row in calls_df.iterrows():
        strike = row["strike"]
        iv = row["impliedVolatility"]
        oi = row["openInterest"]
        
        gamma = calculate_gamma(current_price, strike, time_to_expiry, risk_free_rate, iv)
        gamma_exposure = oi * gamma * 100
        total_call_gamma += gamma_exposure
    
    for _, row in puts_df.iterrows():
        strike = row["strike"]
        iv = row["impliedVolatility"]
        oi = row["openInterest"]
        
        gamma = calculate_gamma(current_price, strike, time_to_expiry, risk_free_rate, iv)
        gamma_exposure = oi * gamma * 100
        total_put_gamma += gamma_exposure
    
    manual_net_gamma = total_call_gamma - total_put_gamma
    
    print(f"\nManual Calculation Results:")
    print(f"Total Call Gamma: {total_call_gamma:,.0f}")
    print(f"Total Put Gamma: {total_put_gamma:,.0f}")
    print(f"Manual Net Gamma: {manual_net_gamma:,.0f}")
    
    # Compare results
    print(f"\n4. COMPARISON:")
    print(f"Original Net Gamma: {original_net_gamma:,.0f}")
    print(f"New Net Gamma: {new_net_gamma:,.0f}")
    print(f"Manual Net Gamma: {manual_net_gamma:,.0f}")
    
    if abs(original_net_gamma - manual_net_gamma) < 100:
        print("✅ Original implementation matches manual calculation")
    else:
        print("❌ Original implementation differs from manual calculation")
    
    if abs(new_net_gamma - manual_net_gamma) < 100:
        print("✅ New implementation matches manual calculation")
    else:
        print("❌ New implementation differs from manual calculation")
        
        # Debug the new implementation
        print("\nDebugging new implementation...")
        
        # Check if the new implementation is using the same risk-free rate
        from analytics.black_scholes import BlackScholesCalculator
        bs_calc = BlackScholesCalculator()
        print(f"New implementation risk-free rate: {bs_calc.risk_free_rate}")
        print(f"Original implementation risk-free rate: {risk_free_rate}")
        
        # Check a sample calculation
        sample_strike = calls_df.iloc[0]["strike"]
        sample_iv = calls_df.iloc[0]["impliedVolatility"]
        
        # Original calculation
        orig_gamma = calculate_gamma(current_price, sample_strike, time_to_expiry, risk_free_rate, sample_iv)
        
        # New calculation
        new_greeks = bs_calc.calculate_all_greeks(current_price, sample_strike, time_to_expiry, sample_iv, "call")
        new_gamma = new_greeks.get("gamma", 0)
        
        print(f"\nSample calculation for strike {sample_strike}:")
        print(f"Original gamma: {orig_gamma:.6f}")
        print(f"New gamma: {new_gamma:.6f}")
        print(f"Difference: {abs(orig_gamma - new_gamma):.6f}")

if __name__ == "__main__":
    debug_gamma_calculation() 