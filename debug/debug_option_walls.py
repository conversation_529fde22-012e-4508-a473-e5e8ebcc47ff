#!/usr/bin/env python3
"""
Debug script to investigate option wall detection issues
"""

import sys
import os
sys.path.append('src')
from checklist_driver import OptionSignalGenerator
import pandas as pd

def debug_option_walls():
    """Debug option wall detection"""
    print("Debugging Option Wall Detection...")
    
    # Initialize option signal generator
    option_gen = OptionSignalGenerator(ticker="^GSPC", days_back=30)
    
    # Fetch price data
    print("\n1. Fetching price data...")
    option_gen.fetch_price_data()
    current_price = option_gen.get_current_price()
    print(f"Current S&P 500 price: {current_price}")
    
    # Fetch option chain
    print("\n2. Fetching option chain...")
    option_chain = option_gen.fetch_option_chain()
    
    if option_chain:
        print(f"Option chain fetched successfully")
        print(f"Current price in chain: {option_chain.get('current_price')}")
        print(f"Number of calls: {len(option_chain.get('calls', []))}")
        print(f"Number of puts: {len(option_chain.get('puts', []))}")
        print(f"use_real_options flag: {option_gen.use_real_options}")
        
        # Check data source detection logic
        calls_exist = option_chain.get("calls")
        print(f"Calls exist: {bool(calls_exist)}")
        print(f"option_chain type: {type(option_chain)}")
        print(f"hasattr check: {hasattr(option_chain, 'get')}")
        
        # Debug the data source detection
        data_source_check = option_gen.use_real_options and hasattr(option_chain, 'get') and option_chain.get("calls")
        print(f"Data source check result: {data_source_check}")
        
        # Look at actual option data
        calls_df = pd.DataFrame(option_chain["calls"])
        puts_df = pd.DataFrame(option_chain["puts"])
        
        print(f"\n3. Analyzing option data...")
        print(f"Calls DataFrame shape: {calls_df.shape}")
        print(f"Puts DataFrame shape: {puts_df.shape}")
        
        if not calls_df.empty:
            print(f"Call strikes range: {calls_df['strike'].min():.0f} - {calls_df['strike'].max():.0f}")
            print(f"Call OI range: {calls_df['openInterest'].min()} - {calls_df['openInterest'].max()}")
            print(f"Sample call data:")
            print(calls_df[['strike', 'openInterest']].head())
            
            # Check strikes near current price
            price_range = (current_price * 0.9, current_price * 1.1)
            calls_nearby = calls_df[(calls_df["strike"] > current_price) & (calls_df["strike"] <= price_range[1])]
            print(f"\nCalls near current price ({price_range[0]:.0f} - {price_range[1]:.0f}): {len(calls_nearby)}")
            if not calls_nearby.empty:
                top_call_oi = calls_nearby.nlargest(3, "openInterest")
                print("Top 3 call OI near current price:")
                print(top_call_oi[['strike', 'openInterest']])
        
        if not puts_df.empty:
            print(f"\nPut strikes range: {puts_df['strike'].min():.0f} - {puts_df['strike'].max():.0f}")
            print(f"Put OI range: {puts_df['openInterest'].min()} - {puts_df['openInterest'].max()}")
            
            # Check strikes near current price
            puts_nearby = puts_df[(puts_df["strike"] < current_price) & (puts_df["strike"] >= price_range[0])]
            print(f"Puts near current price ({price_range[0]:.0f} - {price_range[1]:.0f}): {len(puts_nearby)}")
            if not puts_nearby.empty:
                top_put_oi = puts_nearby.nlargest(3, "openInterest")
                print("Top 3 put OI near current price:")
                print(top_put_oi[['strike', 'openInterest']])
    
    # Test strike level identification
    print("\n4. Testing strike level identification...")
    strike_levels = option_gen.identify_strike_levels()
    
    if strike_levels:
        print(f"Strike levels identified successfully")
        print(f"Data source: {strike_levels.get('data_source')}")
        print(f"Current price: {strike_levels.get('current_price')}")
        print(f"Call walls: {len(strike_levels.get('call_walls', []))}")
        print(f"Put walls: {len(strike_levels.get('put_walls', []))}")
        print(f"Gamma exposure: {strike_levels.get('gamma_exposure')}")
        
        # Show wall details
        call_walls = strike_levels.get('call_walls', [])
        put_walls = strike_levels.get('put_walls', [])
        
        if call_walls:
            print("\nCall walls found:")
            for i, wall in enumerate(call_walls):
                print(f"  Wall {i+1}: Strike {wall['strike']:.0f}, OI {wall['openInterest']}")
        else:
            print("\nNo call walls found")
            
        if put_walls:
            print("\nPut walls found:")
            for i, wall in enumerate(put_walls):
                print(f"  Wall {i+1}: Strike {wall['strike']:.0f}, OI {wall['openInterest']}")
        else:
            print("\nNo put walls found")
    else:
        print("Strike level identification failed")

if __name__ == "__main__":
    debug_option_walls() 