#!/usr/bin/env python3
"""
Detailed debug script for gamma wall issues
"""

import sys
import os
sys.path.append('src')
from checklist_driver import OptionSignalGenerator
import pandas as pd
import numpy as np

def debug_gamma_calculations():
    """Debug the gamma wall calculations in detail"""
    print("Detailed Gamma Wall Debug...")
    
    # Initialize option signal generator
    option_gen = OptionSignalGenerator(ticker="^GSPC", days_back=30)
    
    # Fetch data
    option_gen.fetch_price_data()
    current_price = option_gen.get_current_price()
    print(f"Current price: {current_price}")
    
    option_chain = option_gen.fetch_option_chain()
    strike_levels = option_gen.identify_strike_levels()
    
    if not option_chain or not strike_levels:
        print("Failed to get option data")
        return
    
    # Check the raw option data
    calls_df = pd.DataFrame(option_chain["calls"])
    puts_df = pd.DataFrame(option_chain["puts"])
    
    print(f"\nCalls data shape: {calls_df.shape}")
    print(f"Puts data shape: {puts_df.shape}")
    
    # Check if gamma column exists
    print(f"\nCalls columns: {calls_df.columns.tolist()}")
    print(f"Puts columns: {puts_df.columns.tolist()}")
    
    # Check for gamma values
    if 'gamma' in calls_df.columns:
        print(f"\nCalls gamma stats:")
        print(f"  Min: {calls_df['gamma'].min()}")
        print(f"  Max: {calls_df['gamma'].max()}")
        print(f"  Mean: {calls_df['gamma'].mean()}")
        print(f"  Non-zero count: {(calls_df['gamma'] != 0).sum()}")
    else:
        print("\n❌ No gamma column in calls data!")
    
    if 'gamma' in puts_df.columns:
        print(f"\nPuts gamma stats:")
        print(f"  Min: {puts_df['gamma'].min()}")
        print(f"  Max: {puts_df['gamma'].max()}")
        print(f"  Mean: {puts_df['gamma'].mean()}")
        print(f"  Non-zero count: {(puts_df['gamma'] != 0).sum()}")
    else:
        print("\n❌ No gamma column in puts data!")
    
    # Check sample data
    print(f"\nSample calls data (first 5 rows):")
    print(calls_df.head())
    
    print(f"\nSample puts data (first 5 rows):")
    print(puts_df.head())
    
    # Test gamma calculation manually
    print(f"\n=== Manual Gamma Calculation Test ===")
    from checklist_driver import calculate_gamma, get_risk_free_rate
    
    risk_free_rate = get_risk_free_rate()
    time_to_expiry = 7 / 365.25
    
    # Test with a few strikes
    test_strikes = [5800, current_price, 5850]
    test_iv = 0.25
    
    for strike in test_strikes:
        gamma = calculate_gamma(current_price, strike, time_to_expiry, risk_free_rate, test_iv)
        print(f"Strike {strike}: Gamma = {gamma:.6f}")
    
    # Check the gamma exposure calculation in the plot function
    print(f"\n=== Gamma Exposure Calculation ===")
    price_buffer = 300
    min_strike = current_price - price_buffer
    max_strike = current_price + price_buffer
    
    calls_filtered = calls_df[(calls_df["strike"] >= min_strike) & (calls_df["strike"] <= max_strike)]
    puts_filtered = puts_df[(puts_df["strike"] >= min_strike) & (puts_df["strike"] <= max_strike)]
    
    print(f"Filtered calls: {len(calls_filtered)}")
    print(f"Filtered puts: {len(puts_filtered)}")
    
    if 'gamma' in calls_filtered.columns:
        calls_filtered_copy = calls_filtered.copy()
        calls_filtered_copy["gamma_exposure"] = calls_filtered_copy["openInterest"] * calls_filtered_copy["gamma"] * 100
        
        print(f"\nCalls gamma exposure stats:")
        print(f"  Min: {calls_filtered_copy['gamma_exposure'].min()}")
        print(f"  Max: {calls_filtered_copy['gamma_exposure'].max()}")
        print(f"  Sum: {calls_filtered_copy['gamma_exposure'].sum()}")
        print(f"  Non-zero count: {(calls_filtered_copy['gamma_exposure'] != 0).sum()}")
        
        # Show top gamma exposures
        top_calls = calls_filtered_copy.nlargest(5, 'gamma_exposure')[['strike', 'openInterest', 'gamma', 'gamma_exposure']]
        print(f"\nTop 5 call gamma exposures:")
        print(top_calls)
    
    if 'gamma' in puts_filtered.columns:
        puts_filtered_copy = puts_filtered.copy()
        puts_filtered_copy["gamma_exposure"] = puts_filtered_copy["openInterest"] * puts_filtered_copy["gamma"] * 100
        
        print(f"\nPuts gamma exposure stats:")
        print(f"  Min: {puts_filtered_copy['gamma_exposure'].min()}")
        print(f"  Max: {puts_filtered_copy['gamma_exposure'].max()}")
        print(f"  Sum: {puts_filtered_copy['gamma_exposure'].sum()}")
        print(f"  Non-zero count: {(puts_filtered_copy['gamma_exposure'] != 0).sum()}")
        
        # Show top gamma exposures
        top_puts = puts_filtered_copy.nlargest(5, 'gamma_exposure')[['strike', 'openInterest', 'gamma', 'gamma_exposure']]
        print(f"\nTop 5 put gamma exposures:")
        print(top_puts)

if __name__ == "__main__":
    debug_gamma_calculations() 