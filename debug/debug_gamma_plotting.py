#!/usr/bin/env python3
"""
Debug script to compare gamma wall plotting between main driver and test script
"""

import sys
import os
sys.path.append('src')

# Import both implementations
from checklist_driver import OptionSignalGenerator as OriginalOptionGen
from analytics.option_analyzer import OptionAnalyzer
from market_data.polygon_client import PolygonDataFetcher
from market_data.price_data import PriceDataFetcher

def debug_gamma_plotting():
    """Debug gamma wall plotting differences"""
    print("=== GAMMA WALL PLOTTING DEBUG ===")
    
    # Test 1: Original implementation (like test_gamma_wall.py)
    print("\n1. Testing Original Implementation (test_gamma_wall.py approach)...")
    original = OriginalOptionGen(ticker="^GSPC", days_back=30)
    original.fetch_price_data()
    original_chain = original.fetch_option_chain()
    original_current_price = original.get_current_price()
    
    print(f"Original - Current price: {original_current_price}")
    if original_chain:
        print(f"Original - Calls: {len(original_chain.get('calls', []))}, Puts: {len(original_chain.get('puts', []))}")
        print(f"Original - Data source: {original_chain.get('data_source', 'Unknown')}")
    
    original_strike_levels = original.identify_strike_levels()
    if original_strike_levels:
        print(f"Original - Net gamma: {original_strike_levels.get('net_gamma', 0):,.0f}")
        print(f"Original - Call walls: {len(original_strike_levels.get('call_walls', []))}")
        print(f"Original - Put walls: {len(original_strike_levels.get('put_walls', []))}")
    
    # Test 2: Main driver approach
    print("\n2. Testing Main Driver Approach...")
    
    # Initialize components like main driver
    polygon_client = PolygonDataFetcher()
    price_fetcher = PriceDataFetcher()
    option_analyzer = OptionAnalyzer()
    
    # Fetch data like main driver
    ticker = "SPX"  # Main driver uses SPX
    price_ticker = "^GSPC"  # But price from ^GSPC
    
    print(f"Main driver - Using option ticker: {ticker}, price ticker: {price_ticker}")
    
    # Get price data
    price_data = price_fetcher.get_historical_prices(price_ticker, 120)
    current_price = price_data["Adj Close"].iloc[-1] if not price_data.empty else None
    print(f"Main driver - Current price: {current_price}")
    
    # Get option chain (full chain like main driver)
    option_chain = polygon_client.fetch_option_chain(ticker, price_range=None)
    if option_chain:
        print(f"Main driver - Calls: {len(option_chain.get('calls', []))}, Puts: {len(option_chain.get('puts', []))}")
        print(f"Main driver - Data source: {option_chain.get('data_source', 'Unknown')}")
        print(f"Main driver - Option chain current price: {option_chain.get('current_price')}")
    
    # Set option chain and analyze
    option_analyzer.set_option_chain(option_chain)
    main_strike_levels = option_analyzer.identify_strike_levels()
    
    if main_strike_levels:
        print(f"Main driver - Net gamma: {main_strike_levels.get('net_gamma', 0):,.0f}")
        print(f"Main driver - Call walls: {len(main_strike_levels.get('call_walls', []))}")
        print(f"Main driver - Put walls: {len(main_strike_levels.get('put_walls', []))}")
    
    # Test 3: Compare the option chains
    print("\n3. COMPARING OPTION CHAINS...")
    
    if original_chain and option_chain:
        orig_calls = len(original_chain.get('calls', []))
        main_calls = len(option_chain.get('calls', []))
        print(f"Calls count - Original: {orig_calls}, Main: {main_calls}")
        
        orig_puts = len(original_chain.get('puts', []))
        main_puts = len(option_chain.get('puts', []))
        print(f"Puts count - Original: {orig_puts}, Main: {main_puts}")
        
        orig_price = original_chain.get('current_price')
        main_price = option_chain.get('current_price')
        print(f"Current price - Original: {orig_price}, Main: {main_price}")
        
        if orig_calls != main_calls or orig_puts != main_puts:
            print("❌ DIFFERENT OPTION CHAINS - This explains the plotting difference!")
        else:
            print("✅ Same option chain size")
    
    # Test 4: Generate plots for comparison
    print("\n4. Generating comparison plots...")
    
    # Original plot
    original_plot = original.plot_gamma_wall("debug_original_gamma.png")
    print(f"Original plot: {original_plot}")
    
    # Main driver plot
    main_plot = option_analyzer.plot_gamma_wall("debug_main_driver_gamma.png")
    print(f"Main driver plot: {main_plot}")
    
    print("\n✅ Debug complete - check the generated plots!")

if __name__ == "__main__":
    debug_gamma_plotting() 