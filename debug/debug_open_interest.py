#!/usr/bin/env python3
"""
Debug script to check open interest data from Polygon API
"""

import sys
import os
sys.path.append('src')

import pandas as pd
from market_data.polygon_client import PolygonDataFetcher
from checklist_driver import OptionSignalGenerator as OriginalOptionGen

def debug_open_interest():
    """Debug open interest data differences"""
    print("=== OPEN INTEREST DEBUG ===")
    
    # Test 1: Original implementation
    print("\n1. Testing Original Implementation...")
    original = OriginalOptionGen(ticker="^GSPC", days_back=30)
    original.fetch_price_data()
    original_chain = original.fetch_option_chain()
    
    if original_chain:
        orig_calls = original_chain.get("calls", [])
        orig_puts = original_chain.get("puts", [])
        
        print(f"Original - Calls: {len(orig_calls)}, Puts: {len(orig_puts)}")
        
        # Check open interest in first 10 calls
        print("\nOriginal Implementation - Sample Call Open Interest:")
        print("Strike | IV | Open Interest | Volume")
        print("-" * 45)
        for i, call in enumerate(orig_calls[:10]):
            strike = call.get("strike", 0)
            iv = call.get("impliedVolatility", 0)
            oi = call.get("openInterest", 0)
            volume = call.get("volume", 0)
            print(f"{strike:6.0f} | {iv:4.1%} | {oi:11.0f} | {volume:6.0f}")
        
        # Calculate total open interest
        total_call_oi = sum(call.get("openInterest", 0) for call in orig_calls)
        total_put_oi = sum(put.get("openInterest", 0) for put in orig_puts)
        print(f"\nOriginal Total Call OI: {total_call_oi:,}")
        print(f"Original Total Put OI: {total_put_oi:,}")
    
    # Test 2: New implementation
    print("\n2. Testing New Implementation...")
    polygon_client = PolygonDataFetcher()
    new_chain = polygon_client.fetch_option_chain("SPX", price_range=None)
    
    if new_chain:
        new_calls = new_chain.get("calls", [])
        new_puts = new_chain.get("puts", [])
        
        print(f"New - Calls: {len(new_calls)}, Puts: {len(new_puts)}")
        
        # Check open interest in first 10 calls
        print("\nNew Implementation - Sample Call Open Interest:")
        print("Strike | IV | Open Interest | Volume")
        print("-" * 45)
        for i, call in enumerate(new_calls[:10]):
            strike = call.get("strike", 0)
            iv = call.get("impliedVolatility", 0)
            oi = call.get("openInterest", 0)
            volume = call.get("volume", 0)
            print(f"{strike:6.0f} | {iv:4.1%} | {oi:11.0f} | {volume:6.0f}")
        
        # Calculate total open interest
        total_call_oi = sum(call.get("openInterest", 0) for call in new_calls)
        total_put_oi = sum(put.get("openInterest", 0) for put in new_puts)
        print(f"\nNew Total Call OI: {total_call_oi:,}")
        print(f"New Total Put OI: {total_put_oi:,}")
        
        # Check if any options have non-zero open interest
        non_zero_oi_calls = [c for c in new_calls if c.get("openInterest", 0) > 0]
        non_zero_oi_puts = [p for p in new_puts if p.get("openInterest", 0) > 0]
        
        print(f"\nOptions with non-zero OI:")
        print(f"Calls: {len(non_zero_oi_calls)}/{len(new_calls)}")
        print(f"Puts: {len(non_zero_oi_puts)}/{len(new_puts)}")
        
        if non_zero_oi_calls:
            print("\nSample calls with non-zero OI:")
            for call in non_zero_oi_calls[:5]:
                strike = call.get("strike", 0)
                oi = call.get("openInterest", 0)
                print(f"  Strike {strike}: OI = {oi}")
    
    # Test 3: Check if it's a data source issue
    print("\n3. Checking Data Source Differences...")
    
    if original_chain and new_chain:
        orig_source = original_chain.get("data_source", "Unknown")
        new_source = new_chain.get("data_source", "Unknown")
        
        print(f"Original data source: {orig_source}")
        print(f"New data source: {new_source}")
        
        # Check if they're using different tickers
        orig_ticker = original_chain.get("option_ticker", "Unknown")
        new_ticker = new_chain.get("option_ticker", "Unknown")
        
        print(f"Original ticker: {orig_ticker}")
        print(f"New ticker: {new_ticker}")

if __name__ == "__main__":
    debug_open_interest() 