#!/bin/bash

# Activate virtual environment
source .venv/bin/activate

# Load environment variables from .env file
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Default values
TICKER="SPX"
OUTPUT_DIR="output"
NARRATIVE_FLAG=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ticker)
            TICKER="$2"
            shift 2
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --narrative)
            NARRATIVE_FLAG="--narrative"
            shift
            ;;
        *)
            echo "Unknown parameter: $1"
            exit 1
            ;;
    esac
done

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Run the analysis with the specified parameters
# Pass the same ticker to all parameters by default
python src/driver/main.py --ticker "$TICKER" --price-ticker "$TICKER" --options-ticker "$TICKER" --days 30 --output-dir "$OUTPUT_DIR" $NARRATIVE_FLAG 