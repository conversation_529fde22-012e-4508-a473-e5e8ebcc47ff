#!/usr/bin/env python3
"""
Test script for gamma wall visualization
"""

import sys
import os
sys.path.append('src')
from checklist_driver import OptionSignalGenerator
import matplotlib.pyplot as plt

def test_gamma_wall():
    """Test the gamma wall visualization"""
    print("Testing Gamma Wall Visualization...")
    
    # Initialize option signal generator
    option_gen = OptionSignalGenerator(ticker="^GSPC", days_back=30)
    
    # Fetch price data
    print("\n1. Fetching price data...")
    option_gen.fetch_price_data()
    current_price = option_gen.get_current_price()
    print(f"Current S&P 500 price: {current_price}")
    
    # Fetch option chain
    print("\n2. Fetching option chain...")
    option_chain = option_gen.fetch_option_chain()
    
    if option_chain:
        print(f"   - Calls: {len(option_chain.get('calls', []))}")
        print(f"   - Puts: {len(option_chain.get('puts', []))}")
        print(f"   - Data source: {'Real' if option_chain.get('option_ticker') else 'Simulated'}")
    
    # Identify strike levels (includes Greek calculations)
    print("\n3. Analyzing strike levels and Greeks...")
    strike_levels = option_gen.identify_strike_levels()
    
    if strike_levels:
        print(f"   - Current price: {strike_levels['current_price']:.2f}")
        print(f"   - Call walls detected: {len(strike_levels.get('call_walls', []))}")
        print(f"   - Put walls detected: {len(strike_levels.get('put_walls', []))}")
        print(f"   - Net Gamma: {strike_levels.get('net_gamma', 0):,.0f}")
        print(f"   - Net Vega: {strike_levels.get('net_vega', 0):,.0f}")
        print(f"   - Gamma trend: {strike_levels.get('gamma_trend', 'UNKNOWN')}")
        print(f"   - Vega trend: {strike_levels.get('vega_trend', 'UNKNOWN')}")
    
    # Generate gamma wall plot
    print("\n4. Generating gamma wall visualization...")
    plot_path = option_gen.plot_gamma_wall(save_path="test_gamma_wall.png")
    
    if plot_path:
        print(f"✅ Gamma wall plot saved to: {plot_path}")
    else:
        print("❌ Failed to generate gamma wall plot")
    
    return plot_path

if __name__ == "__main__":
    test_gamma_wall() 