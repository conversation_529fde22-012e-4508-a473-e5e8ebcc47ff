#!/usr/bin/env python3
"""
Debug script to test TGA data alignment specifically
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import json

class FiscalDataFetcher:
    """Fetches data from Treasury FiscalData API."""
    BASE_URL = "https://api.fiscaldata.treasury.gov/services/api/fiscal_service/"

    def fetch_tga_balance(self, start_date, end_date):
        """Fetch Treasury General Account (TGA) balance data."""
        print("Fetching Treasury General Account (TGA) data...")
        endpoint = "v1/accounting/dts/operating_cash_balance"
        fields = "record_date,account_type,close_today_bal,open_today_bal"
        account_type_filter_value = "Treasury General Account (TGA) Closing Balance"
        fetch_start_date = start_date - timedelta(days=7)
        start_date_str = fetch_start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")
        filters = f"record_date:gte:{start_date_str},record_date:lte:{end_date_str},account_type:eq:{account_type_filter_value}"
        params = {
            "fields": fields,
            "filter": filters,
            "sort": "-record_date",
            "page[size]": 1000
        }
        url = f"{self.BASE_URL}{endpoint}"

        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()["data"]

            if not data:
                print("No TGA data returned from API.")
                return pd.DataFrame()

            df = pd.DataFrame(data)
            df["record_date"] = pd.to_datetime(df["record_date"])
            df.set_index("record_date", inplace=True)
            df.sort_index(inplace=True)
            for col in ["close_today_bal", "open_today_bal"]:
                df[col] = pd.to_numeric(df[col].str.replace(",", ""), errors="coerce") / 1_000_000_000
            print(f"Fetched {len(df)} TGA data points from {start_date_str} to {end_date_str}")
            return df[start_date:end_date]
        except requests.exceptions.RequestException as e:
            print(f"Error fetching TGA data: {e}")
            return pd.DataFrame()
        except (KeyError, json.JSONDecodeError) as e:
            print(f"Error parsing TGA data response: {e}")
            return pd.DataFrame()

def test_tga_alignment():
    """Test TGA data alignment with business day index"""
    print("Testing TGA Data Alignment...")
    
    # Use same date range as main script
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)
    
    print(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    # Fetch TGA data
    fiscal_fetcher = FiscalDataFetcher()
    tga_data = fiscal_fetcher.fetch_tga_balance(start_date, end_date)
    
    if tga_data.empty:
        print("✗ TGA data fetch failed")
        return
    
    print(f"✓ TGA data fetched successfully")
    print(f"TGA data shape: {tga_data.shape}")
    print(f"TGA date range: {tga_data.index.min()} to {tga_data.index.max()}")
    print(f"TGA index type: {type(tga_data.index[0])}")
    print(f"Sample TGA data:")
    print(tga_data.head())
    
    # Create business day index (same as main script)
    common_index = pd.date_range(start=start_date.date(), end=end_date.date(), freq="B")
    print(f"\nBusiness day index length: {len(common_index)}")
    print(f"Business day range: {common_index[0]} to {common_index[-1]}")
    print(f"Business day index type: {type(common_index[0])}")
    
    # Test alignment
    print(f"\n=== Testing TGA Alignment ===")
    
    # Test with close_today_bal column
    tga_series = tga_data["close_today_bal"].copy()
    print(f"Original TGA series length: {len(tga_series)}")
    print(f"Original TGA series index type: {type(tga_series.index[0])}")
    
    # Normalize TGA index (remove time component)
    tga_normalized = tga_series.copy()
    tga_normalized.index = tga_normalized.index.normalize()
    print(f"Normalized TGA index type: {type(tga_normalized.index[0])}")
    
    # Test reindex
    reindexed = tga_normalized.reindex(common_index)
    print(f"Reindexed TGA length: {len(reindexed)}")
    print(f"NaN count after reindex: {reindexed.isnull().sum()}")
    
    # Test forward fill
    filled = reindexed.ffill()
    print(f"NaN count after ffill: {filled.isnull().sum()}")
    
    if filled.isnull().sum() < len(filled):
        print(f"✓ TGA alignment successful!")
        print(f"Sample aligned TGA data:")
        print(filled.dropna().tail())
    else:
        print(f"✗ TGA alignment failed")
        
        # Debug: check for overlapping dates
        tga_dates = set(tga_normalized.index.date)
        business_dates = set(common_index.date)
        overlap = tga_dates.intersection(business_dates)
        print(f"TGA dates: {len(tga_dates)}")
        print(f"Business dates: {len(business_dates)}")
        print(f"Overlapping dates: {len(overlap)}")
        
        if len(overlap) > 0:
            print("Sample overlapping dates:")
            print(sorted(list(overlap))[:5])

if __name__ == "__main__":
    test_tga_alignment() 