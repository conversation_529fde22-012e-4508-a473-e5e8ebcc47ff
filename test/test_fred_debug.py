#!/usr/bin/env python3
"""
Debug script to test FRED API connectivity and data fetching
"""

import pandas as pd
from fredapi import <PERSON>
from datetime import datetime, timedelta
import traceback

# Use the same API key from the main script
FRED_API_KEY = "15eaa189642129aebde8c20e34a92725"

def test_fred_connection():
    """Test FRED API connection and data fetching"""
    print("Testing FRED API Connection...")
    
    try:
        # Initialize FRED API
        fred = Fred(api_key=FRED_API_KEY)
        print("✓ FRED API initialized successfully")
        
        # Test date range (90 days like the main script)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)
        
        print(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        # Test each series individually
        series_to_test = {
            "RRPONTSYD": "Overnight Reverse Repurchase Agreements",
            "WALCL": "All Federal Reserve Banks Total Assets",
            "DGS10": "10-Year Treasury Constant Maturity Rate"
        }
        
        all_data = {}
        
        for series_id, description in series_to_test.items():
            print(f"\nTesting {series_id}: {description}")
            try:
                data = fred.get_series(series_id, observation_start=start_date, observation_end=end_date)
                print(f"  Raw data length: {len(data)}")
                print(f"  Raw data type: {type(data)}")
                if len(data) > 0:
                    print(f"  Date range in data: {data.index.min()} to {data.index.max()}")
                    
                    # Drop NaN values
                    clean_data = data.dropna()
                    print(f"  Clean data length: {len(clean_data)}")
                    
                    if len(clean_data) > 0:
                        print(f"  Latest value: {clean_data.iloc[-1]}")
                        print(f"  First value: {clean_data.iloc[0]}")
                        print(f"  ✓ {series_id} fetched successfully")
                        all_data[series_id] = clean_data
                    else:
                        print(f"  ✗ {series_id} returned no valid data")
                else:
                    print(f"  ✗ {series_id} returned empty series")
                    
            except Exception as e:
                print(f"  ✗ Error fetching {series_id}: {e}")
                traceback.print_exc()
        
        # Test data alignment (like the main script does)
        print(f"\n=== Testing Data Alignment ===")
        if all_data:
            common_index = pd.date_range(start=start_date, end=end_date, freq="B")
            print(f"Common business day index length: {len(common_index)}")
            print(f"Common index range: {common_index[0]} to {common_index[-1]}")
            
            df = pd.DataFrame(index=common_index)
            
            for series_id, data in all_data.items():
                print(f"\nAligning {series_id}:")
                reindexed = data.reindex(common_index)
                print(f"  After reindex length: {len(reindexed)}")
                print(f"  NaN count after reindex: {reindexed.isnull().sum()}")
                
                filled = reindexed.ffill()
                print(f"  NaN count after ffill: {filled.isnull().sum()}")
                
                df[series_id] = filled
            
            print(f"\nCombined DataFrame:")
            print(f"  Shape: {df.shape}")
            print(f"  Missing values per column:\n{df.isnull().sum()}")
            
            # Test dropna like the main script
            essential_cols = ["RRPONTSYD", "WALCL", "DGS10"]
            before_dropna = len(df)
            df_clean = df.dropna(subset=essential_cols)
            after_dropna = len(df_clean)
            print(f"  Rows before dropna: {before_dropna}")
            print(f"  Rows after dropna: {after_dropna}")
            
            if after_dropna > 0:
                print(f"  ✓ Data alignment successful!")
                print(f"  Sample of aligned data:")
                print(df_clean.tail())
            else:
                print(f"  ✗ Data alignment failed - no overlapping data")
                
    except Exception as e:
        print(f"✗ Error initializing FRED API: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_fred_connection() 