#!/usr/bin/env python3
"""
Test script to verify RRP impact analysis fix
"""

import sys
sys.path.append('src')
from analytics.money_market_analyzer import MoneyMarketAnalyzer
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_rrp_impact():
    """Test RRP impact analysis"""
    print("=== TESTING RRP IMPACT ANALYSIS ===")
    
    analyzer = MoneyMarketAnalyzer()
    
    # Create test data with rising RRP
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
    
    print("\n1. Testing RISING RRP (should drain liquidity)...")
    test_data = pd.DataFrame({
        'fed_assets': np.random.normal(8000, 100, len(dates)),
        'rrp_amount': np.linspace(2000, 2500, len(dates)),  # Rising RRP
        'tga_balance': np.random.normal(500, 50, len(dates)),
        'treasury_10y_yield': np.random.normal(0.045, 0.005, len(dates))
    }, index=dates)
    
    analyzer.liquidity_df = test_data
    rrp_impact = analyzer._analyze_rrp_impact()
    print(f'RRP Impact (rising RRP): {rrp_impact}')
    
    print("\n2. Testing FALLING RRP (should add liquidity)...")
    test_data['rrp_amount'] = np.linspace(2500, 2000, len(dates))  # Falling RRP
    analyzer.liquidity_df = test_data
    rrp_impact = analyzer._analyze_rrp_impact()
    print(f'RRP Impact (falling RRP): {rrp_impact}')
    
    print("\n3. Testing STABLE RRP (should be neutral)...")
    test_data['rrp_amount'] = np.full(len(dates), 2250)  # Stable RRP
    analyzer.liquidity_df = test_data
    rrp_impact = analyzer._analyze_rrp_impact()
    print(f'RRP Impact (stable RRP): {rrp_impact}')
    
    print("\n✅ RRP impact analysis test complete!")

if __name__ == "__main__":
    test_rrp_impact() 