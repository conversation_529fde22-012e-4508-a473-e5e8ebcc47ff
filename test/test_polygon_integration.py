#!/usr/bin/env python3
"""
Test script to verify Polygon.io option data integration
"""

import sys
import os
sys.path.append('src')
from checklist_driver import PolygonDataFetcher

POLYGON_API_KEY = "********************************"

def test_polygon_integration():
    """Test Polygon.io option data fetching"""
    print("Testing Polygon.io Option Data Integration...")
    
    # Initialize fetcher
    polygon_fetcher = PolygonDataFetcher(api_key=POLYGON_API_KEY)
    
    # Test with SPX (S&P 500 index options - cash settled)
    ticker = "^GSPC"  # Will be converted to SPX
    
    try:
        print(f"\nTesting option chain fetch for {ticker}...")
        option_chain = polygon_fetcher.fetch_option_chain(ticker)
        
        if option_chain:
            print(f"✓ Option chain fetched successfully")
            print(f"  Current Price: {option_chain.get('current_price', 'N/A')}")
            print(f"  Calls: {len(option_chain.get('calls', []))}")
            print(f"  Puts: {len(option_chain.get('puts', []))}")
            
            # Show sample data
            calls = option_chain.get('calls', [])
            puts = option_chain.get('puts', [])
            
            if calls:
                print(f"  Sample Call Strike: {calls[0].get('strike', 'N/A')}")
                print(f"  Sample Call OI: {calls[0].get('openInterest', 'N/A')}")
                print(f"  Sample Call IV: {calls[0].get('impliedVolatility', 'N/A'):.3f}")
                
            if puts:
                print(f"  Sample Put Strike: {puts[0].get('strike', 'N/A')}")
                print(f"  Sample Put OI: {puts[0].get('openInterest', 'N/A')}")
                print(f"  Sample Put IV: {puts[0].get('impliedVolatility', 'N/A'):.3f}")
                
            return True
        else:
            print("✗ Option chain fetch returned empty data")
            return False
            
    except Exception as e:
        print(f"✗ Error testing Polygon integration: {e}")
        return False

if __name__ == "__main__":
    success = test_polygon_integration()
    
    if success:
        print("\n🎉 Polygon.io integration test passed!")
        print("You can now run the main script with real option data.")
    else:
        print("\n⚠️  Polygon.io integration test failed.")
        print("The script will fall back to simulated option data.")
        print("Check your API key and ensure you have an active Polygon subscription.") 