"""
Price Data Fetcher
Handles price data retrieval with fallback mechanisms.
"""

import os
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, List
from dotenv import load_dotenv

load_dotenv()


class PriceDataFetcher:
    """Fetches price data with multiple fallback mechanisms."""
    
    def __init__(self):
        self.cache = {}
        self.cache_expiry = timedelta(minutes=15)  # Cache for 15 minutes
    
    def get_current_price(self, ticker: str) -> Optional[float]:
        """Get current price with caching."""
        cache_key = f"current_{ticker}"
        now = datetime.now()
        
        # Check cache first
        if cache_key in self.cache:
            price, timestamp = self.cache[cache_key]
            if now - timestamp < self.cache_expiry:
                return price
        
        # Fetch new price
        try:
            # Convert ticker for yfinance
            yf_ticker = self._convert_ticker_for_yfinance(ticker)
            tkr = yf.Ticker(yf_ticker)
            data = tkr.history(period="1d")
            
            if not data.empty:
                price = float(data['Close'].iloc[-1])
                self.cache[cache_key] = (price, now)
                return price
            
            return None
            
        except Exception as e:
            print(f"Error fetching current price for {ticker}: {e}")
            return None
    
    def get_historical_prices(self, ticker: str, days: int = 252) -> pd.DataFrame:
        """Get historical price data."""
        try:
            # Convert ticker for yfinance
            yf_ticker = self._convert_ticker_for_yfinance(ticker)
            
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days + 50)  # Extra buffer
            
            tkr = yf.Ticker(yf_ticker)
            data = tkr.history(start=start_date, end=end_date, auto_adjust=False)
            
            if data.empty:
                print(f"No historical data found for {ticker}")
                return pd.DataFrame()
            
            # Ensure we have Adj Close
            if "Adj Close" not in data.columns:
                if "Close" in data.columns:
                    data["Adj Close"] = data["Close"]
                else:
                    print(f"Missing price data for {ticker}")
                    return pd.DataFrame()
            
            # Return the most recent 'days' of data
            return data.tail(days) if len(data) >= days else data
            
        except Exception as e:
            print(f"Error fetching historical prices for {ticker}: {e}")
            return pd.DataFrame()
    
    def _convert_ticker_for_yfinance(self, ticker: str) -> str:
        """Convert ticker symbol for yfinance."""
        # Handle common ticker conversions
        if ticker == 'SPX':
            return '^GSPC'
        elif ticker == 'NDX':
            return '^NDX'
        elif ticker.startswith('^'):
            return ticker
        else:
            return ticker
    
    def calculate_returns(self, prices: pd.Series) -> pd.Series:
        """Calculate daily returns from price series."""
        return prices.pct_change().dropna()
    
    def calculate_log_returns(self, prices: pd.Series) -> pd.Series:
        """Calculate log returns from price series."""
        return np.log(prices / prices.shift(1)).dropna() 