"""
Black-Scholes Option Pricing and Greeks Calculator
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, Any
import os
from dotenv import load_dotenv

load_dotenv()


class BlackScholesCalculator:
    """Black-Scholes option pricing and Greeks calculations."""
    
    def __init__(self, risk_free_rate: float = None):
        self.risk_free_rate = risk_free_rate or float(os.getenv('RISK_FREE_RATE', '0.045'))
    
    def call_price(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate Black-Scholes call option price."""
        if T <= 0:
            return max(S - K, 0)
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        call_price = S * stats.norm.cdf(d1) - K * np.exp(-self.risk_free_rate * T) * stats.norm.cdf(d2)
        return max(call_price, 0)

    def put_price(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate Black-Scholes put option price."""
        if T <= 0:
            return max(K - S, 0)
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        put_price = K * np.exp(-self.risk_free_rate * T) * stats.norm.cdf(-d2) - S * stats.norm.cdf(-d1)
        return max(put_price, 0)

    def gamma(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate option gamma (same for calls and puts)."""
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        gamma = stats.norm.pdf(d1) / (S * sigma * np.sqrt(T))
        return gamma

    def vega(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate option vega (same for calls and puts)."""
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        vega = S * stats.norm.pdf(d1) * np.sqrt(T) / 100  # Divided by 100 for 1% vol change
        return vega

    def delta_call(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate call option delta."""
        if T <= 0:
            return 1.0 if S > K else 0.0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        return stats.norm.cdf(d1)

    def delta_put(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate put option delta."""
        if T <= 0:
            return -1.0 if S < K else 0.0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        return stats.norm.cdf(d1) - 1

    def theta_call(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate call option theta."""
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        theta = (-(S * stats.norm.pdf(d1) * sigma) / (2 * np.sqrt(T)) 
                 - self.risk_free_rate * K * np.exp(-self.risk_free_rate * T) * stats.norm.cdf(d2)) / 365
        return theta

    def theta_put(self, S: float, K: float, T: float, sigma: float) -> float:
        """Calculate put option theta."""
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        theta = (-(S * stats.norm.pdf(d1) * sigma) / (2 * np.sqrt(T)) 
                 + self.risk_free_rate * K * np.exp(-self.risk_free_rate * T) * stats.norm.cdf(-d2)) / 365
        return theta

    def vanna(self, S: float, K: float, T: float, sigma: float) -> float:
        """
        Calculate vanna (dDelta/dVol or dVega/dSpot).
        Critical for understanding how delta hedging changes with volatility.
        """
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        vanna = -stats.norm.pdf(d1) * d2 / sigma / 100  # Divided by 100 for 1% vol change
        return vanna

    def charm_call(self, S: float, K: float, T: float, sigma: float) -> float:
        """
        Calculate charm for calls (dDelta/dTime).
        Shows how call delta changes over time - crucial for gamma scalping.
        """
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        charm = -stats.norm.pdf(d1) * (2 * self.risk_free_rate * T - d2 * sigma * np.sqrt(T)) / (2 * T * sigma * np.sqrt(T)) / 365
        return charm

    def charm_put(self, S: float, K: float, T: float, sigma: float) -> float:
        """
        Calculate charm for puts (dDelta/dTime).
        Shows how put delta changes over time.
        """
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        charm = stats.norm.pdf(d1) * (2 * self.risk_free_rate * T - d2 * sigma * np.sqrt(T)) / (2 * T * sigma * np.sqrt(T)) / 365
        return charm

    def volga(self, S: float, K: float, T: float, sigma: float) -> float:
        """
        Calculate volga/vomma (dVega/dVol).
        Second-order volatility risk - how vega changes with volatility.
        """
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        volga = S * stats.norm.pdf(d1) * np.sqrt(T) * d1 * d2 / sigma / 10000  # Scaled for practical use
        return volga

    def color(self, S: float, K: float, T: float, sigma: float) -> float:
        """
        Calculate color (dGamma/dTime).
        Shows how gamma changes over time - critical for understanding gamma decay.
        """
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        color = -stats.norm.pdf(d1) / (2 * S * T * sigma * np.sqrt(T)) * (
            2 * self.risk_free_rate * T + 1 + d1 * (2 * self.risk_free_rate * T - d2 * sigma * np.sqrt(T))
        ) / 365
        return color

    def speed(self, S: float, K: float, T: float, sigma: float) -> float:
        """
        Calculate speed (dGamma/dSpot).
        Third-order Greek showing how gamma changes with spot price.
        """
        if T <= 0:
            return 0
        
        d1 = (np.log(S / K) + (self.risk_free_rate + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        
        speed = -stats.norm.pdf(d1) / (S**2 * sigma * np.sqrt(T)) * (d1 / (sigma * np.sqrt(T)) + 1)
        return speed

    def calculate_all_greeks(self, S: float, K: float, T: float, sigma: float, option_type: str) -> Dict[str, float]:
        """Calculate all Greeks for an option including advanced second-order Greeks."""
        greeks = {
            'gamma': self.gamma(S, K, T, sigma),
            'vega': self.vega(S, K, T, sigma),
            'vanna': self.vanna(S, K, T, sigma),
            'volga': self.volga(S, K, T, sigma),
            'color': self.color(S, K, T, sigma),
            'speed': self.speed(S, K, T, sigma)
        }
        
        if option_type.lower() == 'call':
            greeks.update({
                'delta': self.delta_call(S, K, T, sigma),
                'theta': self.theta_call(S, K, T, sigma),
                'charm': self.charm_call(S, K, T, sigma)
            })
        else:  # put
            greeks.update({
                'delta': self.delta_put(S, K, T, sigma),
                'theta': self.theta_put(S, K, T, sigma),
                'charm': self.charm_put(S, K, T, sigma)
            })
        
        return greeks

    def calculate_dealer_positioning_metrics(self, S: float, K: float, T: float, sigma: float, 
                                           open_interest: float, option_type: str) -> Dict[str, float]:
        """
        Calculate dealer positioning metrics for options flow analysis.
        Assumes dealers are short options (long gamma/vega exposure for market).
        """
        greeks = self.calculate_all_greeks(S, K, T, sigma, option_type)
        
        # Contract multiplier (100 shares per contract)
        multiplier = 100
        
        # Dealer exposure (negative of customer position)
        dealer_metrics = {
            'dealer_delta_exposure': -greeks['delta'] * open_interest * multiplier,
            'dealer_gamma_exposure': -greeks['gamma'] * open_interest * multiplier,
            'dealer_vega_exposure': -greeks['vega'] * open_interest * multiplier,
            'dealer_vanna_exposure': -greeks['vanna'] * open_interest * multiplier,
            'dealer_charm_exposure': -greeks['charm'] * open_interest * multiplier,
            'dealer_volga_exposure': -greeks['volga'] * open_interest * multiplier,
            'dealer_color_exposure': -greeks['color'] * open_interest * multiplier,
            'moneyness': S / K,
            'time_to_expiry': T,
            'implied_volatility': sigma
        }
        
        return dealer_metrics

    def implied_volatility(self, market_price: float, S: float, K: float, T: float, option_type: str, 
                          max_iterations: int = 100, tolerance: float = 1e-6) -> float:
        """Calculate implied volatility using Newton-Raphson method."""
        if T <= 0 or market_price <= 0:
            return 0.20  # Default 20% volatility
        
        # Initial guess
        vol = 0.20
        
        for i in range(max_iterations):
            if option_type.lower() == 'call':
                price = self.call_price(S, K, T, vol)
            else:
                price = self.put_price(S, K, T, vol)
            
            # Calculate vega for Newton-Raphson
            vega_val = self.vega(S, K, T, vol) * 100  # Convert back from percentage
            
            if abs(vega_val) < tolerance:
                break
                
            # Newton-Raphson update
            price_diff = price - market_price
            if abs(price_diff) < tolerance:
                break
                
            vol = vol - price_diff / vega_val
            
            # Keep volatility in reasonable bounds
            vol = max(0.001, min(vol, 5.0))
        
        return vol 