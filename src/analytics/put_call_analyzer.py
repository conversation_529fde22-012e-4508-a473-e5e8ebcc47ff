"""
Put/Call Ratio Analyzer
Advanced analysis of put/call ratios using volume, open interest, and Greek-weighted metrics
to understand market sentiment, positioning, and buying pressure.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from .black_scholes import BlackScholesCalculator


class PutCallAnalyzer:
    """Advanced put/call ratio analysis for market sentiment and positioning."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        self.bs_calculator = BlackScholesCalculator()
        self.option_chain = None
        self.pc_analysis = None
        
    def set_option_chain(self, option_chain: Dict[str, Any]):
        """Set option chain data."""
        self.option_chain = option_chain
        
    def analyze_put_call_dynamics(self, moneyness_ranges: Dict[str, Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        Comprehensive put/call ratio analysis across different moneyness ranges.
        
        Args:
            moneyness_ranges: Dictionary of range names to (min_moneyness, max_moneyness) tuples
            
        Returns:
            Comprehensive put/call analysis
        """
        if not self.option_chain:
            return {"error": "No option chain data available"}
            
        if moneyness_ranges is None:
            moneyness_ranges = {
                "DOTM": (0.85, 0.95),    # Deep OTM puts / Far OTM calls
                "OTM": (0.95, 0.98),     # OTM puts / OTM calls  
                "ATM": (0.98, 1.02),     # At-the-money
                "ITM": (1.02, 1.05),     # ITM puts / OTM calls
                "DITM": (1.05, 1.15)     # Deep ITM puts / Far OTM calls
            }
            
        current_price = self.option_chain.get("current_price")
        calls = self.option_chain.get("calls", [])
        puts = self.option_chain.get("puts", [])
        
        if not current_price or not calls or not puts:
            return {"error": "Insufficient option chain data"}
            
        # Convert to DataFrames and calculate moneyness
        calls_df = pd.DataFrame(calls)
        puts_df = pd.DataFrame(puts)
        
        calls_df['moneyness'] = calls_df['strike'] / current_price
        puts_df['moneyness'] = puts_df['strike'] / current_price
        
        # Calculate Greeks for all options
        calls_df = self._add_greeks(calls_df, current_price, 'call')
        puts_df = self._add_greeks(puts_df, current_price, 'put')
        
        analysis = {
            "current_price": current_price,
            "analysis_timestamp": datetime.now(),
            "overall_ratios": {},
            "moneyness_analysis": {},
            "flow_analysis": {},
            "sentiment_signals": {}
        }
        
        # Overall ratios
        analysis["overall_ratios"] = self._calculate_overall_ratios(calls_df, puts_df)
        
        # Moneyness-based analysis
        for range_name, (min_money, max_money) in moneyness_ranges.items():
            analysis["moneyness_analysis"][range_name] = self._analyze_moneyness_range(
                calls_df, puts_df, min_money, max_money, current_price
            )
        
        # Flow analysis
        analysis["flow_analysis"] = self._analyze_options_flow(calls_df, puts_df, current_price)
        
        # Sentiment signals
        analysis["sentiment_signals"] = self._generate_sentiment_signals(analysis)
        
        self.pc_analysis = analysis
        return analysis
    
    def _add_greeks(self, df: pd.DataFrame, current_price: float, option_type: str) -> pd.DataFrame:
        """Add Greeks to options DataFrame."""
        df = df.copy()
        
        # Use a standard time to expiry for analysis (can be enhanced with real expiry dates)
        tte = 30/365  # 30 days
        
        for idx, row in df.iterrows():
            strike = row['strike']
            iv = row['impliedVolatility']
            
            greeks = self.bs_calculator.calculate_all_greeks(
                current_price, strike, tte, iv, option_type
            )
            
            for greek, value in greeks.items():
                df.at[idx, greek] = value
                
        return df
    
    def _calculate_overall_ratios(self, calls_df: pd.DataFrame, puts_df: pd.DataFrame) -> Dict[str, float]:
        """Calculate overall put/call ratios."""
        
        # Basic volume and OI ratios
        total_call_volume = calls_df['volume'].sum()
        total_put_volume = puts_df['volume'].sum()
        total_call_oi = calls_df['openInterest'].sum()
        total_put_oi = puts_df['openInterest'].sum()
        
        pc_volume_ratio = total_put_volume / total_call_volume if total_call_volume > 0 else 0
        pc_oi_ratio = total_put_oi / total_call_oi if total_call_oi > 0 else 0
        
        # Greek-weighted ratios
        total_call_gamma = (calls_df['gamma'] * calls_df['openInterest']).sum()
        total_put_gamma = (puts_df['gamma'] * puts_df['openInterest']).sum()
        pc_gamma_ratio = total_put_gamma / total_call_gamma if total_call_gamma > 0 else 0
        
        total_call_vega = (calls_df['vega'] * calls_df['openInterest']).sum()
        total_put_vega = (puts_df['vega'] * puts_df['openInterest']).sum()
        pc_vega_ratio = total_put_vega / total_call_vega if total_call_vega > 0 else 0
        
        # Delta-weighted ratios (absolute values)
        total_call_delta = (abs(calls_df['delta']) * calls_df['openInterest']).sum()
        total_put_delta = (abs(puts_df['delta']) * puts_df['openInterest']).sum()
        pc_delta_ratio = total_put_delta / total_call_delta if total_call_delta > 0 else 0
        
        # Vanna-weighted ratios
        total_call_vanna = (abs(calls_df['vanna']) * calls_df['openInterest']).sum()
        total_put_vanna = (abs(puts_df['vanna']) * puts_df['openInterest']).sum()
        pc_vanna_ratio = total_put_vanna / total_call_vanna if total_call_vanna > 0 else 0
        
        return {
            'pc_volume_ratio': pc_volume_ratio,
            'pc_oi_ratio': pc_oi_ratio,
            'pc_gamma_ratio': pc_gamma_ratio,
            'pc_vega_ratio': pc_vega_ratio,
            'pc_delta_ratio': pc_delta_ratio,
            'pc_vanna_ratio': pc_vanna_ratio,
            'total_call_volume': total_call_volume,
            'total_put_volume': total_put_volume,
            'total_call_oi': total_call_oi,
            'total_put_oi': total_put_oi
        }
    
    def _analyze_moneyness_range(self, calls_df: pd.DataFrame, puts_df: pd.DataFrame, 
                               min_money: float, max_money: float, current_price: float) -> Dict[str, Any]:
        """Analyze put/call dynamics for a specific moneyness range."""
        
        # Filter by moneyness range
        calls_range = calls_df[
            (calls_df['moneyness'] >= min_money) & 
            (calls_df['moneyness'] <= max_money)
        ]
        puts_range = puts_df[
            (puts_df['moneyness'] >= min_money) & 
            (puts_df['moneyness'] <= max_money)
        ]
        
        if calls_range.empty and puts_range.empty:
            return {"error": f"No options in moneyness range {min_money:.2f}-{max_money:.2f}"}
        
        # Volume analysis
        call_volume = calls_range['volume'].sum()
        put_volume = puts_range['volume'].sum()
        volume_ratio = put_volume / call_volume if call_volume > 0 else float('inf')
        
        # Open interest analysis
        call_oi = calls_range['openInterest'].sum()
        put_oi = puts_range['openInterest'].sum()
        oi_ratio = put_oi / call_oi if call_oi > 0 else float('inf')
        
        # Greek exposures
        call_gamma_exp = (calls_range['gamma'] * calls_range['openInterest']).sum()
        put_gamma_exp = (puts_range['gamma'] * puts_range['openInterest']).sum()
        net_gamma_exp = call_gamma_exp - put_gamma_exp  # Market maker perspective
        
        call_vanna_exp = (calls_range['vanna'] * calls_range['openInterest']).sum()
        put_vanna_exp = (puts_range['vanna'] * puts_range['openInterest']).sum()
        net_vanna_exp = call_vanna_exp + put_vanna_exp
        
        # Buying pressure indicators
        avg_call_iv = calls_range['impliedVolatility'].mean() if not calls_range.empty else 0
        avg_put_iv = puts_range['impliedVolatility'].mean() if not puts_range.empty else 0
        iv_skew = avg_put_iv - avg_call_iv
        
        # Determine dominant activity
        if volume_ratio > 1.5:
            dominant_activity = "PUT_HEAVY"
        elif volume_ratio < 0.67:
            dominant_activity = "CALL_HEAVY"
        else:
            dominant_activity = "BALANCED"
        
        return {
            'moneyness_range': (min_money, max_money),
            'call_volume': call_volume,
            'put_volume': put_volume,
            'volume_ratio': volume_ratio,
            'call_oi': call_oi,
            'put_oi': put_oi,
            'oi_ratio': oi_ratio,
            'net_gamma_exposure': net_gamma_exp,
            'net_vanna_exposure': net_vanna_exp,
            'avg_call_iv': avg_call_iv,
            'avg_put_iv': avg_put_iv,
            'iv_skew': iv_skew,
            'dominant_activity': dominant_activity
        }
    
    def _analyze_options_flow(self, calls_df: pd.DataFrame, puts_df: pd.DataFrame, 
                            current_price: float) -> Dict[str, Any]:
        """Analyze options flow patterns for buying pressure signals."""
        
        # Separate by position relative to current price
        otm_calls = calls_df[calls_df['strike'] > current_price]  # Bullish bets
        otm_puts = puts_df[puts_df['strike'] < current_price]    # Bearish bets
        itm_calls = calls_df[calls_df['strike'] < current_price]  # Protective/covered
        itm_puts = puts_df[puts_df['strike'] > current_price]    # Protective
        
        # OTM activity (directional bets)
        otm_call_volume = otm_calls['volume'].sum()
        otm_put_volume = otm_puts['volume'].sum()
        otm_ratio = otm_put_volume / otm_call_volume if otm_call_volume > 0 else float('inf')
        
        # ITM activity (hedging/protection)
        itm_call_volume = itm_calls['volume'].sum()
        itm_put_volume = itm_puts['volume'].sum()
        itm_ratio = itm_put_volume / itm_call_volume if itm_call_volume > 0 else float('inf')
        
        # Unusual activity detection
        # High volume relative to open interest suggests new positioning
        calls_df['volume_oi_ratio'] = calls_df['volume'] / calls_df['openInterest'].replace(0, 1)
        puts_df['volume_oi_ratio'] = puts_df['volume'] / puts_df['openInterest'].replace(0, 1)
        
        unusual_call_activity = calls_df[calls_df['volume_oi_ratio'] > 0.5]  # >50% of OI traded
        unusual_put_activity = puts_df[puts_df['volume_oi_ratio'] > 0.5]
        
        # Vanna flow (vol/spot correlation trades)
        high_vanna_calls = calls_df[abs(calls_df['vanna']) > calls_df['vanna'].quantile(0.8)]
        high_vanna_puts = puts_df[abs(puts_df['vanna']) > puts_df['vanna'].quantile(0.8)]
        
        vanna_call_volume = (high_vanna_calls['volume'] * high_vanna_calls['vanna']).sum()
        vanna_put_volume = (high_vanna_puts['volume'] * high_vanna_puts['vanna']).sum()
        net_vanna_flow = vanna_call_volume + vanna_put_volume
        
        return {
            'otm_call_volume': otm_call_volume,
            'otm_put_volume': otm_put_volume,
            'otm_ratio': otm_ratio,
            'itm_call_volume': itm_call_volume,
            'itm_put_volume': itm_put_volume,
            'itm_ratio': itm_ratio,
            'unusual_call_strikes': unusual_call_activity['strike'].tolist(),
            'unusual_put_strikes': unusual_put_activity['strike'].tolist(),
            'net_vanna_flow': net_vanna_flow,
            'directional_bias': 'BEARISH' if otm_ratio > 1.2 else 'BULLISH' if otm_ratio < 0.8 else 'NEUTRAL'
        }
    
    def _generate_sentiment_signals(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate sentiment signals based on put/call analysis."""
        
        signals = {
            'primary_sentiment': 'NEUTRAL',
            'confidence': 'LOW',
            'key_indicators': [],
            'contrarian_signals': [],
            'flow_pressure': 'BALANCED'
        }
        
        overall = analysis.get('overall_ratios', {})
        flow = analysis.get('flow_analysis', {})
        
        if not overall or not flow:
            return signals
        
        # Sentiment scoring
        bearish_score = 0
        bullish_score = 0
        
        # Volume-based signals
        pc_volume = overall.get('pc_volume_ratio', 1.0)
        if pc_volume > 1.2:  # High put volume
            bearish_score += 2
            signals['key_indicators'].append(f"High P/C volume ratio: {pc_volume:.2f}")
        elif pc_volume < 0.8:  # High call volume
            bullish_score += 2
            signals['key_indicators'].append(f"Low P/C volume ratio: {pc_volume:.2f}")
        
        # OTM directional flow
        otm_ratio = flow.get('otm_ratio', 1.0)
        if otm_ratio > 1.5:  # Heavy OTM put buying
            bearish_score += 2
            signals['key_indicators'].append(f"Heavy OTM put buying: {otm_ratio:.2f}")
        elif otm_ratio < 0.67:  # Heavy OTM call buying
            bullish_score += 2
            signals['key_indicators'].append(f"Heavy OTM call buying: {otm_ratio:.2f}")
        
        # Greek-weighted signals
        pc_gamma = overall.get('pc_gamma_ratio', 1.0)
        if pc_gamma > 1.3:
            bearish_score += 1
            signals['key_indicators'].append("High gamma-weighted P/C ratio")
        elif pc_gamma < 0.7:
            bullish_score += 1
            signals['key_indicators'].append("Low gamma-weighted P/C ratio")
        
        # Vanna flow signals
        net_vanna = flow.get('net_vanna_flow', 0)
        if net_vanna > 0:
            bullish_score += 1
            signals['key_indicators'].append("Positive vanna flow (vol/spot positive correlation)")
        elif net_vanna < 0:
            bearish_score += 1
            signals['key_indicators'].append("Negative vanna flow (vol/spot negative correlation)")
        
        # Contrarian signals (extreme readings)
        if pc_volume > 2.0:  # Extreme put buying
            signals['contrarian_signals'].append("Extreme put buying - potential contrarian bullish")
        elif pc_volume < 0.5:  # Extreme call buying
            signals['contrarian_signals'].append("Extreme call buying - potential contrarian bearish")
        
        # Determine primary sentiment
        if bearish_score > bullish_score + 1:
            signals['primary_sentiment'] = 'BEARISH'
            signals['confidence'] = 'HIGH' if bearish_score - bullish_score >= 3 else 'MEDIUM'
        elif bullish_score > bearish_score + 1:
            signals['primary_sentiment'] = 'BULLISH'
            signals['confidence'] = 'HIGH' if bullish_score - bearish_score >= 3 else 'MEDIUM'
        else:
            signals['primary_sentiment'] = 'NEUTRAL'
            signals['confidence'] = 'LOW'
        
        # Flow pressure
        if otm_ratio > 1.3:
            signals['flow_pressure'] = 'BEARISH_PRESSURE'
        elif otm_ratio < 0.7:
            signals['flow_pressure'] = 'BULLISH_PRESSURE'
        else:
            signals['flow_pressure'] = 'BALANCED'
        
        return signals
    
    def plot_put_call_analysis(self, save_path: str = None) -> Optional[str]:
        """Plot comprehensive put/call analysis."""
        if not self.pc_analysis:
            return None
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Put/Call Ratio Analysis', fontsize=16, fontweight='bold')
        
        # Overall ratios
        ax1 = axes[0, 0]
        ratios = self.pc_analysis['overall_ratios']
        ratio_names = ['Volume', 'OI', 'Gamma', 'Vega', 'Delta', 'Vanna']
        ratio_values = [
            ratios['pc_volume_ratio'], ratios['pc_oi_ratio'], 
            ratios['pc_gamma_ratio'], ratios['pc_vega_ratio'],
            ratios['pc_delta_ratio'], ratios['pc_vanna_ratio']
        ]
        
        bars = ax1.bar(ratio_names, ratio_values, color=['red' if v > 1 else 'green' for v in ratio_values])
        ax1.axhline(y=1.0, color='black', linestyle='--', alpha=0.7)
        ax1.set_title('Put/Call Ratios')
        ax1.set_ylabel('P/C Ratio')
        ax1.tick_params(axis='x', rotation=45)
        
        # Moneyness analysis
        ax2 = axes[0, 1]
        moneyness_data = self.pc_analysis['moneyness_analysis']
        ranges = list(moneyness_data.keys())
        volume_ratios = [data.get('volume_ratio', 0) for data in moneyness_data.values()]
        
        ax2.bar(ranges, volume_ratios, color=['red' if v > 1 else 'green' for v in volume_ratios])
        ax2.axhline(y=1.0, color='black', linestyle='--', alpha=0.7)
        ax2.set_title('P/C Volume Ratio by Moneyness')
        ax2.set_ylabel('P/C Volume Ratio')
        
        # Flow analysis
        ax3 = axes[1, 0]
        flow = self.pc_analysis['flow_analysis']
        flow_categories = ['OTM', 'ITM']
        flow_ratios = [flow.get('otm_ratio', 0), flow.get('itm_ratio', 0)]
        
        ax3.bar(flow_categories, flow_ratios, color=['red' if v > 1 else 'green' for v in flow_ratios])
        ax3.axhline(y=1.0, color='black', linestyle='--', alpha=0.7)
        ax3.set_title('Directional vs Hedging Flow')
        ax3.set_ylabel('P/C Ratio')
        
        # Sentiment summary
        ax4 = axes[1, 1]
        sentiment = self.pc_analysis['sentiment_signals']
        sentiment_text = f"Primary Sentiment: {sentiment['primary_sentiment']}\n"
        sentiment_text += f"Confidence: {sentiment['confidence']}\n"
        sentiment_text += f"Flow Pressure: {sentiment['flow_pressure']}\n\n"
        sentiment_text += "Key Indicators:\n"
        for indicator in sentiment['key_indicators'][:3]:  # Top 3
            sentiment_text += f"• {indicator}\n"
        
        ax4.text(0.05, 0.95, sentiment_text, transform=ax4.transAxes, 
                verticalalignment='top', fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')
        ax4.set_title('Sentiment Summary')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            return save_path
        else:
            plt.show()
            return None
    
    def generate_pc_report(self) -> str:
        """Generate a comprehensive put/call analysis report."""
        if not self.pc_analysis:
            return "No put/call analysis available. Run analyze_put_call_dynamics() first."
        
        report = []
        report.append("=" * 80)
        report.append("PUT/CALL RATIO ANALYSIS REPORT")
        report.append("=" * 80)
        report.append(f"Analysis Time: {self.pc_analysis['analysis_timestamp']}")
        report.append(f"Current Price: ${self.pc_analysis['current_price']:.2f}")
        report.append("")
        
        # Sentiment signals
        sentiment = self.pc_analysis['sentiment_signals']
        report.append("SENTIMENT ANALYSIS:")
        report.append(f"  Primary Sentiment: {sentiment['primary_sentiment']}")
        report.append(f"  Confidence: {sentiment['confidence']}")
        report.append(f"  Flow Pressure: {sentiment['flow_pressure']}")
        report.append("")
        
        # Key indicators
        if sentiment['key_indicators']:
            report.append("KEY INDICATORS:")
            for indicator in sentiment['key_indicators']:
                report.append(f"  • {indicator}")
            report.append("")
        
        # Contrarian signals
        if sentiment['contrarian_signals']:
            report.append("CONTRARIAN SIGNALS:")
            for signal in sentiment['contrarian_signals']:
                report.append(f"  ⚠ {signal}")
            report.append("")
        
        # Overall ratios
        overall = self.pc_analysis['overall_ratios']
        report.append("OVERALL PUT/CALL RATIOS:")
        report.append(f"  Volume Ratio: {overall['pc_volume_ratio']:.2f}")
        report.append(f"  Open Interest Ratio: {overall['pc_oi_ratio']:.2f}")
        report.append(f"  Gamma-Weighted Ratio: {overall['pc_gamma_ratio']:.2f}")
        report.append(f"  Vega-Weighted Ratio: {overall['pc_vega_ratio']:.2f}")
        report.append(f"  Vanna-Weighted Ratio: {overall['pc_vanna_ratio']:.2f}")
        report.append("")
        
        # Flow analysis
        flow = self.pc_analysis['flow_analysis']
        report.append("FLOW ANALYSIS:")
        report.append(f"  OTM P/C Ratio: {flow['otm_ratio']:.2f} ({flow['directional_bias']})")
        report.append(f"  ITM P/C Ratio: {flow['itm_ratio']:.2f}")
        report.append(f"  Net Vanna Flow: {flow['net_vanna_flow']:,.0f}")
        report.append("")
        
        # Moneyness breakdown
        report.append("MONEYNESS ANALYSIS:")
        for range_name, data in self.pc_analysis['moneyness_analysis'].items():
            if 'error' not in data:
                report.append(f"  {range_name}: Volume P/C = {data['volume_ratio']:.2f}, "
                            f"Activity = {data['dominant_activity']}")
        
        return "\n".join(report) 