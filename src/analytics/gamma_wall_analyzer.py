import matplotlib.pyplot as plt

# Calculate cumulative gamma
df['cumulative_gamma'] = df['gamma'].cumsum()

# Calculate strike range based on 3 standard deviations
price_std_estimate = current_price * 0.15  # Assume ~15% annual volatility as baseline
range_width = 3 * price_std_estimate  # 3 standard deviations

# Set minimum and maximum bounds to ensure reasonable chart range
min_range_width = current_price * 0.30  # At least 30% range
max_range_width = current_price * 0.90  # At most 90% range
range_width = max(min_range_width, min(range_width, max_range_width))

x_min = current_price - range_width
x_max = current_price + range_width

# Ensure we don't go below zero for the minimum
x_min = max(0, x_min)

# Filter data to the visible range
visible_data = df[
    (df['strike'] >= x_min) & 
    (df['strike'] <= x_max)
]

# Plot cumulative gamma
plt.figure(figsize=(12, 6))
plt.plot(visible_data['strike'], visible_data['cumulative_gamma'], 'b-', linewidth=2)
plt.axvline(current_price, color='black', linestyle='--', alpha=0.8, label='Current Price')
plt.title('Cumulative Gamma Profile')
plt.xlabel('Strike Price')
plt.ylabel('Cumulative Gamma')
plt.xlim(x_min, x_max)  # Set horizontal scale to 3 std dev range around current price
plt.legend()
plt.grid(True, alpha=0.3) 