"""
Option Analyzer
Handles option chain analysis, Greeks calculations, and strike level identification.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from .black_scholes import BlackScholesCalculator


class OptionAnalyzer:
    """Analyzes option chains and calculates Greeks."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        self.bs_calculator = BlackScholesCalculator()
        self.option_chain = None
        self.strike_levels = None
        
    def set_option_chain(self, option_chain: Dict[str, Any]):
        """Set option chain data."""
        self.option_chain = option_chain
        
    def identify_strike_levels(self, time_to_expiry: float = 7/365.25) -> Optional[Dict[str, Any]]:
        """Identify important strike levels based on option data with Greek analysis."""
        if not self.option_chain:
            print("No option chain data available")
            return None
            
        current_price = self.option_chain.get("current_price")
        calls = self.option_chain.get("calls", [])
        puts = self.option_chain.get("puts", [])
        
        if not calls or not puts or not current_price:
            print("Insufficient option chain data")
            return None
            
        calls_df = pd.DataFrame(calls)
        puts_df = pd.DataFrame(puts)
        
        # Calculate Greeks for all options
        calls_df = self._calculate_greeks(calls_df, current_price, time_to_expiry, "call")
        puts_df = self._calculate_greeks(puts_df, current_price, time_to_expiry, "put")
        
        # Calculate position-weighted Greeks (multiply by 100 for contract size)
        calls_df["gamma_exposure"] = calls_df["openInterest"] * calls_df["gamma"] * 100
        calls_df["vega_exposure"] = calls_df["openInterest"] * calls_df["vega"] * 100
        puts_df["gamma_exposure"] = puts_df["openInterest"] * puts_df["gamma"] * 100
        puts_df["vega_exposure"] = puts_df["openInterest"] * puts_df["vega"] * 100
        
        # Identify walls (top 3 OI strikes within +/- 10% of current price)
        # CRITICAL: Call walls are ABOVE current price, Put walls are BELOW current price
        price_range = (current_price * 0.9, current_price * 1.1)
        calls_nearby = calls_df[
            (calls_df["strike"] > current_price) & 
            (calls_df["strike"] <= price_range[1])
        ]
        puts_nearby = puts_df[
            (puts_df["strike"] < current_price) & 
            (puts_df["strike"] >= price_range[0])
        ]
        
        call_walls = calls_nearby.nlargest(3, "openInterest").to_dict("records") if not calls_nearby.empty else []
        put_walls = puts_nearby.nlargest(3, "openInterest").to_dict("records") if not puts_nearby.empty else []
        
        # Calculate net Greek exposures (calls positive, puts NEGATIVE for market makers)
        net_gamma_exposure = calls_df["gamma_exposure"].sum() - puts_df["gamma_exposure"].sum()
        net_vega_exposure = calls_df["vega_exposure"].sum() + puts_df["vega_exposure"].sum()  # Both contribute positively to vega
        
        # Calculate Greek concentrations at ATM (within 2% of current price)
        atm_range = (current_price * 0.98, current_price * 1.02)
        atm_calls = calls_df[
            (calls_df["strike"] >= atm_range[0]) & 
            (calls_df["strike"] <= atm_range[1])
        ]
        atm_puts = puts_df[
            (puts_df["strike"] >= atm_range[0]) & 
            (puts_df["strike"] <= atm_range[1])
        ]
        
        atm_gamma = atm_calls["gamma_exposure"].sum() - atm_puts["gamma_exposure"].sum()
        atm_vega = atm_calls["vega_exposure"].sum() + atm_puts["vega_exposure"].sum()
        
        # Determine gamma and vega trends
        gamma_exposure_sign = "POSITIVE" if net_gamma_exposure > 0 else "NEGATIVE" if net_gamma_exposure < 0 else "NEUTRAL"
        
        # Vega trend based on concentration and IV levels
        avg_iv = (calls_df["impliedVolatility"].mean() + puts_df["impliedVolatility"].mean()) / 2
        if avg_iv > 0.25 and net_vega_exposure > calls_df["vega_exposure"].quantile(0.75):
            vega_trend = "HIGH_VEGA_RISK"
        elif avg_iv < 0.15 and net_vega_exposure < calls_df["vega_exposure"].quantile(0.25):
            vega_trend = "LOW_VEGA_RISK"
        else:
            vega_trend = "MODERATE_VEGA_RISK"

        # Gamma trend based on concentration and price proximity
        if abs(atm_gamma) > abs(net_gamma_exposure) * 0.3:  # ATM gamma is >30% of total
            if net_gamma_exposure > 0:
                gamma_trend = "POSITIVE_GAMMA_WALL"  # Price likely to be sticky
            else:
                gamma_trend = "NEGATIVE_GAMMA_WALL"  # Price likely to be volatile
        else:
            gamma_trend = "DISTRIBUTED_GAMMA"
            
        self.strike_levels = {
            "current_price": current_price,
            "call_walls": call_walls,
            "put_walls": put_walls,
            "net_gamma": net_gamma_exposure,
            "net_vega": net_vega_exposure,
            "atm_gamma": atm_gamma,
            "atm_vega": atm_vega,
            "gamma_exposure": gamma_exposure_sign,
            "gamma_trend": gamma_trend,
            "vega_trend": vega_trend,
            "avg_implied_vol": avg_iv,
            "data_source": self.option_chain.get("data_source", "Unknown"),
            "greeks_summary": {
                "total_call_gamma": calls_df["gamma_exposure"].sum(),
                "total_put_gamma": puts_df["gamma_exposure"].sum(),
                "total_call_vega": calls_df["vega_exposure"].sum(),
                "total_put_vega": puts_df["vega_exposure"].sum(),
                "max_gamma_strike": calls_df.loc[calls_df["gamma_exposure"].idxmax(), "strike"] if not calls_df.empty else None,
                "max_vega_strike": calls_df.loc[calls_df["vega_exposure"].idxmax(), "strike"] if not calls_df.empty else None
            }
        }
        
        return self.strike_levels
        
    def _calculate_greeks(self, df: pd.DataFrame, current_price: float, time_to_expiry: float, option_type: str) -> pd.DataFrame:
        """Calculate Greeks for option dataframe."""
        df = df.copy()
        
        for idx, row in df.iterrows():
            strike = row["strike"]
            iv = row["impliedVolatility"]
            
            greeks = self.bs_calculator.calculate_all_greeks(
                current_price, strike, time_to_expiry, iv, option_type
            )
            
            for greek, value in greeks.items():
                df.at[idx, greek] = value
                
        return df
    
    def _calculate_gamma(self, S, K, T, r, sigma):
        """Calculate gamma using Black-Scholes formula."""
        return self.bs_calculator.calculate_gamma(S, K, T, r, sigma)
    
    def _calculate_vega(self, S, K, T, r, sigma):
        """Calculate vega using Black-Scholes formula."""
        return self.bs_calculator.calculate_vega(S, K, T, r, sigma)
    
    def _calculate_delta_call(self, S, K, T, r, sigma):
        """Calculate call delta using Black-Scholes formula."""
        return self.bs_calculator.calculate_delta(S, K, T, r, sigma, "call")
    
    def _calculate_delta_put(self, S, K, T, r, sigma):
        """Calculate put delta using Black-Scholes formula."""
        return self.bs_calculator.calculate_delta(S, K, T, r, sigma, "put")
        
    def generate_option_signals(self, money_market_bias: Dict[str, Any]) -> Dict[str, Any]:
        """Generate option signals based on strike levels and money market bias."""
        if self.strike_levels is None:
            if self.identify_strike_levels() is None:
                print("Cannot generate signals, strike level analysis failed.")
                return {"error": "Strike level analysis failed", "signal": "UNKNOWN", "confidence": "NONE", "rationale": "Strike level analysis failed"}

        # Get data source from strike levels analysis
        data_source = self.strike_levels.get("data_source", "Unknown Data")
        print(f"Generating option trading signals... (Using {data_source})")

        current_price = self.strike_levels["current_price"]
        if pd.isna(current_price):
             print("Cannot generate signals without current price.")
             return {"error": "Missing current price", "signal": "UNKNOWN", "confidence": "NONE", "rationale": "Missing current price"}

        # Check proximity to walls
        approaching_call_wall, nearest_call_wall_strike = False, None
        call_walls = self.strike_levels.get("call_walls", [])
        if call_walls:
            # Find nearest call wall *above* current price
            walls_above = [w for w in call_walls if w["strike"] > current_price]
            if walls_above:
                nearest_call = min(walls_above, key=lambda x: x["strike"])
                if 0 < (nearest_call["strike"] - current_price) / current_price < 0.02: # Within 2%
                    approaching_call_wall = True
                    nearest_call_wall_strike = nearest_call["strike"]

        approaching_put_wall, nearest_put_wall_strike = False, None
        put_walls = self.strike_levels.get("put_walls", [])
        if put_walls:
            # Find nearest put wall *below* current price
            walls_below = [w for w in put_walls if w["strike"] < current_price]
            if walls_below:
                nearest_put = max(walls_below, key=lambda x: x["strike"])
                if 0 < (current_price - nearest_put["strike"]) / current_price < 0.02: # Within 2%
                    approaching_put_wall = True
                    nearest_put_wall_strike = nearest_put["strike"]

        # Determine signal based on bias and structure
        mm_bias = money_market_bias.get("bias", "UNKNOWN")
        gamma_exposure = self.strike_levels["gamma_exposure"]
        gamma_trend = self.strike_levels.get("gamma_trend", "UNKNOWN")
        vega_trend = self.strike_levels.get("vega_trend", "UNKNOWN")
        avg_iv = self.strike_levels.get("avg_implied_vol", 0)
        signal, confidence, rationale = "NEUTRAL", "LOW", "Default neutral signal."

        # Extract values before f-string
        nearest_call_strike_fmt = f"{nearest_call_wall_strike:.0f}" if nearest_call_wall_strike else "N/A"
        nearest_put_strike_fmt = f"{nearest_put_wall_strike:.0f}" if nearest_put_wall_strike else "N/A"

        if mm_bias == "BEARISH":
            if approaching_call_wall and gamma_trend == "NEGATIVE_GAMMA_WALL": 
                signal, confidence, rationale = "STRONG SHORT", "HIGH", f"Bearish MM bias, approaching Call Wall @ {nearest_call_strike_fmt} with Negative Gamma Wall"
            elif approaching_call_wall: 
                signal, confidence, rationale = "STRONG SHORT", "HIGH", f"Bearish MM bias, approaching Call Wall @ {nearest_call_strike_fmt}"
            elif gamma_exposure == "NEGATIVE" and vega_trend == "HIGH_VEGA_RISK": 
                signal, confidence, rationale = "SHORT", "HIGH", f"Bearish MM bias, Negative Gamma, High Vega Risk (IV: {avg_iv:.1%})"
            elif gamma_exposure == "NEGATIVE": 
                signal, confidence, rationale = "SHORT", "MEDIUM", "Bearish MM bias, Negative Gamma"
            else: signal, confidence, rationale = "SHORT", "LOW", "Bearish MM bias"
        elif mm_bias == "BULLISH":
            if approaching_put_wall and gamma_trend == "POSITIVE_GAMMA_WALL": 
                signal, confidence, rationale = "STRONG LONG", "HIGH", f"Bullish MM bias, approaching Put Wall @ {nearest_put_strike_fmt} with Positive Gamma Wall"
            elif approaching_put_wall: 
                signal, confidence, rationale = "STRONG LONG", "HIGH", f"Bullish MM bias, approaching Put Wall @ {nearest_put_strike_fmt}"
            elif gamma_exposure == "POSITIVE" and vega_trend == "LOW_VEGA_RISK": 
                signal, confidence, rationale = "LONG", "HIGH", f"Bullish MM bias, Positive Gamma, Low Vega Risk (IV: {avg_iv:.1%})"
            elif gamma_exposure == "POSITIVE": 
                signal, confidence, rationale = "LONG", "MEDIUM", "Bullish MM bias, Positive Gamma"
            else: signal, confidence, rationale = "LONG", "LOW", "Bullish MM bias"
        elif mm_bias == "NEUTRAL":
            if approaching_call_wall and gamma_trend == "NEGATIVE_GAMMA_WALL": 
                signal, confidence, rationale = "SHORT", "MEDIUM", f"Neutral MM bias, approaching Call Wall @ {nearest_call_strike_fmt}, Negative Gamma Wall"
            elif approaching_put_wall and gamma_trend == "POSITIVE_GAMMA_WALL": 
                signal, confidence, rationale = "LONG", "MEDIUM", f"Neutral MM bias, approaching Put Wall @ {nearest_put_strike_fmt}, Positive Gamma Wall"
            elif vega_trend == "HIGH_VEGA_RISK": 
                signal, confidence, rationale = "NEUTRAL", "LOW", f"Neutral MM bias, High Vega Risk suggests volatility plays (IV: {avg_iv:.1%})"
            else: rationale = "Neutral MM bias, no clear option structure signal"
        else: # UNKNOWN bias
             rationale = "Money market bias unknown, cannot generate reliable signal."
             signal = "UNKNOWN"

        # Determine position type, expiry, size
        if "SHORT" in signal: position_type = "PUT SPREADS"
        elif "LONG" in signal: position_type = "CALL SPREADS"
        else: position_type = "AVOID DIRECTIONAL"

        if confidence == "HIGH": expiry, size = ("1-2 WEEKS" if "SHORT" in signal else "2-3 WEEKS"), "3-5%"
        elif confidence == "MEDIUM": expiry, size = ("1-2 WEEKS" if "SHORT" in signal else "1-2 WEEKS"), "2-3%"
        elif confidence == "LOW" and signal not in ["NEUTRAL", "UNKNOWN"]: expiry, size = ("1 WEEK" if "SHORT" in signal else "1 WEEK"), "1-2%"
        else: expiry, size = "N/A", "0%"

        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "ticker": self.option_chain.get("option_ticker", "SPX"),
            "current_price": current_price,
            "signal": signal,
            "confidence": confidence,
            "rationale": rationale,
            "position_type": position_type,
            "recommended_expiry": expiry,
            "position_size": size,
            "approaching_call_wall": approaching_call_wall,
            "nearest_call_wall": nearest_call_wall_strike,
            "approaching_put_wall": approaching_put_wall,
            "nearest_put_wall": nearest_put_wall_strike,
            "gamma_exposure": gamma_exposure,
            "gamma_trend": gamma_trend,
            "vega_trend": vega_trend,
            "avg_implied_vol": avg_iv,
            "net_gamma": self.strike_levels.get("net_gamma", 0),
            "net_vega": self.strike_levels.get("net_vega", 0),
            "money_market_bias": mm_bias,
            "data_source": data_source
        }
    
    def plot_gamma_wall(self, save_path: str = None) -> Optional[str]:
        """Plot gamma exposure by strike level to visualize gamma walls."""
        if not self.option_chain or not self.strike_levels:
            print("Cannot plot gamma wall, analysis not complete.")
            return None

        calls_df = pd.DataFrame(self.option_chain["calls"])
        puts_df = pd.DataFrame(self.option_chain["puts"])
        current_price = self.strike_levels["current_price"]
        data_source = self.option_chain.get("data_source", "Unknown")

        if calls_df.empty or puts_df.empty or pd.isna(current_price):
            print("Cannot plot gamma wall due to missing data.")
            return None

        # Calculate gamma exposure for plotting
        time_to_expiry = 7 / 365.25  # Assume 1 week to expiry
        
        # Add gamma calculations
        calls_df = self._calculate_greeks(calls_df, current_price, time_to_expiry, "call")
        puts_df = self._calculate_greeks(puts_df, current_price, time_to_expiry, "put")

        # Filter to reasonable range around current price
        price_buffer = 300  # Fixed buffer like original
        min_strike = current_price - price_buffer
        max_strike = current_price + price_buffer
        
        # Combine and filter data
        all_strikes_data = []
        
        # Process calls (positive gamma exposure for market makers)
        calls_filtered = calls_df[(calls_df["strike"] >= min_strike) & (calls_df["strike"] <= max_strike)]
        for _, row in calls_filtered.iterrows():
            gamma_exposure = row["openInterest"] * row["gamma"] * 100  # *100 for contract multiplier
            all_strikes_data.append({
                "strike": row["strike"],
                "gamma_exposure": gamma_exposure,  # Positive for calls
                "option_type": "calls",
                "open_interest": row["openInterest"]
            })
        
        # Process puts (negative gamma exposure for market makers when they're short puts)
        puts_filtered = puts_df[(puts_df["strike"] >= min_strike) & (puts_df["strike"] <= max_strike)]
        for _, row in puts_filtered.iterrows():
            gamma_exposure = -(row["openInterest"] * row["gamma"] * 100)  # Negative for puts (*100 for contract multiplier)
            all_strikes_data.append({
                "strike": row["strike"],
                "gamma_exposure": gamma_exposure,  # Negative for puts
                "option_type": "puts",
                "open_interest": row["openInterest"]
            })
        
        if not all_strikes_data:
            print("No gamma data available for plotting.")
            return None
        
        # Convert to DataFrame and aggregate by strike
        gamma_df = pd.DataFrame(all_strikes_data)
        strike_gamma = gamma_df.groupby("strike").agg({
            "gamma_exposure": "sum",
            "open_interest": "sum"
        }).reset_index()
        
        # Sort by strike
        strike_gamma = strike_gamma.sort_values("strike")
        
        # Calculate cumulative gamma exposure
        strike_gamma["cumulative_gamma"] = strike_gamma["gamma_exposure"].cumsum()
        
        # Create the plot with space for table (like original)
        fig = plt.figure(figsize=(15, 16))
        
        # Create chart areas (top 75% of figure)
        ax1 = plt.subplot2grid((12, 1), (0, 0), rowspan=5)  # Gamma exposure chart
        ax2 = plt.subplot2grid((12, 1), (5, 0), rowspan=5)  # Cumulative gamma chart
        
        # === TOP PLOT: Gamma Exposure by Strike ===
        colors = ["red" if x < 0 else "green" for x in strike_gamma["gamma_exposure"]]
        bars = ax1.bar(strike_gamma["strike"], strike_gamma["gamma_exposure"], 
                      color=colors, alpha=0.7, width=10)
        
        # Current price line
        ax1.axvline(x=current_price, color="blue", linestyle="--", linewidth=3, 
                   label=f"Current Price: {current_price:.2f}")
        
        # Zero line
        ax1.axhline(y=0, color="black", linestyle="-", linewidth=1, alpha=0.5)
        
        # Highlight significant gamma walls
        gamma_threshold = strike_gamma["gamma_exposure"].abs().quantile(0.90)  # Top 10% by absolute gamma
        significant_strikes = strike_gamma[strike_gamma["gamma_exposure"].abs() >= gamma_threshold]
        
        for _, row in significant_strikes.iterrows():
            strike_val = row["strike"]
            gamma_val = row["gamma_exposure"]
            wall_type = "CALL WALL" if gamma_val > 0 else "PUT WALL"
            color = "darkgreen" if gamma_val > 0 else "darkred"
            
            # Add vertical line for wall
            ax1.axvline(x=strike_val, color=color, linestyle="-", linewidth=2, alpha=0.8)
            
            # Add annotation
            ax1.annotate(f'{wall_type}\n{strike_val:.0f}\nγ: {gamma_val:,.0f}', 
                        xy=(strike_val, gamma_val), 
                        xytext=(10, 10), textcoords='offset points',
                        ha='left', va='bottom', fontsize=9, fontweight='bold',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.2))
        
        ax1.set_title(f"Gamma Wall Analysis - {self.option_chain.get('option_ticker', 'SPX')} ({data_source})\n"
                     f"Net Gamma: {strike_gamma['gamma_exposure'].sum():,.0f}", 
                     fontsize=14, fontweight='bold')
        ax1.set_ylabel("Gamma Exposure\n(Market Maker Perspective)", fontsize=12)
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax1.legend(loc="upper left")
        ax1.grid(True, alpha=0.3)
        
        # Add explanatory text
        explanation = ("Positive γ: MM buy as price rises (resistance)\n"
                      "Negative γ: MM sell as price rises (support)")
        ax1.text(0.98, 0.02, explanation, transform=ax1.transAxes, 
                fontsize=9, ha='right', va='bottom',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8))
        
        # === BOTTOM PLOT: Cumulative Gamma Profile ===
        ax2.plot(strike_gamma["strike"], strike_gamma["cumulative_gamma"], 
                color="purple", linewidth=3, label="Cumulative Gamma")
        ax2.fill_between(strike_gamma["strike"], 0, strike_gamma["cumulative_gamma"], 
                        alpha=0.3, color="purple")
        
        # Current price line
        ax2.axvline(x=current_price, color="blue", linestyle="--", linewidth=3, 
                   label=f"Current Price: {current_price:.2f}")
        
        # Zero line
        ax2.axhline(y=0, color="black", linestyle="-", linewidth=1, alpha=0.5)
        
        # Find current cumulative gamma
        current_price_idx = strike_gamma.iloc[(strike_gamma["strike"] - current_price).abs().argsort()[:1]].index[0]
        current_cum_gamma = strike_gamma.loc[current_price_idx, "cumulative_gamma"]
        
        ax2.scatter([current_price], [current_cum_gamma], color="red", s=100, zorder=5, 
                   label=f"Current Cum γ: {current_cum_gamma:,.0f}")
        
        ax2.set_title("Cumulative Gamma Profile - Market Flow Direction", fontsize=12, fontweight='bold')
        ax2.set_xlabel("Strike Price", fontsize=12)
        ax2.set_ylabel("Cumulative Gamma", fontsize=12)
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax2.legend(loc="upper left")
        ax2.grid(True, alpha=0.3)
        
        # Add market maker impact explanation
        if current_cum_gamma > 0:
            mm_impact = "MM are net long gamma → stabilizing/supportive"
            impact_color = "green"
        else:
            mm_impact = "MM are net short gamma → destabilizing/momentum"
            impact_color = "red"
            
        ax2.text(0.98, 0.98, f"Market Impact: {mm_impact}", transform=ax2.transAxes, 
                fontsize=10, ha='right', va='top', fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor=impact_color, alpha=0.2))
        
        # Calculate net gamma for table
        net_gamma = strike_gamma['gamma_exposure'].sum()
        
        # Add gamma analysis table below the charts
        self._add_gamma_analysis_table(fig, net_gamma, strike_gamma, current_price, current_cum_gamma)
        
        plt.tight_layout()
        
        if save_path:
            try:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"Gamma wall plot saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving gamma wall plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None
    
    def _add_gamma_analysis_table(self, fig, net_gamma, strike_gamma_df, current_price, current_cum_gamma):
        """Add gamma analysis table below the gamma wall charts"""
        try:
            # Calculate additional metrics
            total_positive_gamma = strike_gamma_df[strike_gamma_df['gamma_exposure'] > 0]['gamma_exposure'].sum()
            total_negative_gamma = strike_gamma_df[strike_gamma_df['gamma_exposure'] < 0]['gamma_exposure'].sum()
            max_gamma_strike = strike_gamma_df.loc[strike_gamma_df['gamma_exposure'].abs().idxmax(), 'strike']
            max_gamma_value = strike_gamma_df.loc[strike_gamma_df['gamma_exposure'].abs().idxmax(), 'gamma_exposure']
            
            # Determine gamma concentration around current price (within 2%)
            atm_range = (current_price * 0.98, current_price * 1.02)
            atm_strikes = strike_gamma_df[
                (strike_gamma_df['strike'] >= atm_range[0]) & 
                (strike_gamma_df['strike'] <= atm_range[1])
            ]
            atm_gamma_total = atm_strikes['gamma_exposure'].sum()
            
            # Create table data
            gamma_table_data = [
                ["Net Gamma", f"{net_gamma:,.0f}"],
                ["Positive Gamma", f"{total_positive_gamma:,.0f}"],
                ["Negative Gamma", f"{total_negative_gamma:,.0f}"],
                ["Current Price", f"{current_price:.2f}"],
                ["Current Cum Gamma", f"{current_cum_gamma:,.0f}"],
                ["ATM Gamma (±2%)", f"{atm_gamma_total:,.0f}"],
                ["Max Gamma Strike", f"{max_gamma_strike:.0f}"],
                ["Max Gamma Value", f"{max_gamma_value:,.0f}"]
            ]
            
            # Table headers
            gamma_headers = ["Metric", "Value"]
            
            # Create table area below the charts
            gamma_table_ax = plt.subplot2grid((12, 1), (10, 0), rowspan=2)
            gamma_table_ax.axis('off')
            gamma_table = gamma_table_ax.table(cellText=gamma_table_data, 
                                             colLabels=gamma_headers,
                                             cellLoc='center',
                                             loc='center',
                                             colWidths=[0.5, 0.5])
            gamma_table.auto_set_font_size(False)
            gamma_table.set_fontsize(9)
            gamma_table.scale(1, 1.5)
            
            # Style the gamma table
            for (i, j), cell in gamma_table.get_celld().items():
                if i == 0:  # Header row
                    cell.set_facecolor('#3498db')
                    cell.set_text_props(weight='bold', color='white')
                else:
                    cell.set_facecolor('#ebf3fd' if i % 2 == 0 else 'white')
            
            gamma_table_ax.set_title('GAMMA ANALYSIS SUMMARY', 
                                   fontsize=12, fontweight='bold', 
                                   color='#2980b9', pad=20)
            
        except Exception as e:
            print(f"Error adding gamma analysis table: {e}") 