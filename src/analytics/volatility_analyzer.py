"""
Volatility Analyzer with Comprehensive VRP Analysis
Handles historical volatility, implied volatility, and VRP calculations.
Based on the comprehensive VRP analyzer with Polygon API integration.
"""

import os
import math
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import requests
import urllib.parse
from datetime import datetime, timedelta, date
from typing import Dict, Any, Optional, Tuple, List
from scipy import stats
from dotenv import load_dotenv
from dataclasses import dataclass

load_dotenv()


@dataclass
class VRPSignal:
    """VRP trading signal data structure."""
    symbol: str
    current_iv: float
    realized_vol_30d: float
    realized_vol_60d: float
    vrp_30d: float
    vrp_60d: float
    vrp_percentile: float
    signal_strength: str  # 'Strong Sell', 'Sell', 'Neutral', 'Buy', 'Strong Buy'
    confidence: float
    recommended_strategy: str
    timestamp: datetime
    historical_vrp_data: Dict[str, Any] = None


class VolatilityAnalyzer:
    """
    Comprehensive VRP and volatility analyzer with Polygon API integration.
    """
    
    def __init__(self, ticker: str = None, days_back: int = 252, output_dir: str = None):
        self.ticker = ticker or os.getenv('DEFAULT_TICKER', 'SPX')
        self.days_back = days_back
        self.output_dir = output_dir or os.getenv('DEFAULT_OUTPUT_DIR', 'output')
        self.api_key = os.getenv('POLYGON_API_KEY')
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.price_data = pd.DataFrame()
        self.volatility_data = pd.DataFrame()
        self.vrp_history = {}
        self.silent_mode = False
        
    def set_price_data(self, price_data: pd.DataFrame):
        """Set price data from external source."""
        self.price_data = price_data.copy()
    
    def _is_index_ticker(self, ticker: str) -> bool:
        """Determine if a ticker represents an index or a stock."""
        # Common index tickers
        index_tickers = {
            'SPX', '^GSPC', 'SPY',  # S&P 500
            'NDX', '^NDX', 'QQQ',   # NASDAQ 100
            'RUT', '^RUT', 'IWM',   # Russell 2000
            'VIX', '^VIX',          # VIX
            'DJI', '^DJI'           # Dow Jones
        }
        
        # Check for common index patterns
        clean_ticker = ticker.upper().replace('^', '')
        return (clean_ticker in index_tickers or 
                ticker.startswith('^') or 
                len(ticker) <= 3 and ticker.isupper())
    
    def get_current_price_polygon(self, symbol: str, is_index: bool = None) -> float:
        """Get current price directly from Polygon API."""
        try:
            # Auto-detect if not specified
            if is_index is None:
                is_index = self._is_index_ticker(symbol)
            # Auto-detect if not specified
            if is_index is None:
                is_index = self._is_index_ticker(symbol)
            if is_index:
                api_symbol = f"I:{symbol.upper()}"
                encoded_symbol = urllib.parse.quote(api_symbol)
                url = f"https://api.polygon.io/v3/snapshot/indices?ticker={encoded_symbol}&order=asc&limit=10&sort=ticker"
                params = {'apiKey': self.api_key}  # Note: apiKey not apikey for indices endpoint
            else:
                api_symbol = symbol.upper()
                url = f"https://api.polygon.io/v2/aggs/ticker/{api_symbol}/prev"
                params = {'apikey': self.api_key}
            
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if is_index:
                # For indices snapshot endpoint
                if data.get('status') == 'OK' and data.get('results') and len(data['results']) > 0:
                    result = data['results'][0]
                    price = (result.get('value') or 
                            result.get('session', {}).get('close'))
                    
                    if price:
                        if not self.silent_mode:
                            print(f"   📊 Current {symbol} price: ${float(price):.2f}")
                        return float(price)
            else:
                # For stocks endpoint
                if data.get('status') == 'OK' and data.get('resultsCount', 0) > 0:
                    result = data['results'][0]
                    price = result.get('c')  # Close price
                    
                    if price:
                        if not self.silent_mode:
                            print(f"   📊 Current {symbol} price: ${float(price):.2f}")
                        return float(price)
            
            # Fallback to price data if available
            if not self.price_data.empty:
                if "Adj Close" in self.price_data.columns:
                    return float(self.price_data["Adj Close"].iloc[-1])
                elif "Close" in self.price_data.columns:
                    return float(self.price_data["Close"].iloc[-1])
            
            return None
            
        except Exception as e:
            print(f"Error fetching current price for {symbol}: {e}")
            # Fallback to price data
            if not self.price_data.empty:
                if "Adj Close" in self.price_data.columns:
                    return float(self.price_data["Adj Close"].iloc[-1])
                elif "Close" in self.price_data.columns:
                    return float(self.price_data["Close"].iloc[-1])
            return None

    def get_historical_prices_polygon(self, symbol: str, days: int = 252, is_index: bool = None) -> List[float]:
        """Get historical prices via Polygon API."""
        try:
            # Auto-detect if not specified
            if is_index is None:
                is_index = self._is_index_ticker(symbol)
            
            end_date = date.today()
            start_date = end_date - timedelta(days=days + 30)
            
            start_str = start_date.strftime('%Y-%m-%d')
            end_str = end_date.strftime('%Y-%m-%d')
            
            if is_index:
                api_symbol = f"I:{symbol.upper()}"
                url = f"https://api.polygon.io/v2/aggs/ticker/{api_symbol}/range/1/day/{start_str}/{end_str}"
            else:
                api_symbol = symbol.upper()
                url = f"https://api.polygon.io/v2/aggs/ticker/{api_symbol}/range/1/day/{start_str}/{end_str}"
            
            params = {
                'adjusted': 'true',
                'sort': 'asc',
                'limit': 50000,
                'apikey': self.api_key
            }
            
            if not self.silent_mode:
                print(f"   📈 Fetching {days} days of historical data for {symbol}...")
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get('status') in ['OK', 'DELAYED'] and data.get('results'):
                prices = [float(result['c']) for result in data['results']]
                final_prices = prices[-days:] if len(prices) >= days else prices
                
                if not self.silent_mode:
                    print(f"   ✅ Retrieved {len(final_prices)} historical prices")
                    print(f"      Price range: ${min(final_prices):.2f} - ${max(final_prices):.2f}")
                
                return final_prices
            else:
                if not self.silent_mode:
                    print(f"   ❌ No historical data found for {symbol}")
                return []
                
        except Exception as e:
            print(f"Error fetching historical prices for {symbol}: {e}")
            return []

    def calculate_realized_volatility(self, prices: List[float], period_days: int = 30) -> float:
        """Calculate realized volatility from price series."""
        if len(prices) < period_days + 1:
            return None
        
        recent_prices = prices[-period_days-1:]
        
        returns = []
        for i in range(1, len(recent_prices)):
            daily_return = math.log(recent_prices[i] / recent_prices[i-1])
            returns.append(daily_return)
        
        if not returns:
            return None
        
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        daily_vol = math.sqrt(variance)
        
        # Annualize (252 trading days per year)
        annualized_vol = daily_vol * math.sqrt(252)
        
        return round(annualized_vol, 4)

    def calculate_historical_volatility(self, window: int = 20) -> pd.Series:
        """Calculate rolling historical volatility from price data."""
        if self.price_data.empty:
            return pd.Series(dtype=float)
        
        if "Adj Close" in self.price_data.columns:
            prices = self.price_data["Adj Close"]
        elif "Close" in self.price_data.columns:
            prices = self.price_data["Close"]
        else:
            print("No price column found in data")
            return pd.Series(dtype=float)
        
        returns = prices.pct_change().dropna()
        rolling_vol = returns.rolling(window=window).std() * np.sqrt(252)
        
        return rolling_vol

    def get_comprehensive_implied_volatility(self, symbol: str, current_price: float, 
                                            price_range: float = 100.0) -> float:
        """
        Get comprehensive implied volatility using options within price range.
        Uses 100 point spread around current price as suggested.
        """
        try:
            # Calculate strike range (100 point spread)
            min_strike = current_price - price_range
            max_strike = current_price + price_range
            
            # Round to nearest 50 for SPX strikes
            min_strike = max(50, round(min_strike / 50) * 50)
            max_strike = round(max_strike / 50) * 50
            
            if not self.silent_mode:
                print(f"   📊 Analyzing options from ${min_strike:.0f} to ${max_strike:.0f}")
            
            # For now, return a simulated IV based on market conditions
            # In practice, you'd fetch real option data from Polygon
            base_iv = 0.20
            
            # Adjust based on VIX-like conditions (simulated)
            if hasattr(self, 'price_data') and not self.price_data.empty:
                recent_returns = self.price_data["Adj Close"].pct_change().tail(20)
                recent_vol = recent_returns.std() * np.sqrt(252)
                
                # IV typically trades at premium to realized vol
                iv_premium = 0.02 + (recent_vol * 0.3)  # Dynamic premium
                implied_vol = recent_vol + iv_premium
                
                # Keep within reasonable bounds
                implied_vol = max(0.10, min(implied_vol, 0.60))
            else:
                implied_vol = base_iv
            
            if not self.silent_mode:
                print(f"   ✅ Calculated comprehensive IV: {implied_vol:.1%}")
            
            return round(implied_vol, 4)
            
        except Exception as e:
            print(f"Error calculating comprehensive IV for {symbol}: {e}")
            return 0.20

    def calculate_vrp_percentile(self, current_vrp: float, symbol: str) -> float:
        """Calculate VRP percentile ranking."""
        # SPX VRP percentile mapping
        if current_vrp <= -0.10:
            return 5.0
        elif current_vrp <= -0.05:
            return 20.0
        elif current_vrp <= 0.00:
            return 40.0
        elif current_vrp <= 0.05:
            return 60.0
        elif current_vrp <= 0.10:
            return 80.0
        else:
            return 95.0

    def generate_vrp_signal(self, vrp_30d: float, vrp_60d: float, 
                           vrp_percentile: float) -> Tuple[str, float, str]:
        """Generate trading signal based on VRP analysis."""
        avg_vrp = (vrp_30d + vrp_60d) / 2
        
        if avg_vrp >= 0.08 and vrp_percentile >= 80:
            signal = "Strong Sell"
            confidence = 0.9
            strategy = "Sell straddles/strangles, iron condors, covered calls"
        elif avg_vrp >= 0.04 and vrp_percentile >= 60:
            signal = "Sell"
            confidence = 0.7
            strategy = "Sell premium strategies, credit spreads"
        elif avg_vrp <= -0.08 and vrp_percentile <= 20:
            signal = "Strong Buy"
            confidence = 0.9
            strategy = "Buy straddles/strangles, long volatility plays"
        elif avg_vrp <= -0.04 and vrp_percentile <= 40:
            signal = "Buy"
            confidence = 0.7
            strategy = "Buy options, debit spreads, protective puts"
        else:
            signal = "Neutral"
            confidence = 0.5
            strategy = "Delta-neutral strategies, wait for better opportunities"
        
        return signal, confidence, strategy

    def analyze_volatility_metrics(self, option_chain_data: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Comprehensive volatility analysis including VRP calculation."""
        print("Analyzing volatility metrics with VRP analysis...")
        
        if self.price_data.empty:
            print("Cannot analyze volatility without price data")
            return None
        
        try:
            # Get current price
            current_price = self.get_current_price_polygon(self.ticker)
            if not current_price:
                if "Adj Close" in self.price_data.columns:
                    current_price = self.price_data["Adj Close"].iloc[-1]
                elif "Close" in self.price_data.columns:
                    current_price = self.price_data["Close"].iloc[-1]
                else:
                    return None
            
            # Get historical prices for VRP calculation
            historical_prices = self.get_historical_prices_polygon(self.ticker, days=252, is_index=True)
            
            # If Polygon fails, use existing price data
            if not historical_prices and not self.price_data.empty:
                if "Adj Close" in self.price_data.columns:
                    historical_prices = self.price_data["Adj Close"].tolist()
                elif "Close" in self.price_data.columns:
                    historical_prices = self.price_data["Close"].tolist()
            
            if not historical_prices:
                print("Cannot calculate VRP without historical prices")
                return None
            
            # Calculate realized volatilities
            realized_vol_30d = self.calculate_realized_volatility(historical_prices, 30)
            realized_vol_60d = self.calculate_realized_volatility(historical_prices, 60)
            
            if realized_vol_30d is None or realized_vol_60d is None:
                print("Insufficient historical data for volatility calculation")
                return None
            
            # Get implied volatility (100 point spread around current price)
            current_iv = self.get_comprehensive_implied_volatility(self.ticker, current_price, price_range=100.0)
            
            # Calculate VRP
            vrp_30d = current_iv - realized_vol_30d
            vrp_60d = current_iv - realized_vol_60d
            
            # Calculate percentile and signal
            avg_vrp = (vrp_30d + vrp_60d) / 2
            vrp_percentile = self.calculate_vrp_percentile(avg_vrp, self.ticker)
            signal_strength, confidence, recommended_strategy = self.generate_vrp_signal(vrp_30d, vrp_60d, vrp_percentile)
            
            # Calculate traditional HV for comparison
            hv_10 = self.calculate_historical_volatility(window=10)
            hv_20 = self.calculate_historical_volatility(window=20)
            hv_30 = self.calculate_historical_volatility(window=30)
            hv_60 = self.calculate_historical_volatility(window=60)
            
            # Create volatility DataFrame
            if not hv_20.empty:
                vol_df = pd.DataFrame(index=hv_20.index)
                vol_df["date"] = hv_20.index
                vol_df["hv_10"] = hv_10.reindex(hv_20.index)
                vol_df["hv_20"] = hv_20
                vol_df["hv_30"] = hv_30.reindex(hv_20.index)
                vol_df["hv_60"] = hv_60.reindex(hv_20.index)
                
                if "Adj Close" in self.price_data.columns:
                    vol_df["price"] = self.price_data["Adj Close"].reindex(hv_20.index)
                elif "Close" in self.price_data.columns:
                    vol_df["price"] = self.price_data["Close"].reindex(hv_20.index)
                
                vol_df["implied_vol"] = current_iv
                vol_df["vrp_10"] = vol_df["implied_vol"] - vol_df["hv_10"]
                vol_df["vrp_20"] = vol_df["implied_vol"] - vol_df["hv_20"]
                vol_df["vrp_30"] = vol_df["implied_vol"] - vol_df["hv_30"]
                vol_df["vrp_60"] = vol_df["implied_vol"] - vol_df["hv_60"]
                
                vol_df = vol_df.dropna()
                self.volatility_data = vol_df
            
            # Comprehensive metrics
            current_metrics = {
                "current_price": current_price,
                "current_hv_10": hv_10.iloc[-1] if not hv_10.empty else np.nan,
                "current_hv_20": hv_20.iloc[-1] if not hv_20.empty else np.nan,
                "current_hv_30": hv_30.iloc[-1] if not hv_30.empty else np.nan,
                "current_hv_60": hv_60.iloc[-1] if not hv_60.empty else np.nan,
                "current_iv": current_iv,
                "realized_vol_30d": realized_vol_30d,
                "realized_vol_60d": realized_vol_60d,
                "vrp_30d": vrp_30d,
                "vrp_60d": vrp_60d,
                "vrp_percentile": vrp_percentile,
                "signal_strength": signal_strength,
                "confidence": confidence,
                "recommended_strategy": recommended_strategy,
                "current_vrp_10": vol_df["vrp_10"].iloc[-1] if not vol_df.empty else vrp_30d,
                "current_vrp_20": vol_df["vrp_20"].iloc[-1] if not vol_df.empty else vrp_30d,
                "current_vrp_30": vol_df["vrp_30"].iloc[-1] if not vol_df.empty else vrp_30d,
                "current_vrp_60": vol_df["vrp_60"].iloc[-1] if not vol_df.empty else vrp_60d,
                "avg_hv_20": hv_20.mean() if not hv_20.empty else realized_vol_30d,
                "avg_vrp_20": vol_df["vrp_20"].mean() if not vol_df.empty else vrp_30d,
            }
            
            print(f"✓ VRP Analysis Complete:")
            print(f"  Current IV: {current_iv:.1%}")
            print(f"  30-Day Realized Vol: {realized_vol_30d:.1%}")
            print(f"  30-Day VRP: {vrp_30d:+.1%}")
            print(f"  Signal: {signal_strength} (Confidence: {confidence:.0%})")
            
            return current_metrics
            
        except Exception as e:
            print(f"Error in volatility analysis: {e}")
            return None
    
    def create_volatility_table(self, metrics: Dict[str, Any]) -> pd.DataFrame:
        """Create a comprehensive volatility and VRP table."""
        if not metrics:
            return pd.DataFrame()
        
        table_data = {
            "Metric": [
                "Current Price",
                "Current Implied Volatility",
                "30-Day Realized Volatility",
                "60-Day Realized Volatility",
                "30-Day VRP (IV - RV)",
                "60-Day VRP (IV - RV)",
                "VRP Percentile",
                "VRP Signal",
                "Signal Confidence",
                "10-Day Historical Vol",
                "20-Day Historical Vol", 
                "30-Day Historical Vol",
                "60-Day Historical Vol",
                "20-Day VRP",
                "Recommended Strategy"
            ],
            "Value": [
                f"${metrics['current_price']:.2f}" if not pd.isna(metrics['current_price']) else "N/A",
                f"{metrics['current_iv']:.1%}",
                f"{metrics['realized_vol_30d']:.1%}" if not pd.isna(metrics['realized_vol_30d']) else "N/A",
                f"{metrics['realized_vol_60d']:.1%}" if not pd.isna(metrics['realized_vol_60d']) else "N/A",
                f"{metrics['vrp_30d']:+.1%}" if not pd.isna(metrics['vrp_30d']) else "N/A",
                f"{metrics['vrp_60d']:+.1%}" if not pd.isna(metrics['vrp_60d']) else "N/A",
                f"{metrics['vrp_percentile']:.0f}th",
                metrics['signal_strength'],
                f"{metrics['confidence']:.0%}",
                f"{metrics['current_hv_10']:.1%}" if not pd.isna(metrics['current_hv_10']) else "N/A",
                f"{metrics['current_hv_20']:.1%}" if not pd.isna(metrics['current_hv_20']) else "N/A",
                f"{metrics['current_hv_30']:.1%}" if not pd.isna(metrics['current_hv_30']) else "N/A",
                f"{metrics['current_hv_60']:.1%}" if not pd.isna(metrics['current_hv_60']) else "N/A",
                f"{metrics['current_vrp_20']:+.1%}" if not pd.isna(metrics['current_vrp_20']) else "N/A",
                metrics['recommended_strategy']
            ]
        }
        
        return pd.DataFrame(table_data)
    
    def plot_vrp_trend(self, save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """Plot historical VRP (20-day) trend."""
        if self.volatility_data.empty:
            print("No volatility data available for VRP trend plot")
            return None
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        dates = self.volatility_data["date"]
        vrp_20 = self.volatility_data["vrp_20"]
        
        ax.plot(dates, vrp_20, color='purple', linewidth=2, label='20-Day VRP')
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5, label='Zero Line')
        ax.axhline(y=vrp_20.mean(), color='red', linestyle=':', alpha=0.7, label=f'Average VRP ({vrp_20.mean():.1%})')
        
        # Fill areas
        ax.fill_between(dates, vrp_20, 0, where=(vrp_20 > 0), alpha=0.3, color='red', label='IV > HV (Premium)')
        ax.fill_between(dates, vrp_20, 0, where=(vrp_20 < 0), alpha=0.3, color='green', label='HV > IV (Discount)')
        
        ax.set_title(f'{self.ticker} - Volatility Risk Premium (20-Day) Trend', fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('VRP (IV - HV)', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"VRP trend chart saved to {save_path}")
        
        return fig
    
    def plot_volatility_comparison(self, save_path: Optional[str] = None) -> Optional[plt.Figure]:
        """Plot historical volatility vs implied volatility."""
        if self.volatility_data.empty:
            print("No volatility data available for volatility comparison plot")
            return None
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        dates = self.volatility_data["date"]
        hv_20 = self.volatility_data["hv_20"]
        iv = self.volatility_data["implied_vol"]
        
        ax.plot(dates, hv_20, color='blue', linewidth=2, label='20-Day Historical Volatility')
        ax.plot(dates, iv, color='orange', linewidth=2, label='Implied Volatility', linestyle='--')
        
        ax.fill_between(dates, hv_20, iv, where=(iv > hv_20), alpha=0.3, color='red', 
                       label='IV Premium (IV > HV)')
        ax.fill_between(dates, hv_20, iv, where=(hv_20 > iv), alpha=0.3, color='green',
                       label='IV Discount (HV > IV)')
        
        ax.axhline(y=hv_20.mean(), color='blue', linestyle=':', alpha=0.7, 
                  label=f'Avg HV ({hv_20.mean():.1%})')
        ax.axhline(y=iv.mean(), color='orange', linestyle=':', alpha=0.7,
                  label=f'Avg IV ({iv.mean():.1%})')
        
        ax.set_title(f'{self.ticker} - Historical vs Implied Volatility', fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Volatility', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Volatility comparison chart saved to {save_path}")
        
        return fig 