"""
Money Market Analyzer
Handles Fed Net Liquidity calculations and money market analysis.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple


class MoneyMarketAnalyzer:
    """Analyzes money market conditions and calculates Fed Net Liquidity."""
    
    def __init__(self):
        self.liquidity_df = pd.DataFrame()
        self.error_messages = []
        
    def calculate_fed_net_liquidity(self, fed_assets: pd.Series, rrp_amount: pd.Series, 
                                  tga_balance: pd.Series, treasury_yield_10y: pd.Series,
                                  start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Calculate Fed Net Liquidity using provided data."""
        print("Calculating Fed Net Liquidity...")
        
        # Check if essential data is available
        if fed_assets.empty or rrp_amount.empty or treasury_yield_10y.empty:
            print("Cannot calculate Fed Net Liquidity due to missing essential data.")
            self.error_messages.append("Cannot calculate Fed Net Liquidity due to missing essential data")
            return pd.DataFrame()
        
        # Create common business day index
        common_index = pd.date_range(start=start_date.date(), end=end_date.date(), freq="B")
        df = pd.DataFrame(index=common_index)
        
        # Normalize indices to date only and handle duplicates
        def normalize_and_dedupe_series(series):
            """Normalize index to date and remove duplicates by keeping last value."""
            if series.empty:
                return series
            
            series_norm = series.copy()
            
            # Normalize index to date only
            if hasattr(series_norm.index, 'normalize'):
                series_norm.index = series_norm.index.normalize()
            elif hasattr(series_norm, 'index') and len(series_norm) > 0:
                # Convert RangeIndex to DatetimeIndex if needed
                series_norm.index = pd.to_datetime(series_norm.index, errors='coerce')
                if hasattr(series_norm.index, 'normalize'):
                    series_norm.index = series_norm.index.normalize()
            
            # Remove duplicates by keeping the last value for each date
            if series_norm.index.duplicated().any():
                series_norm = series_norm[~series_norm.index.duplicated(keep='last')]
            
            return series_norm
        
        fed_assets_norm = normalize_and_dedupe_series(fed_assets)
        rrp_norm = normalize_and_dedupe_series(rrp_amount)
        treasury_yield_norm = normalize_and_dedupe_series(treasury_yield_10y)
        
        # Resample and forward fill data
        df["fed_assets"] = fed_assets_norm.reindex(common_index).ffill()
        df["rrp_amount"] = rrp_norm.reindex(common_index).ffill()
        df["treasury_10y_yield"] = treasury_yield_norm.reindex(common_index).ffill()
        
        # Handle TGA separately due to potential gaps
        if not tga_balance.empty:
            # TGA data comes as DataFrame with multiple columns, extract the appropriate balance
            if isinstance(tga_balance, pd.DataFrame):
                # Try closing balance first, fall back to opening balance
                if 'close_today_bal' in tga_balance.columns and not tga_balance['close_today_bal'].isna().all():
                    tga_series = tga_balance['close_today_bal']
                    print("Using TGA closing balance data")
                elif 'open_today_bal' in tga_balance.columns and not tga_balance['open_today_bal'].isna().all():
                    tga_series = tga_balance['open_today_bal']
                    print("TGA closing balance empty, using opening balance data")
                else:
                    print("Warning: Both TGA closing and opening balances are empty")
                    tga_series = pd.Series(dtype=float)
            else:
                # If it's already a Series, use it directly
                tga_series = tga_balance

            if not tga_series.empty:
                tga_norm = normalize_and_dedupe_series(tga_series)
                df["tga_balance"] = tga_norm.reindex(common_index).ffill()
            else:
                df["tga_balance"] = np.nan
                print("Warning: TGA data is empty, liquidity calculation will be incomplete.")
                self.error_messages.append("TGA data is empty, liquidity calculation incomplete")
        else:
            df["tga_balance"] = np.nan
            print("Warning: TGA data missing, liquidity calculation will be incomplete.")
            self.error_messages.append("TGA data missing, liquidity calculation incomplete")
        
        # Drop rows where essential data is missing
        essential_cols = ["fed_assets", "rrp_amount", "treasury_10y_yield"]
        df.dropna(subset=essential_cols, inplace=True)
        
        if df.empty:
            print("Not enough overlapping essential data to calculate Fed Net Liquidity.")
            self.error_messages.append("Not enough overlapping essential data for Fed Net Liquidity calculation")
            return pd.DataFrame()
        
        # Calculate Fed Net Liquidity
        # First try with TGA data if available
        df["core_liquidity"] = np.where(
            df["tga_balance"].notna(),
            df["fed_assets"] - (df["tga_balance"] + df["rrp_amount"]),
            # Fallback: use Fed assets minus RRP only if TGA is missing
            df["fed_assets"] - df["rrp_amount"]
        )
        
        # Apply formula with yield adjustment (use more lenient yield threshold)
        df["fed_net_liquidity"] = np.where(
            (df["core_liquidity"].notna()) & (df["treasury_10y_yield"] > 0.01),  # Lower threshold
            df["core_liquidity"] / df["treasury_10y_yield"] - 1625,
            # Fallback: use core liquidity without yield adjustment if yield is too low
            df["core_liquidity"]
        )
        
        self.liquidity_df = df
        print(f"Calculated Fed Net Liquidity for {len(df)} days (may have NaNs if TGA missing).")
        return self.liquidity_df
    
    def analyze_liquidity_conditions(self) -> Dict[str, Any]:
        """Analyze current liquidity conditions based on calculated liquidity."""
        print("Analyzing liquidity conditions...")
        
        if self.liquidity_df.empty or self.liquidity_df["fed_net_liquidity"].isnull().all():
            print("No valid liquidity data available for analysis.")
            return self._create_error_analysis("Missing or invalid liquidity data")
        
        # Get valid liquidity data
        valid_liquidity = self.liquidity_df["fed_net_liquidity"].dropna()
        if valid_liquidity.empty:
            print("No valid liquidity data points found for trend analysis.")
            return self._create_error_analysis("No valid liquidity data points for trend analysis")
        
        # Calculate changes
        current_liquidity = valid_liquidity.iloc[-1]
        week_ago_liquidity = valid_liquidity.iloc[-6] if len(valid_liquidity) >= 6 else np.nan
        month_ago_liquidity = valid_liquidity.iloc[-22] if len(valid_liquidity) >= 22 else np.nan
        
        week_change = current_liquidity - week_ago_liquidity if not pd.isna(week_ago_liquidity) else np.nan
        month_change = current_liquidity - month_ago_liquidity if not pd.isna(month_ago_liquidity) else np.nan
        
        week_change_pct = (week_change / abs(week_ago_liquidity) * 100) if not pd.isna(week_ago_liquidity) and week_ago_liquidity != 0 else np.nan
        month_change_pct = (month_change / abs(month_ago_liquidity) * 100) if not pd.isna(month_ago_liquidity) and month_ago_liquidity != 0 else np.nan
        
        # Determine trends and conditions
        trend = self._determine_liquidity_trend(week_change, month_change)
        condition = self._determine_liquidity_condition(current_liquidity)
        
        # Analyze component impacts
        tga_impact = self._analyze_tga_impact()
        rrp_impact = self._analyze_rrp_impact()
        
        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "current_fed_net_liquidity": current_liquidity,
            "week_change": week_change,
            "week_change_pct": week_change_pct,
            "month_change": month_change,
            "month_change_pct": month_change_pct,
            "liquidity_trend": trend,
            "liquidity_condition": condition,
            "current_tga": self.liquidity_df["tga_balance"].iloc[-1] if not self.liquidity_df.empty else np.nan,
            "tga_week_change": self._get_tga_week_change(),
            "tga_week_change_pct": self._get_tga_week_change_pct(),
            "tga_impact": tga_impact,
            "current_rrp": self.liquidity_df["rrp_amount"].iloc[-1] if not self.liquidity_df.empty else np.nan,
            "rrp_week_change": self._get_rrp_week_change(),
            "rrp_week_change_pct": self._get_rrp_week_change_pct(),
            "rrp_impact": rrp_impact,
            "treasury_auction_assessment": getattr(self, 'auction_assessment', "NO AUCTION DATA")
        }
    
    def get_trading_bias(self, liquidity_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Determine trading bias based on liquidity conditions."""
        if liquidity_analysis.get("error"):
            liquidity_analysis.update({
                "bias": "UNKNOWN",
                "confidence": "NONE",
                "recommended_expiry": "N/A",
                "position_type": "N/A"
            })
            return liquidity_analysis
        
        bias = "NEUTRAL"
        confidence = "LOW"
        
        trend = liquidity_analysis["liquidity_trend"]
        tga_impact = liquidity_analysis["tga_impact"]
        condition = liquidity_analysis["liquidity_condition"]
        auction_assessment = liquidity_analysis["treasury_auction_assessment"]
        
        # Determine bias based on multiple factors
        if trend in ["DETERIORATING", "RECENTLY DETERIORATING"] and "DRAIN" in tga_impact:
            bias = "BEARISH"
            confidence = "HIGH" if "BEARISH" in auction_assessment else "MEDIUM"
        elif trend in ["IMPROVING", "RECENTLY IMPROVING"] and "ADDITION" in tga_impact:
            bias = "BULLISH"
            confidence = "HIGH" if "BULLISH" in auction_assessment else "MEDIUM"
        elif condition == "STRESSED":
            bias = "BEARISH"
            confidence = "MEDIUM"
        elif condition == "ABUNDANT":
            bias = "BULLISH"
            confidence = "MEDIUM"
        elif trend == "DETERIORATING":
            bias = "BEARISH"
            confidence = "LOW"
        elif trend == "IMPROVING":
            bias = "BULLISH"
            confidence = "LOW"
        
        # Adjust confidence based on auctions
        if confidence != "HIGH":
            if bias == "BEARISH" and "BEARISH" in auction_assessment:
                confidence = "MEDIUM"
            elif bias == "BULLISH" and "BULLISH" in auction_assessment:
                confidence = "MEDIUM"
        
        # Determine recommendations
        recommended_expiry, position_type = self._get_recommendations(bias, confidence)
        
        liquidity_analysis.update({
            "bias": bias,
            "confidence": confidence,
            "recommended_expiry": recommended_expiry,
            "position_type": position_type
        })
        
        return liquidity_analysis
    
    def _create_error_analysis(self, error_msg: str) -> Dict[str, Any]:
        """Create error analysis result."""
        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "error": error_msg,
            "bias": "UNKNOWN",
            "confidence": "NONE",
            "recommended_expiry": "N/A",
            "position_type": "N/A",
            "liquidity_condition": "UNKNOWN",
            "liquidity_trend": "UNKNOWN",
            "tga_impact": "UNKNOWN",
            "rrp_impact": "UNKNOWN",
            "treasury_auction_assessment": "UNKNOWN"
        }
    
    def _determine_liquidity_trend(self, week_change: float, month_change: float) -> str:
        """Determine liquidity trend based on changes."""
        if pd.isna(week_change) or pd.isna(month_change):
            return "UNKNOWN (INSUFFICIENT HISTORY)"
        elif week_change > 0 and month_change > 0:
            return "IMPROVING"
        elif week_change < 0 and month_change < 0:
            return "DETERIORATING"
        elif week_change > 0 and month_change < 0:
            return "RECENTLY IMPROVING"
        else:
            return "RECENTLY DETERIORATING"
    
    def _determine_liquidity_condition(self, current_liquidity: float) -> str:
        """Determine liquidity condition based on current level."""
        if pd.isna(current_liquidity):
            return "UNKNOWN"
        elif current_liquidity > 1000:
            return "ABUNDANT"
        elif current_liquidity > 0:
            return "ADEQUATE"
        elif current_liquidity > -1000:
            return "TIGHTENING"
        else:
            return "STRESSED"
    
    def _analyze_tga_impact(self) -> str:
        """Analyze TGA impact on liquidity."""
        if self.liquidity_df.empty or "tga_balance" not in self.liquidity_df.columns:
            return "UNKNOWN (INSUFFICIENT TGA HISTORY)"
        
        tga_week_change = self._get_tga_week_change()
        
        if pd.isna(tga_week_change):
            return "UNKNOWN (INSUFFICIENT TGA HISTORY)"
        elif tga_week_change > 50:
            return "SIGNIFICANT LIQUIDITY DRAIN"
        elif tga_week_change > 20:
            return "MODERATE LIQUIDITY DRAIN"
        elif tga_week_change < -50:
            return "SIGNIFICANT LIQUIDITY ADDITION"
        elif tga_week_change < -20:
            return "MODERATE LIQUIDITY ADDITION"
        else:
            return "NEUTRAL"
    
    def _analyze_rrp_impact(self) -> str:
        """Analyze RRP impact on liquidity."""
        if self.liquidity_df.empty or "rrp_amount" not in self.liquidity_df.columns:
            return "UNKNOWN (INSUFFICIENT RRP HISTORY)"
        
        rrp_week_change = self._get_rrp_week_change()
        
        if pd.isna(rrp_week_change):
            return "UNKNOWN (INSUFFICIENT RRP HISTORY)"
        elif rrp_week_change > 100:
            return "SIGNIFICANT LIQUIDITY DRAIN (RRP INCREASE)"
        elif rrp_week_change > 50:
            return "MODERATE LIQUIDITY DRAIN (RRP INCREASE)"
        elif rrp_week_change < -100:
            return "SIGNIFICANT LIQUIDITY ADDITION (RRP DECREASE)"
        elif rrp_week_change < -50:
            return "MODERATE LIQUIDITY ADDITION (RRP DECREASE)"
        else:
            return "NEUTRAL"
    
    def _get_tga_week_change(self) -> float:
        """Get TGA week-over-week change."""
        if self.liquidity_df.empty or len(self.liquidity_df) < 6:
            return np.nan
        current_tga = self.liquidity_df["tga_balance"].iloc[-1]
        week_ago_tga = self.liquidity_df["tga_balance"].iloc[-6]
        return current_tga - week_ago_tga if not pd.isna(current_tga) and not pd.isna(week_ago_tga) else np.nan
    
    def _get_tga_week_change_pct(self) -> float:
        """Get TGA week-over-week percentage change."""
        tga_change = self._get_tga_week_change()
        if pd.isna(tga_change) or len(self.liquidity_df) < 6:
            return np.nan
        week_ago_tga = self.liquidity_df["tga_balance"].iloc[-6]
        return (tga_change / week_ago_tga * 100) if not pd.isna(week_ago_tga) and week_ago_tga != 0 else np.nan
    
    def _get_rrp_week_change(self) -> float:
        """Get RRP week-over-week change."""
        if self.liquidity_df.empty or len(self.liquidity_df) < 6:
            return np.nan
        current_rrp = self.liquidity_df["rrp_amount"].iloc[-1]
        week_ago_rrp = self.liquidity_df["rrp_amount"].iloc[-6]
        return current_rrp - week_ago_rrp if not pd.isna(current_rrp) and not pd.isna(week_ago_rrp) else np.nan
    
    def _get_rrp_week_change_pct(self) -> float:
        """Get RRP week-over-week percentage change."""
        rrp_change = self._get_rrp_week_change()
        if pd.isna(rrp_change) or len(self.liquidity_df) < 6:
            return np.nan
        week_ago_rrp = self.liquidity_df["rrp_amount"].iloc[-6]
        return (rrp_change / week_ago_rrp * 100) if not pd.isna(week_ago_rrp) and week_ago_rrp != 0 else np.nan
    
    def _get_recommendations(self, bias: str, confidence: str) -> Tuple[str, str]:
        """Get trading recommendations based on bias and confidence."""
        if bias == "BEARISH" and confidence == "HIGH":
            return "1-2 WEEKS", "PUT SPREADS"
        elif bias == "BEARISH" and confidence == "MEDIUM":
            return "2-3 WEEKS", "PUT SPREADS"
        elif bias == "BEARISH" and confidence == "LOW":
            return "1 WEEK", "CONSIDER PUTS/SPREADS"
        elif bias == "BULLISH" and confidence == "HIGH":
            return "2-3 WEEKS", "CALL SPREADS"
        elif bias == "BULLISH" and confidence == "MEDIUM":
            return "3-4 WEEKS", "CALL SPREADS"
        elif bias == "BULLISH" and confidence == "LOW":
            return "1-2 WEEKS", "CONSIDER CALLS/SPREADS"
        else:
            return "AVOID DIRECTIONAL", "NON-DIRECTIONAL STRATEGIES"
    
    def plot_liquidity_indicators(self, save_path: str = None) -> Optional[str]:
        """Plot key liquidity indicators using fetched data with improved visibility."""
        if self.liquidity_df.empty or self.liquidity_df[["fed_net_liquidity", "tga_balance", "rrp_amount"]].isnull().all().all():
            print("Cannot plot liquidity indicators due to missing or invalid data.")
            return None

        # Limit to last 6 months for better visibility
        recent_data = self.liquidity_df.tail(120)  # Approximately 6 months of business days

        fig, axs = plt.subplots(3, 1, figsize=(12, 15), sharex=True)
        fig.suptitle("Money Market Liquidity Indicators (Last 6 Months)", fontsize=16)

        # Plot Fed Net Liquidity if available
        if "fed_net_liquidity" in recent_data.columns and not recent_data["fed_net_liquidity"].isnull().all():
            fed_data = recent_data["fed_net_liquidity"].dropna()
            axs[0].plot(recent_data.index, recent_data["fed_net_liquidity"],
                       color="blue", linewidth=2, label="Fed Net Liquidity (Calculated)")
            axs[0].axhline(y=0, color="r", linestyle="--", alpha=0.5)

            # Adjust y-axis to use full range
            if not fed_data.empty:
                y_min, y_max = fed_data.min(), fed_data.max()
                y_range = y_max - y_min
                if y_range > 0:
                    axs[0].set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)

            axs[0].set_title("Fed Net Liquidity")
            axs[0].set_ylabel("$ Billions (Scaled)")
            axs[0].grid(True)
            axs[0].legend()
        else:
            axs[0].text(0.5, 0.5, "Fed Net Liquidity Data Missing",
                       horizontalalignment="center", verticalalignment="center",
                       transform=axs[0].transAxes)
            axs[0].set_title("Fed Net Liquidity (Data Missing)")

        # Plot TGA Balance if available
        if "tga_balance" in recent_data.columns and not recent_data["tga_balance"].isnull().all():
            tga_data = recent_data["tga_balance"].dropna()
            axs[1].plot(recent_data.index, recent_data["tga_balance"],
                       color="green", linewidth=2, label="Treasury General Account")

            # Adjust y-axis to use full range
            if not tga_data.empty:
                y_min, y_max = tga_data.min(), tga_data.max()
                y_range = y_max - y_min
                if y_range > 0:
                    axs[1].set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)

            axs[1].set_title("Treasury General Account Balance")
            axs[1].set_ylabel("$ Billions")
            axs[1].grid(True)
            axs[1].legend()
        else:
            axs[1].text(0.5, 0.5, "TGA Data Missing",
                       horizontalalignment="center", verticalalignment="center",
                       transform=axs[1].transAxes)
            axs[1].set_title("Treasury General Account Balance (Data Missing)")

        # Plot RRP Usage if available
        if "rrp_amount" in recent_data.columns and not recent_data["rrp_amount"].isnull().all():
            rrp_data = recent_data["rrp_amount"].dropna()
            axs[2].plot(recent_data.index, recent_data["rrp_amount"],
                       color="purple", linewidth=2, label="Reverse Repo Facility Usage")

            # Adjust y-axis to use full range
            if not rrp_data.empty:
                y_min, y_max = rrp_data.min(), rrp_data.max()
                y_range = y_max - y_min
                if y_range > 0:
                    axs[2].set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)

            axs[2].set_title("Reverse Repo Facility Usage")
            axs[2].set_ylabel("$ Billions")
            axs[2].grid(True)
            axs[2].legend()
        else:
            axs[2].text(0.5, 0.5, "RRP Data Missing",
                       horizontalalignment="center", verticalalignment="center",
                       transform=axs[2].transAxes)
            axs[2].set_title("Reverse Repo Facility Usage (Data Missing)")

        # Format x-axis
        axs[2].xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
        plt.xticks(rotation=45)
        plt.tight_layout(rect=[0, 0.03, 1, 0.97])  # Adjust layout to prevent title overlap

        if save_path:
            try:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"Liquidity plot saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving liquidity plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None 