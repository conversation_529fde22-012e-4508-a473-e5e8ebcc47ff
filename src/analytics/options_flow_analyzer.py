"""
Advanced Options Flow Analyzer
Analyzes options flow using advanced Greeks to understand dealer positioning,
buying pressure, and market structure dynamics.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from .black_scholes import BlackScholesCalculator


class OptionsFlowAnalyzer:
    """Advanced options flow analysis using second-order Greeks."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        self.bs_calculator = BlackScholesCalculator()
        self.option_chain = None
        self.flow_analysis = None
        
    def set_option_chain(self, option_chain: Dict[str, Any]):
        """Set option chain data."""
        self.option_chain = option_chain
        
    def analyze_dealer_positioning(self, time_horizons: List[float] = None) -> Dict[str, Any]:
        """
        Analyze dealer positioning across multiple time horizons using advanced Greeks.
        
        Args:
            time_horizons: List of time to expiry values (in years) to analyze
            
        Returns:
            Comprehensive dealer positioning analysis
        """
        if not self.option_chain:
            return {"error": "No option chain data available"}
            
        if time_horizons is None:
            time_horizons = [7/365, 14/365, 30/365, 60/365]  # 1W, 2W, 1M, 2M
            
        current_price = self.option_chain.get("current_price")
        calls = self.option_chain.get("calls", [])
        puts = self.option_chain.get("puts", [])
        
        if not current_price or not calls or not puts:
            return {"error": "Insufficient option chain data"}
            
        analysis = {
            "current_price": current_price,
            "analysis_timestamp": datetime.now(),
            "time_horizons": {},
            "aggregate_metrics": {},
            "flow_signals": {}
        }
        
        # Analyze each time horizon
        for tte in time_horizons:
            horizon_name = f"{int(tte * 365)}D"
            analysis["time_horizons"][horizon_name] = self._analyze_time_horizon(
                calls, puts, current_price, tte
            )
        
        # Calculate aggregate metrics across all horizons
        analysis["aggregate_metrics"] = self._calculate_aggregate_metrics(analysis["time_horizons"])
        
        # Generate flow signals
        analysis["flow_signals"] = self._generate_flow_signals(analysis)
        
        self.flow_analysis = analysis
        return analysis
    
    def _analyze_time_horizon(self, calls: List[Dict], puts: List[Dict], 
                             current_price: float, tte: float) -> Dict[str, Any]:
        """Analyze dealer positioning for a specific time horizon."""
        
        # Filter options by time to expiry (approximate)
        # In real implementation, you'd filter by actual expiration dates
        calls_df = pd.DataFrame(calls)
        puts_df = pd.DataFrame(puts)
        
        # Calculate dealer metrics for all strikes
        call_metrics = []
        put_metrics = []
        
        for _, call in calls_df.iterrows():
            metrics = self.bs_calculator.calculate_dealer_positioning_metrics(
                current_price, call['strike'], tte, call['impliedVolatility'], 
                call['openInterest'], 'call'
            )
            metrics['strike'] = call['strike']
            metrics['volume'] = call.get('volume', 0)
            call_metrics.append(metrics)
            
        for _, put in puts_df.iterrows():
            metrics = self.bs_calculator.calculate_dealer_positioning_metrics(
                current_price, put['strike'], tte, put['impliedVolatility'], 
                put['openInterest'], 'put'
            )
            metrics['strike'] = put['strike']
            metrics['volume'] = put.get('volume', 0)
            put_metrics.append(metrics)
        
        call_metrics_df = pd.DataFrame(call_metrics)
        put_metrics_df = pd.DataFrame(put_metrics)
        
        # Calculate net exposures
        net_exposures = {
            'net_dealer_delta': call_metrics_df['dealer_delta_exposure'].sum() + put_metrics_df['dealer_delta_exposure'].sum(),
            'net_dealer_gamma': call_metrics_df['dealer_gamma_exposure'].sum() + put_metrics_df['dealer_gamma_exposure'].sum(),
            'net_dealer_vega': call_metrics_df['dealer_vega_exposure'].sum() + put_metrics_df['dealer_vega_exposure'].sum(),
            'net_dealer_vanna': call_metrics_df['dealer_vanna_exposure'].sum() + put_metrics_df['dealer_vanna_exposure'].sum(),
            'net_dealer_charm': call_metrics_df['dealer_charm_exposure'].sum() + put_metrics_df['dealer_charm_exposure'].sum(),
            'net_dealer_volga': call_metrics_df['dealer_volga_exposure'].sum() + put_metrics_df['dealer_volga_exposure'].sum(),
            'net_dealer_color': call_metrics_df['dealer_color_exposure'].sum() + put_metrics_df['dealer_color_exposure'].sum()
        }
        
        # Identify key levels
        key_levels = self._identify_key_levels(call_metrics_df, put_metrics_df, current_price)
        
        # Calculate flow pressure metrics
        flow_pressure = self._calculate_flow_pressure(call_metrics_df, put_metrics_df, current_price)
        
        return {
            'time_to_expiry': tte,
            'net_exposures': net_exposures,
            'key_levels': key_levels,
            'flow_pressure': flow_pressure,
            'call_metrics': call_metrics_df.to_dict('records'),
            'put_metrics': put_metrics_df.to_dict('records')
        }
    
    def _identify_key_levels(self, call_metrics_df: pd.DataFrame, 
                           put_metrics_df: pd.DataFrame, current_price: float) -> Dict[str, Any]:
        """Identify key support/resistance levels based on Greek concentrations."""
        
        # Combine all metrics
        all_metrics = pd.concat([call_metrics_df, put_metrics_df], ignore_index=True)
        
        # Find gamma concentration levels
        gamma_concentrations = all_metrics.groupby('strike')['dealer_gamma_exposure'].sum().abs()
        top_gamma_strikes = gamma_concentrations.nlargest(5).index.tolist()
        
        # Find vanna concentration levels (important for vol/spot correlation)
        vanna_concentrations = all_metrics.groupby('strike')['dealer_vanna_exposure'].sum().abs()
        top_vanna_strikes = vanna_concentrations.nlargest(3).index.tolist()
        
        # Find charm concentration levels (time decay effects)
        charm_concentrations = all_metrics.groupby('strike')['dealer_charm_exposure'].sum().abs()
        top_charm_strikes = charm_concentrations.nlargest(3).index.tolist()
        
        # Identify support/resistance based on position relative to current price
        support_levels = [s for s in top_gamma_strikes if s < current_price]
        resistance_levels = [s for s in top_gamma_strikes if s > current_price]
        
        return {
            'gamma_concentration_strikes': top_gamma_strikes,
            'vanna_concentration_strikes': top_vanna_strikes,
            'charm_concentration_strikes': top_charm_strikes,
            'support_levels': sorted(support_levels, reverse=True)[:3],  # Closest to current price
            'resistance_levels': sorted(resistance_levels)[:3]  # Closest to current price
        }
    
    def _calculate_flow_pressure(self, call_metrics_df: pd.DataFrame, 
                               put_metrics_df: pd.DataFrame, current_price: float) -> Dict[str, Any]:
        """Calculate buying/selling pressure metrics."""
        
        # ATM range (within 2% of current price)
        atm_range = (current_price * 0.98, current_price * 1.02)
        
        # OTM call buying (strikes > current price)
        otm_calls = call_metrics_df[call_metrics_df['strike'] > current_price]
        otm_call_volume = otm_calls['volume'].sum()
        otm_call_oi = otm_calls['dealer_gamma_exposure'].sum()
        
        # OTM put buying (strikes < current price)
        otm_puts = put_metrics_df[put_metrics_df['strike'] < current_price]
        otm_put_volume = otm_puts['volume'].sum()
        otm_put_oi = otm_puts['dealer_gamma_exposure'].sum()
        
        # ATM activity
        atm_calls = call_metrics_df[
            (call_metrics_df['strike'] >= atm_range[0]) & 
            (call_metrics_df['strike'] <= atm_range[1])
        ]
        atm_puts = put_metrics_df[
            (put_metrics_df['strike'] >= atm_range[0]) & 
            (put_metrics_df['strike'] <= atm_range[1])
        ]
        
        atm_call_volume = atm_calls['volume'].sum()
        atm_put_volume = atm_puts['volume'].sum()
        
        # Calculate put/call ratios
        total_call_volume = call_metrics_df['volume'].sum()
        total_put_volume = put_metrics_df['volume'].sum()
        put_call_volume_ratio = total_put_volume / total_call_volume if total_call_volume > 0 else 0
        
        total_call_oi = call_metrics_df['dealer_gamma_exposure'].sum()
        total_put_oi = put_metrics_df['dealer_gamma_exposure'].sum()
        put_call_oi_ratio = abs(total_put_oi / total_call_oi) if total_call_oi != 0 else 0
        
        # Vanna-based directional bias
        net_vanna = call_metrics_df['dealer_vanna_exposure'].sum() + put_metrics_df['dealer_vanna_exposure'].sum()
        vanna_bias = "BULLISH" if net_vanna > 0 else "BEARISH" if net_vanna < 0 else "NEUTRAL"
        
        # Charm-based time decay pressure
        net_charm = call_metrics_df['dealer_charm_exposure'].sum() + put_metrics_df['dealer_charm_exposure'].sum()
        time_decay_pressure = "ACCELERATING" if net_charm < 0 else "DECELERATING" if net_charm > 0 else "NEUTRAL"
        
        return {
            'otm_call_volume': otm_call_volume,
            'otm_put_volume': otm_put_volume,
            'atm_call_volume': atm_call_volume,
            'atm_put_volume': atm_put_volume,
            'put_call_volume_ratio': put_call_volume_ratio,
            'put_call_oi_ratio': put_call_oi_ratio,
            'vanna_directional_bias': vanna_bias,
            'time_decay_pressure': time_decay_pressure,
            'net_vanna_exposure': net_vanna,
            'net_charm_exposure': net_charm
        }
    
    def _calculate_aggregate_metrics(self, time_horizons: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate aggregate metrics across all time horizons."""
        
        if not time_horizons:
            return {}
        
        # Aggregate net exposures
        aggregate_exposures = {}
        exposure_keys = ['net_dealer_delta', 'net_dealer_gamma', 'net_dealer_vega', 
                        'net_dealer_vanna', 'net_dealer_charm', 'net_dealer_volga', 'net_dealer_color']
        
        for key in exposure_keys:
            values = [horizon['net_exposures'][key] for horizon in time_horizons.values()]
            aggregate_exposures[key] = {
                'total': sum(values),
                'average': np.mean(values),
                'short_term': values[0] if values else 0,  # Shortest horizon
                'long_term': values[-1] if values else 0   # Longest horizon
            }
        
        # Calculate term structure slopes
        term_structure = {}
        for key in exposure_keys:
            values = [horizon['net_exposures'][key] for horizon in time_horizons.values()]
            if len(values) >= 2:
                term_structure[f"{key}_slope"] = values[-1] - values[0]  # Long term - Short term
        
        return {
            'aggregate_exposures': aggregate_exposures,
            'term_structure': term_structure
        }
    
    def _generate_flow_signals(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trading signals based on options flow analysis."""
        
        signals = {
            'primary_signal': 'NEUTRAL',
            'confidence': 'LOW',
            'time_horizon': 'MIXED',
            'key_factors': [],
            'risk_factors': [],
            'price_targets': {}
        }
        
        if not analysis.get('aggregate_metrics'):
            return signals
        
        agg = analysis['aggregate_metrics']['aggregate_exposures']
        
        # Primary signal based on gamma and vanna
        net_gamma = agg.get('net_dealer_gamma', {}).get('total', 0)
        net_vanna = agg.get('net_dealer_vanna', {}).get('total', 0)
        net_charm = agg.get('net_dealer_charm', {}).get('total', 0)
        
        # Signal logic
        bullish_factors = 0
        bearish_factors = 0
        
        # Gamma analysis
        if net_gamma < 0:  # Dealers short gamma = market long gamma = stabilizing
            signals['key_factors'].append("Market long gamma (price stabilization)")
            bullish_factors += 1
        elif net_gamma > 0:  # Dealers long gamma = market short gamma = destabilizing
            signals['key_factors'].append("Market short gamma (increased volatility)")
            bearish_factors += 1
        
        # Vanna analysis
        if net_vanna > 0:  # Positive vanna = bullish vol/spot correlation
            signals['key_factors'].append("Positive vanna (vol up = spot up)")
            bullish_factors += 1
        elif net_vanna < 0:  # Negative vanna = bearish vol/spot correlation
            signals['key_factors'].append("Negative vanna (vol up = spot down)")
            bearish_factors += 1
        
        # Charm analysis (time decay effects)
        if net_charm < 0:  # Negative charm = delta decay over time
            signals['key_factors'].append("Negative charm (delta decay pressure)")
            bearish_factors += 1
        elif net_charm > 0:  # Positive charm = delta increase over time
            signals['key_factors'].append("Positive charm (delta acceleration)")
            bullish_factors += 1
        
        # Determine primary signal
        if bullish_factors > bearish_factors:
            signals['primary_signal'] = 'BULLISH'
            signals['confidence'] = 'HIGH' if bullish_factors - bearish_factors >= 2 else 'MEDIUM'
        elif bearish_factors > bullish_factors:
            signals['primary_signal'] = 'BEARISH'
            signals['confidence'] = 'HIGH' if bearish_factors - bullish_factors >= 2 else 'MEDIUM'
        else:
            signals['primary_signal'] = 'NEUTRAL'
            signals['confidence'] = 'LOW'
        
        # Time horizon analysis
        short_term_gamma = agg.get('net_dealer_gamma', {}).get('short_term', 0)
        long_term_gamma = agg.get('net_dealer_gamma', {}).get('long_term', 0)
        
        if abs(short_term_gamma) > abs(long_term_gamma):
            signals['time_horizon'] = 'SHORT_TERM_DOMINANT'
        elif abs(long_term_gamma) > abs(short_term_gamma):
            signals['time_horizon'] = 'LONG_TERM_DOMINANT'
        else:
            signals['time_horizon'] = 'BALANCED'
        
        # Risk factors
        if abs(net_gamma) > 1000000:  # Large gamma exposure
            signals['risk_factors'].append("High gamma exposure - increased volatility risk")
        
        if abs(net_vanna) > 500000:  # Large vanna exposure
            signals['risk_factors'].append("High vanna exposure - vol/spot correlation risk")
        
        return signals
    
    def plot_greek_exposures(self, save_path: str = None) -> Optional[str]:
        """Plot Greek exposures across strikes and time horizons."""
        if not self.flow_analysis:
            return None
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Options Flow Analysis - Greek Exposures', fontsize=16, fontweight='bold')
        
        current_price = self.flow_analysis['current_price']
        
        # Collect data across all time horizons
        all_strikes = []
        all_gamma = []
        all_vanna = []
        all_charm = []
        all_volga = []
        all_color = []
        all_horizons = []
        
        for horizon_name, horizon_data in self.flow_analysis['time_horizons'].items():
            for call_metric in horizon_data['call_metrics']:
                all_strikes.append(call_metric['strike'])
                all_gamma.append(call_metric['dealer_gamma_exposure'])
                all_vanna.append(call_metric['dealer_vanna_exposure'])
                all_charm.append(call_metric['dealer_charm_exposure'])
                all_volga.append(call_metric['dealer_volga_exposure'])
                all_color.append(call_metric['dealer_color_exposure'])
                all_horizons.append(horizon_name)
            
            for put_metric in horizon_data['put_metrics']:
                all_strikes.append(put_metric['strike'])
                all_gamma.append(put_metric['dealer_gamma_exposure'])
                all_vanna.append(put_metric['dealer_vanna_exposure'])
                all_charm.append(put_metric['dealer_charm_exposure'])
                all_volga.append(put_metric['dealer_volga_exposure'])
                all_color.append(put_metric['dealer_color_exposure'])
                all_horizons.append(horizon_name)
        
        df = pd.DataFrame({
            'strike': all_strikes,
            'gamma': all_gamma,
            'vanna': all_vanna,
            'charm': all_charm,
            'volga': all_volga,
            'color': all_color,
            'horizon': all_horizons
        })
        
        # Plot each Greek
        greeks = ['gamma', 'vanna', 'charm', 'volga', 'color']
        titles = ['Dealer Gamma Exposure', 'Dealer Vanna Exposure', 'Dealer Charm Exposure', 
                 'Dealer Volga Exposure', 'Dealer Color Exposure']
        
        # Calculate 3 standard deviations range based on price level
        # Use a percentage-based approach that scales with the underlying price
        price_std_estimate = current_price * 0.15  # Assume ~15% annual volatility as baseline
        range_width = 3 * price_std_estimate  # 3 standard deviations
        
        # Set minimum and maximum bounds to ensure reasonable chart range
        min_range_width = current_price * 0.30  # At least 30% range
        max_range_width = current_price * 0.90  # At most 90% range
        range_width = max(min_range_width, min(range_width, max_range_width))
        
        x_min = current_price - range_width
        x_max = current_price + range_width
        
        # Ensure we don't go below zero for the minimum
        x_min = max(0, x_min)
        
        for i, (greek, title) in enumerate(zip(greeks, titles)):
            ax = axes[i//3, i%3]
            
            # Aggregate by strike
            strike_data = df.groupby('strike')[greek].sum().reset_index()
            
            # Filter data to the visible range for better performance
            visible_data = strike_data[
                (strike_data['strike'] >= x_min) & 
                (strike_data['strike'] <= x_max)
            ]
            
            # Plot
            if not visible_data.empty:
                ax.bar(visible_data['strike'], visible_data[greek], alpha=0.7, 
                      color='red' if visible_data[greek].mean() < 0 else 'green')
            
            ax.axvline(current_price, color='black', linestyle='--', alpha=0.8, label='Current Price')
            ax.set_title(title)
            ax.set_xlabel('Strike Price')
            ax.set_ylabel('Exposure')
            ax.set_xlim(x_min, x_max)  # Set horizontal scale to 3 std dev range around current price
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # Remove empty subplot
        if len(greeks) < 6:
            fig.delaxes(axes[1, 2])
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            return save_path
        else:
            plt.show()
            return None
    
    def generate_flow_report(self) -> str:
        """Generate a comprehensive options flow analysis report."""
        if not self.flow_analysis:
            return "No flow analysis available. Run analyze_dealer_positioning() first."
        
        report = []
        report.append("=" * 80)
        report.append("ADVANCED OPTIONS FLOW ANALYSIS REPORT")
        report.append("=" * 80)
        report.append(f"Analysis Time: {self.flow_analysis['analysis_timestamp']}")
        report.append(f"Current Price: ${self.flow_analysis['current_price']:.2f}")
        report.append("")
        
        # Primary signals
        signals = self.flow_analysis['flow_signals']
        report.append("PRIMARY SIGNALS:")
        report.append(f"  Signal: {signals['primary_signal']}")
        report.append(f"  Confidence: {signals['confidence']}")
        report.append(f"  Time Horizon: {signals['time_horizon']}")
        report.append("")
        
        # Key factors
        if signals['key_factors']:
            report.append("KEY FACTORS:")
            for factor in signals['key_factors']:
                report.append(f"  • {factor}")
            report.append("")
        
        # Risk factors
        if signals['risk_factors']:
            report.append("RISK FACTORS:")
            for risk in signals['risk_factors']:
                report.append(f"  ⚠ {risk}")
            report.append("")
        
        # Aggregate exposures
        agg = self.flow_analysis['aggregate_metrics']['aggregate_exposures']
        report.append("AGGREGATE GREEK EXPOSURES:")
        for greek, data in agg.items():
            report.append(f"  {greek.replace('net_dealer_', '').upper()}: {data['total']:,.0f}")
        report.append("")
        
        # Time horizon breakdown
        report.append("TIME HORIZON ANALYSIS:")
        for horizon_name, horizon_data in self.flow_analysis['time_horizons'].items():
            report.append(f"  {horizon_name}:")
            net_exp = horizon_data['net_exposures']
            report.append(f"    Gamma: {net_exp['net_dealer_gamma']:,.0f}")
            report.append(f"    Vanna: {net_exp['net_dealer_vanna']:,.0f}")
            report.append(f"    Charm: {net_exp['net_dealer_charm']:,.0f}")
            
            # Flow pressure
            flow = horizon_data['flow_pressure']
            report.append(f"    P/C Volume Ratio: {flow['put_call_volume_ratio']:.2f}")
            report.append(f"    Vanna Bias: {flow['vanna_directional_bias']}")
            report.append("")
        
        return "\n".join(report) 