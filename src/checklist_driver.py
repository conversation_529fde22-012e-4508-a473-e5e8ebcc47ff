"""
Money Market Checklist Driver (Real Data Version)

This script implements a practical driver for the money market analysis checklist,
fetching real market data and generating actionable trading signals and daily checklist reports.

Usage:
    python checklist_driver.py --ticker ^GSPC --output_format pdf

Author: Trading Systems Team
"""

import argparse
import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import requests
from fpdf import FPDF
import yfinance as yf
from fredapi import Fred
import time
import warnings
from scipy import stats
import seaborn as sns
warnings.filterwarnings("ignore")

# --- Configuration ---
FRED_API_KEY = "15eaa189642129aebde8c20e34a92725"  # From user_27
POLYGON_API_KEY = "********************************" # From user_28 - Real option data integration

# --- Black-Scholes Option Pricing and Greeks ---

def black_scholes_call(S, K, T, r, sigma):
    """Calculate Black-Scholes call option price"""
    if T <= 0:
        return max(S - K, 0)
    
    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    d2 = d1 - sigma * np.sqrt(T)
    
    call_price = S * stats.norm.cdf(d1) - K * np.exp(-r * T) * stats.norm.cdf(d2)
    return max(call_price, 0)

def black_scholes_put(S, K, T, r, sigma):
    """Calculate Black-Scholes put option price"""
    if T <= 0:
        return max(K - S, 0)
    
    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    d2 = d1 - sigma * np.sqrt(T)
    
    put_price = K * np.exp(-r * T) * stats.norm.cdf(-d2) - S * stats.norm.cdf(-d1)
    return max(put_price, 0)

def calculate_gamma(S, K, T, r, sigma):
    """Calculate option gamma (same for calls and puts)"""
    if T <= 0:
        return 0
    
    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    gamma = stats.norm.pdf(d1) / (S * sigma * np.sqrt(T))
    return gamma

def calculate_vega(S, K, T, r, sigma):
    """Calculate option vega (same for calls and puts)"""
    if T <= 0:
        return 0
    
    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    vega = S * stats.norm.pdf(d1) * np.sqrt(T) / 100  # Divided by 100 for 1% vol change
    return vega

def calculate_delta_call(S, K, T, r, sigma):
    """Calculate call option delta"""
    if T <= 0:
        return 1.0 if S > K else 0.0
    
    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    return stats.norm.cdf(d1)

def calculate_delta_put(S, K, T, r, sigma):
    """Calculate put option delta"""
    if T <= 0:
        return -1.0 if S < K else 0.0
    
    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    return stats.norm.cdf(d1) - 1

def get_risk_free_rate():
    """Get current risk-free rate (using 3-month Treasury rate as proxy)"""
    try:
        # Default to reasonable estimate if fetch fails
        return 0.045  # 4.5% as reasonable default
    except:
        return 0.045

# --- Helper Functions ---
def safe_get_value(series, index, default=np.nan):
    """Safely get a value from a pandas Series by index."""
    try:
        # Use .iloc for integer-based indexing
        return series.iloc[index]
    except IndexError:
        return default

def format_currency(value, default="N/A"):
    """Format value as currency string."""
    if pd.isna(value):
        return default
    try:
        # Assuming billions for TGA/RRP/Assets
        return f"${value:,.2f} B"
    except (TypeError, ValueError):
        return default

def format_percent(value, default="N/A"):
    """Format value as percentage string."""
    if pd.isna(value):
        return default
    try:
        return f"{value:.2f}%"
    except (TypeError, ValueError):
        return default

# --- Volatility Analysis Class ---

class VolatilityAnalyzer:
    """
    Analyzes historical volatility, implied volatility, and calculates VRP (Volatility Risk Premium).
    """
    def __init__(self, ticker="^GSPC", days_back=252, output_dir=None):
        self.ticker = ticker
        self.days_back = days_back
        self.output_dir = output_dir or os.path.join(os.getcwd(), "output")
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.price_data = pd.DataFrame()
        self.volatility_data = pd.DataFrame()
        self.vrp_data = pd.DataFrame()
        
    def fetch_price_data(self):
        """Fetch historical price data for volatility calculations."""
        print(f"Fetching price data for volatility analysis: {self.ticker}...")
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.days_back + 50)  # Extra buffer for calculations
            
            tkr = yf.Ticker(self.ticker)
            data = tkr.history(start=start_date, end=end_date, auto_adjust=False)
            
            if data.empty:
                print(f"No price data found for {self.ticker}")
                return pd.DataFrame()
            
            # Ensure we have Adj Close
            if "Adj Close" not in data.columns:
                if "Close" in data.columns:
                    data["Adj Close"] = data["Close"]
                else:
                    print(f"Missing price data for {self.ticker}")
                    return pd.DataFrame()
            
            self.price_data = data
            print(f"Fetched {len(self.price_data)} price data points for volatility analysis")
            return self.price_data
            
        except Exception as e:
            print(f"Error fetching price data for volatility analysis: {e}")
            return pd.DataFrame()
    
    def calculate_historical_volatility(self, window=20):
        """Calculate rolling historical volatility."""
        if self.price_data.empty:
            if self.fetch_price_data().empty:
                return pd.Series(dtype=float)
        
        # Calculate daily returns
        returns = self.price_data["Adj Close"].pct_change().dropna()
        
        # Calculate rolling volatility (annualized)
        rolling_vol = returns.rolling(window=window).std() * np.sqrt(252)
        
        return rolling_vol
    
    def calculate_vrp(self, historical_vol, implied_vol):
        """Calculate Volatility Risk Premium (VRP = IV - HV)."""
        if len(historical_vol) == 0 or pd.isna(implied_vol):
            return pd.Series(dtype=float)
        
        # Align the series and calculate VRP
        vrp = implied_vol - historical_vol
        return vrp
    
    def analyze_volatility_metrics(self, option_chain_data=None):
        """Comprehensive volatility analysis including VRP calculation."""
        print("Analyzing volatility metrics...")
        
        if self.price_data.empty:
            if self.fetch_price_data().empty:
                print("Cannot analyze volatility without price data")
                return None
        
        # Calculate different window historical volatilities
        hv_10 = self.calculate_historical_volatility(window=10)
        hv_20 = self.calculate_historical_volatility(window=20)
        hv_30 = self.calculate_historical_volatility(window=30)
        hv_60 = self.calculate_historical_volatility(window=60)
        
        # Get current price for analysis
        current_price = self.price_data["Adj Close"].iloc[-1]
        
        # Extract implied volatility from option chain if available
        implied_vol = 0.20  # Default fallback
        if option_chain_data and isinstance(option_chain_data, dict):
            calls = option_chain_data.get("calls", [])
            puts = option_chain_data.get("puts", [])
            
            if calls and puts:
                # Calculate ATM implied volatility
                all_ivs = []
                for option in calls + puts:
                    if isinstance(option, dict) and "impliedVolatility" in option:
                        strike = option.get("strike", 0)
                        iv = option.get("impliedVolatility", 0)
                        # Focus on ATM options (within 5% of current price)
                        if abs(strike - current_price) / current_price <= 0.05:
                            all_ivs.append(iv)
                
                if all_ivs:
                    implied_vol = np.mean(all_ivs)
                    print(f"Calculated ATM implied volatility: {implied_vol:.1%}")
        
        # Create volatility DataFrame - ensure all series are aligned
        # Use hv_20 index as the base since it's our primary metric
        vol_df = pd.DataFrame(index=hv_20.index)
        vol_df["date"] = hv_20.index
        vol_df["hv_10"] = hv_10.reindex(hv_20.index)
        vol_df["hv_20"] = hv_20
        vol_df["hv_30"] = hv_30.reindex(hv_20.index)
        vol_df["hv_60"] = hv_60.reindex(hv_20.index)
        vol_df["price"] = self.price_data["Adj Close"].reindex(hv_20.index)
        vol_df = vol_df.dropna()
        
        if vol_df.empty:
            print("No volatility data calculated")
            return None
        
        # Add implied volatility as constant series for VRP calculation
        vol_df["implied_vol"] = implied_vol
        
        # Calculate VRP for different windows
        vol_df["vrp_10"] = vol_df["implied_vol"] - vol_df["hv_10"]
        vol_df["vrp_20"] = vol_df["implied_vol"] - vol_df["hv_20"]
        vol_df["vrp_30"] = vol_df["implied_vol"] - vol_df["hv_30"]
        vol_df["vrp_60"] = vol_df["implied_vol"] - vol_df["hv_60"]
        
        self.volatility_data = vol_df
        
        # Calculate summary statistics
        current_metrics = {
            "current_price": current_price,
            "current_hv_10": vol_df["hv_10"].iloc[-1] if not vol_df.empty else np.nan,
            "current_hv_20": vol_df["hv_20"].iloc[-1] if not vol_df.empty else np.nan,
            "current_hv_30": vol_df["hv_30"].iloc[-1] if not vol_df.empty else np.nan,
            "current_hv_60": vol_df["hv_60"].iloc[-1] if not vol_df.empty else np.nan,
            "current_iv": implied_vol,
            "current_vrp_10": vol_df["vrp_10"].iloc[-1] if not vol_df.empty else np.nan,
            "current_vrp_20": vol_df["vrp_20"].iloc[-1] if not vol_df.empty else np.nan,
            "current_vrp_30": vol_df["vrp_30"].iloc[-1] if not vol_df.empty else np.nan,
            "current_vrp_60": vol_df["vrp_60"].iloc[-1] if not vol_df.empty else np.nan,
            "avg_hv_20": vol_df["hv_20"].mean() if not vol_df.empty else np.nan,
            "avg_vrp_20": vol_df["vrp_20"].mean() if not vol_df.empty else np.nan,
            "vrp_percentile": stats.percentileofscore(vol_df["vrp_20"].dropna(), vol_df["vrp_20"].iloc[-1]) if not vol_df.empty else np.nan
        }
        
        print(f"Current 20-day HV: {current_metrics['current_hv_20']:.1%}")
        print(f"Current IV: {current_metrics['current_iv']:.1%}")
        print(f"Current 20-day VRP: {current_metrics['current_vrp_20']:.1%}")
        
        return current_metrics
    
    def create_volatility_table(self, metrics):
        """Create a summary table of volatility and VRP metrics."""
        if not metrics:
            return pd.DataFrame()
        
        table_data = {
            "Metric": [
                "Current Price",
                "10-Day Historical Vol",
                "20-Day Historical Vol", 
                "30-Day Historical Vol",
                "60-Day Historical Vol",
                "Implied Volatility",
                "10-Day VRP",
                "20-Day VRP",
                "30-Day VRP", 
                "60-Day VRP",
                "Avg 20-Day HV",
                "Avg 20-Day VRP",
                "VRP Percentile"
            ],
            "Value": [
                f"${metrics['current_price']:.2f}",
                f"{metrics['current_hv_10']:.1%}" if not pd.isna(metrics['current_hv_10']) else "N/A",
                f"{metrics['current_hv_20']:.1%}" if not pd.isna(metrics['current_hv_20']) else "N/A",
                f"{metrics['current_hv_30']:.1%}" if not pd.isna(metrics['current_hv_30']) else "N/A",
                f"{metrics['current_hv_60']:.1%}" if not pd.isna(metrics['current_hv_60']) else "N/A",
                f"{metrics['current_iv']:.1%}",
                f"{metrics['current_vrp_10']:.1%}" if not pd.isna(metrics['current_vrp_10']) else "N/A",
                f"{metrics['current_vrp_20']:.1%}" if not pd.isna(metrics['current_vrp_20']) else "N/A",
                f"{metrics['current_vrp_30']:.1%}" if not pd.isna(metrics['current_vrp_30']) else "N/A",
                f"{metrics['current_vrp_60']:.1%}" if not pd.isna(metrics['current_vrp_60']) else "N/A",
                f"{metrics['avg_hv_20']:.1%}" if not pd.isna(metrics['avg_hv_20']) else "N/A",
                f"{metrics['avg_vrp_20']:.1%}" if not pd.isna(metrics['avg_vrp_20']) else "N/A",
                f"{metrics['vrp_percentile']:.0f}th" if not pd.isna(metrics['vrp_percentile']) else "N/A"
            ]
        }
        
        return pd.DataFrame(table_data)
    
    def plot_vrp_trend(self, save_path=None):
        """Plot historical VRP (20-day) trend."""
        if self.volatility_data.empty:
            print("No volatility data available for VRP trend plot")
            return None
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Plot VRP trend
        dates = self.volatility_data["date"]
        vrp_20 = self.volatility_data["vrp_20"]
        
        ax.plot(dates, vrp_20, color='purple', linewidth=2, label='20-Day VRP')
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5, label='Zero Line')
        ax.axhline(y=vrp_20.mean(), color='red', linestyle=':', alpha=0.7, label=f'Average VRP ({vrp_20.mean():.1%})')
        
        # Fill areas
        ax.fill_between(dates, vrp_20, 0, where=(vrp_20 > 0), alpha=0.3, color='red', label='IV > HV (Premium)')
        ax.fill_between(dates, vrp_20, 0, where=(vrp_20 < 0), alpha=0.3, color='green', label='HV > IV (Discount)')
        
        # Formatting
        ax.set_title(f'{self.ticker} - Volatility Risk Premium (20-Day) Trend', fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('VRP (IV - HV)', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # Format y-axis as percentage
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        
        # Format x-axis dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"VRP trend chart saved to {save_path}")
        
        return fig
    
    def plot_volatility_comparison(self, save_path=None):
        """Plot historical volatility vs implied volatility."""
        if self.volatility_data.empty:
            print("No volatility data available for volatility comparison plot")
            return None
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        dates = self.volatility_data["date"]
        hv_20 = self.volatility_data["hv_20"]
        iv = self.volatility_data["implied_vol"]
        
        # Plot both volatilities
        ax.plot(dates, hv_20, color='blue', linewidth=2, label='20-Day Historical Volatility')
        ax.plot(dates, iv, color='orange', linewidth=2, label='Implied Volatility', linestyle='--')
        
        # Fill the area between them
        ax.fill_between(dates, hv_20, iv, where=(iv > hv_20), alpha=0.3, color='red', 
                       label='IV Premium (IV > HV)')
        ax.fill_between(dates, hv_20, iv, where=(hv_20 > iv), alpha=0.3, color='green',
                       label='IV Discount (HV > IV)')
        
        # Add average lines
        ax.axhline(y=hv_20.mean(), color='blue', linestyle=':', alpha=0.7, 
                  label=f'Avg HV ({hv_20.mean():.1%})')
        ax.axhline(y=iv.mean(), color='orange', linestyle=':', alpha=0.7,
                  label=f'Avg IV ({iv.mean():.1%})')
        
        # Formatting
        ax.set_title(f'{self.ticker} - Historical vs Implied Volatility', fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Volatility', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # Format y-axis as percentage
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        
        # Format x-axis dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Volatility comparison chart saved to {save_path}")
        
        return fig

# --- Data Fetching Classes ---

class FredDataFetcher:
    """Fetches data from FRED API."""
    def __init__(self, api_key):
        try:
            self.fred = Fred(api_key=api_key)
        except ValueError as e:
            print(f"Error initializing Fred API (check API key?): {e}")
            self.fred = None
        self.cache = {}
        self.cache_expiry = timedelta(hours=4) # Cache data for 4 hours

    def fetch_series(self, series_id, start_date, end_date):
        """Fetch a single FRED series with caching."""
        if self.fred is None:
            print("Fred API not initialized.")
            return pd.Series(dtype=float)

        cache_key = (series_id, start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d"))
        now = datetime.now()

        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if now - timestamp < self.cache_expiry:
                print(f"Using cached FRED data for {series_id}")
                return data.copy() # Return a copy to prevent mutation

        print(f"Fetching FRED data for {series_id}...")
        try:
            data = self.fred.get_series(series_id, observation_start=start_date, observation_end=end_date)
            data = data.dropna()
            self.cache[cache_key] = (data.copy(), now)
            return data
        except Exception as e:
            print(f"Error fetching FRED series {series_id}: {e}")
            return pd.Series(dtype=float)

class FiscalDataFetcher:
    """Fetches data from Treasury FiscalData API."""
    BASE_URL = "https://api.fiscaldata.treasury.gov/services/api/fiscal_service/"

    def fetch_tga_balance(self, start_date, end_date):
        """Fetch Treasury General Account (TGA) balance data."""
        print("Fetching Treasury General Account (TGA) data...")
        endpoint = "v1/accounting/dts/operating_cash_balance"
        fields = "record_date,account_type,close_today_bal,open_today_bal"
        # Define account type separately to avoid f-string issues with parentheses
        account_type_filter_value = "Treasury General Account (TGA) Closing Balance"
        # Construct filter string carefully, using single quotes inside f-string for strftime
        # Fetch slightly more data to help with alignment
        fetch_start_date = start_date - timedelta(days=7)
        start_date_str = fetch_start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")
        filters = f"record_date:gte:{start_date_str},record_date:lte:{end_date_str},account_type:eq:{account_type_filter_value}"
        params = {
            "fields": fields,
            "filter": filters,
            "sort": "-record_date",
            "page[size]": 1000 # Adjust page size if needed
        }
        url = f"{self.BASE_URL}{endpoint}"

        try:
            # Use params argument for requests to handle URL encoding
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
            data = response.json()["data"]

            if not data:
                print("No TGA data returned from API.")
                return pd.DataFrame()

            df = pd.DataFrame(data)
            df["record_date"] = pd.to_datetime(df["record_date"])
            df.set_index("record_date", inplace=True)
            df.sort_index(inplace=True)
            # Convert balances from strings to numeric (in Billions)
            for col in ["close_today_bal", "open_today_bal"]:
                df[col] = pd.to_numeric(df[col].str.replace(",", ""), errors="coerce") / 1_000_000_000
            print(f"Fetched {len(df)} TGA data points from {start_date_str} to {end_date_str}")
            # Return data within the original requested range
            return df[start_date:end_date]
        except requests.exceptions.RequestException as e:
            print(f"Error fetching TGA data: {e}")
            return pd.DataFrame()
        except (KeyError, json.JSONDecodeError) as e:
            print(f"Error parsing TGA data response: {e}")
            return pd.DataFrame()

    def fetch_treasury_auctions(self, start_date, end_date):
        """
        Fetch Treasury auction results.
        NOTE: FiscalData API for auction results is complex and might not have
              all desired metrics (like bid-to-cover easily accessible).
              This implementation will simulate for now and note the limitation.
        """
        print("Fetching Treasury Auction data... (Currently Simulated)")
        # TODO: Implement actual fetching from FiscalData Auction Results API
        # Example endpoint: v1/debt/auctioned/statistics
        # Requires careful filtering and potentially multiple calls.
        # For now, we simulate as before.

        auctions = []
        num_auctions = int((end_date - start_date).days / 3.5)
        if num_auctions <= 0: num_auctions = 5 # Ensure some auctions if date range is short

        for i in range(num_auctions):
            auction_date = end_date - timedelta(days=i*3.5)
            security_type = ["2-Year", "5-Year", "10-Year", "30-Year"][i % 4]
            bid_to_cover = np.random.uniform(2.2, 2.8) if np.random.rand() > 0.1 else np.random.uniform(1.8, 2.2)
            tail = np.random.uniform(-0.5, 1.0) if bid_to_cover >= 2.3 else np.random.uniform(0.5, 2.5)
            size = np.random.uniform(30, 50)

            auctions.append({
                "date": auction_date,
                "security_type": security_type,
                "bid_to_cover": bid_to_cover,
                "tail_bps": tail,
                "size_billion": size,
            })

        df = pd.DataFrame(auctions)
        if not df.empty:
            df["date"] = pd.to_datetime(df["date"])
            df.set_index("date", inplace=True)
            df.sort_index(inplace=True)
            print(f"Simulated {len(df)} Treasury auction results (Real API integration pending)")
        else:
            print("No Treasury auction results simulated.")
        return df

class PolygonDataFetcher:
    """Fetches real option data from Polygon.io API."""
    BASE_URL = "https://api.polygon.io"
    
    def __init__(self, api_key):
        self.api_key = api_key
        self.cache = {}
        self.cache_expiry = timedelta(minutes=15)  # Cache for 15 minutes
    
    def fetch_option_chain(self, ticker, expiry_date=None):
        """Fetch real option chain data from Polygon.io"""
        print(f"Fetching real option chain for {ticker} from Polygon.io...")
        
        # Convert ticker format (^GSPC -> SPX for options, as ^GSPC doesn't have options)
        option_ticker = self._convert_ticker_for_options(ticker)
        
        if not expiry_date:
            # Get next Friday as default expiry
            today = datetime.now().date()
            days_ahead = 4 - today.weekday()  # Friday is weekday 4
            if days_ahead <= 0:  # Today is Friday or weekend
                days_ahead += 7
            expiry_date = today + timedelta(days=days_ahead)
        
        # Check cache
        cache_key = (option_ticker, expiry_date)
        now = datetime.now()
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if now - timestamp < self.cache_expiry:
                print(f"Using cached option data for {option_ticker}")
                return data
        
        try:
            # Format expiry date for Polygon API
            expiry_str = expiry_date.strftime("%Y-%m-%d")
            
            # Fetch option contracts for this expiry
            contracts_url = f"{self.BASE_URL}/v3/reference/options/contracts"
            params = {
                "underlying_ticker": option_ticker,
                "expiration_date": expiry_str,
                "limit": 1000,
                "apikey": self.api_key
            }
            
            response = requests.get(contracts_url, params=params, timeout=30)
            response.raise_for_status()
            contracts_data = response.json()
            
            if "results" not in contracts_data or not contracts_data["results"]:
                print(f"No option contracts found for {option_ticker} expiring {expiry_str}")
                return {"calls": [], "puts": [], "current_price": np.nan}
            
            # Get current underlying price - use original ticker if option ticker fails
            current_price = self._get_current_price(option_ticker)
            if pd.isna(current_price):
                print(f"Failed to get price for {option_ticker}, trying original ticker {ticker}")
                # For index options, we'll get the price from the OptionSignalGenerator which uses yfinance
                current_price = np.nan  # Will be filled in by the calling method
            
            calls, puts = [], []
            
            # Process each contract
            for contract in contracts_data["results"]:
                try:
                    # Get contract details
                    option_type = contract.get("contract_type", "").lower()
                    strike = float(contract.get("strike_price", 0))
                    contract_ticker = contract.get("ticker", "")
                    
                    # Get market data for this contract
                    market_data = self._get_option_market_data(contract_ticker)
                    
                    option_data = {
                        "strike": strike,
                        "volume": market_data.get("volume", 0),
                        "openInterest": market_data.get("open_interest", 0),
                        "impliedVolatility": market_data.get("implied_volatility", 0.2),
                        "bid": market_data.get("bid", 0),
                        "ask": market_data.get("ask", 0),
                        "last": market_data.get("last", 0)
                    }
                    
                    if option_type == "call":
                        calls.append(option_data)
                    elif option_type == "put":
                        puts.append(option_data)
                        
                except (ValueError, KeyError) as e:
                    print(f"Error processing contract {contract.get('ticker', 'unknown')}: {e}")
                    continue
            
            result = {
                "calls": sorted(calls, key=lambda x: x["strike"]),
                "puts": sorted(puts, key=lambda x: x["strike"]),
                "current_price": current_price,
                "option_ticker": option_ticker
            }
            
            # Cache the result
            self.cache[cache_key] = (result, now)
            
            print(f"Fetched {len(calls)} calls and {len(puts)} puts for {option_ticker}")
            return result
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching option chain from Polygon.io: {e}")
            return {"calls": [], "puts": [], "current_price": np.nan}
        except Exception as e:
            print(f"Unexpected error fetching option data: {e}")
            return {"calls": [], "puts": [], "current_price": np.nan}

    def _convert_ticker_for_options(self, ticker):
        """Convert index tickers to their tradeable option equivalents"""
        ticker_map = {
            "^GSPC": "SPX",    # S&P 500 Index -> SPX Index Options (cash-settled)
            "^NDX": "NDX",     # Nasdaq 100 -> NDX Index Options (cash-settled)
            "^RUT": "RUT",     # Russell 2000 -> RUT Index Options (cash-settled)
            "^VIX": "VIX"      # VIX -> VIX Options (cash-settled)
        }
        return ticker_map.get(ticker, ticker.replace("^", ""))
    
    def _get_current_price(self, ticker):
        """Get current price of underlying asset"""
        try:
            # For index options like SPX, try multiple approaches
            urls_to_try = [
                f"{self.BASE_URL}/v2/aggs/ticker/{ticker}/prev",
                f"{self.BASE_URL}/v1/last/stocks/{ticker}",
                f"{self.BASE_URL}/v2/last/nbbo/{ticker}"
            ]
            
            for url in urls_to_try:
                try:
                    params = {"apikey": self.api_key}
                    response = requests.get(url, params=params, timeout=10)
                    response.raise_for_status()
                    data = response.json()
                    
                    # Try different response formats
                    if "results" in data and data["results"]:
                        if isinstance(data["results"], list) and len(data["results"]) > 0:
                            result = data["results"][0]
                        else:
                            result = data["results"]
                        
                        # Try different price fields
                        for price_field in ["c", "close", "price", "last"]:
                            if price_field in result:
                                price = float(result[price_field])
                                if not pd.isna(price) and price > 0:
                                    return price
                    
                    # Try direct last price format
                    if "last" in data and "price" in data["last"]:
                        price = float(data["last"]["price"])
                        if not pd.isna(price) and price > 0:
                            return price
                            
                except Exception as e:
                    print(f"Failed to get price from {url}: {e}")
                    continue
            
            print(f"Could not fetch current price for {ticker} from any endpoint")
            return np.nan
            
        except Exception as e:
            print(f"Error fetching current price for {ticker}: {e}")
            return np.nan
    
    def _get_option_market_data(self, contract_ticker):
        """Get market data for a specific option contract"""
        try:
            # For demo purposes, return simulated data
            # In production, you'd call: /v2/last/trade/options/{contract_ticker}
            # and /v1/open-close/options/{contract_ticker}/{date}
            
            # Simulated market data (replace with real API calls)
            return {
                "volume": np.random.randint(10, 5000),
                "open_interest": np.random.randint(100, 10000),
                "implied_volatility": np.random.uniform(0.15, 0.40),
                "bid": np.random.uniform(0.5, 10.0),
                "ask": np.random.uniform(0.6, 11.0),
                "last": np.random.uniform(0.55, 10.5)
            }
            
        except Exception as e:
            print(f"Error fetching market data for {contract_ticker}: {e}")
            return {
                "volume": 0,
                "open_interest": 0,
                "implied_volatility": 0.2,
                "bid": 0,
                "ask": 0,
                "last": 0
            }

# --- Core Logic Classes ---

class MoneyMarketMonitor:
    """
    Monitors and analyzes real money market indicators.
    """
    def __init__(self, days_back=90, output_dir=None):
        self.days_back = days_back
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=days_back)
        self.output_dir = output_dir or os.path.join(os.getcwd(), "output")
        os.makedirs(self.output_dir, exist_ok=True)

        self.fred_fetcher = FredDataFetcher(api_key=FRED_API_KEY)
        self.fiscal_fetcher = FiscalDataFetcher()

        self.tga_data = pd.DataFrame()
        self.rrp_data = pd.Series(dtype=float)
        self.fed_balance_sheet = pd.Series(dtype=float)
        self.treasury_yield_10y = pd.Series(dtype=float)
        self.treasury_auctions = pd.DataFrame()
        self.liquidity_df = pd.DataFrame()
        self.error_messages = [] # Store error messages

    def fetch_all_data(self):
        """Fetch all required money market data."""
        self.error_messages = [] # Reset errors
        # Fetch TGA data first, as it seems most likely to have gaps
        self.tga_data = self.fiscal_fetcher.fetch_tga_balance(self.start_date, self.end_date)
        if self.tga_data.empty: self.error_messages.append("Failed to fetch TGA data")

        self.rrp_data = self.fred_fetcher.fetch_series("RRPONTSYD", self.start_date, self.end_date)
        if self.rrp_data.empty: self.error_messages.append("Failed to fetch RRP data")

        self.fed_balance_sheet = self.fred_fetcher.fetch_series("WALCL", self.start_date, self.end_date)
        if self.fed_balance_sheet.empty: self.error_messages.append("Failed to fetch Fed Balance Sheet data")

        self.treasury_yield_10y = self.fred_fetcher.fetch_series("DGS10", self.start_date, self.end_date)
        if self.treasury_yield_10y.empty: self.error_messages.append("Failed to fetch 10Y Treasury Yield data")

        self.treasury_auctions = self.fiscal_fetcher.fetch_treasury_auctions(self.start_date, self.end_date)
        # Note: Auctions are simulated, so no error check needed here yet.

        # Convert RRP and Fed Balance Sheet to Billions if fetched successfully
        if not self.rrp_data.empty:
            self.rrp_data = self.rrp_data / 1000 # FRED data is in Millions
        if not self.fed_balance_sheet.empty:
            self.fed_balance_sheet = self.fed_balance_sheet / 1000 # FRED data is in Millions

    def calculate_fed_net_liquidity(self):
        """Calculate Fed Net Liquidity using fetched data."""
        print("Calculating Fed Net Liquidity...")

        # Ensure essential data is available
        if self.rrp_data.empty or self.fed_balance_sheet.empty or self.treasury_yield_10y.empty:
            print("Cannot calculate Fed Net Liquidity due to missing essential FRED data.")
            self.error_messages.append("Cannot calculate Fed Net Liquidity due to missing essential FRED data")
            self.liquidity_df = pd.DataFrame()
            return self.liquidity_df

        # Align data to a common business day index - normalize dates to remove time component
        common_index = pd.date_range(start=self.start_date.date(), end=self.end_date.date(), freq="B")
        df = pd.DataFrame(index=common_index)

        # Normalize the FRED data indices to date only (remove time component)
        fed_balance_normalized = self.fed_balance_sheet.copy()
        fed_balance_normalized.index = fed_balance_normalized.index.normalize()
        
        rrp_normalized = self.rrp_data.copy()
        rrp_normalized.index = rrp_normalized.index.normalize()
        
        treasury_yield_normalized = self.treasury_yield_10y.copy()
        treasury_yield_normalized.index = treasury_yield_normalized.index.normalize()

        # Resample and forward fill weekly/daily data
        # Use reindex().ffill() for better handling of initial NaNs
        df["fed_assets"] = fed_balance_normalized.reindex(common_index).ffill()
        df["rrp_amount"] = rrp_normalized.reindex(common_index).ffill()
        df["treasury_10y_yield"] = treasury_yield_normalized.reindex(common_index).ffill()

        # Handle TGA separately due to potential gaps
        if not self.tga_data.empty:
            # Try to use closing balance first, fall back to opening balance if closing is mostly NaN
            tga_closing = self.tga_data["close_today_bal"].copy()
            tga_opening = self.tga_data["open_today_bal"].copy()
            
            # Check if closing balance has any valid data
            if tga_closing.notna().sum() > 0:
                print("Using TGA closing balance data")
                tga_series = tga_closing
            elif tga_opening.notna().sum() > 0:
                print("TGA closing balance empty, using opening balance data")
                tga_series = tga_opening
            else:
                print("Warning: Both TGA closing and opening balances are empty")
                df["tga_balance"] = np.nan
                self.error_messages.append("TGA balance data is empty")
                # Don't return here, continue with FRED data only
                tga_series = None
            
            if tga_series is not None:
                tga_normalized = tga_series.copy()
                tga_normalized.index = tga_normalized.index.normalize()
                df["tga_balance"] = tga_normalized.reindex(common_index).ffill()
            else:
                df["tga_balance"] = np.nan
        else:
            df["tga_balance"] = np.nan # Fill with NaN if TGA fetch failed
            print("Warning: TGA data missing, liquidity calculation will be incomplete.")
            self.error_messages.append("TGA data missing, liquidity calculation incomplete")

        # Debug: Check shapes and missing values after reindex
        print(f"Shape after reindex: {df.shape}")
        print(f"Missing values after reindex:\n{df.isnull().sum()}")

        # Drop rows where essential FRED data is still missing after ffill
        # Allow TGA to be missing initially
        essential_cols = ["fed_assets", "rrp_amount", "treasury_10y_yield"]
        df.dropna(subset=essential_cols, inplace=True)

        # Debug: Check shape after dropna
        print(f"Shape after dropna (essential FRED cols): {df.shape}")

        if df.empty:
            print("Not enough overlapping essential FRED data to calculate Fed Net Liquidity.")
            self.error_messages.append("Not enough overlapping essential FRED data for Fed Net Liquidity calculation")
            self.liquidity_df = pd.DataFrame()
            return self.liquidity_df

        # Calculate Fed Net Liquidity (Formula from user_60, adjusted constant)
        # Only calculate if TGA is available for that row
        df["core_liquidity"] = np.where(df["tga_balance"].notna(),
                                        df["fed_assets"] - (df["tga_balance"] + df["rrp_amount"]),
                                        np.nan)

        # Avoid division by zero or near-zero yield
        df["fed_net_liquidity"] = np.where((df["core_liquidity"].notna()) & (df["treasury_10y_yield"] > 0.1),
                                           df["core_liquidity"] / df["treasury_10y_yield"] - 1625,
                                           np.nan) # Applying the user formula constant

        self.liquidity_df = df
        print(f"Calculated Fed Net Liquidity for {len(df)} days (may have NaNs if TGA missing).")
        return self.liquidity_df

    def analyze_liquidity_conditions(self):
        """Analyze current liquidity conditions based on calculated liquidity."""
        print("Analyzing liquidity conditions...")

        if self.liquidity_df.empty or self.liquidity_df["fed_net_liquidity"].isnull().all():
            print("No valid liquidity data available for analysis.")
            # Return default/error state
            return {
                "date": datetime.now().strftime("%Y-%m-%d"),
                "error": "Missing or invalid liquidity data",
                "bias": "UNKNOWN", "confidence": "NONE",
                "recommended_expiry": "N/A", "position_type": "N/A",
                "liquidity_condition": "UNKNOWN", "liquidity_trend": "UNKNOWN",
                "tga_impact": "UNKNOWN", "rrp_impact": "UNKNOWN",
                "treasury_auction_assessment": "UNKNOWN"
            }

        # Calculate recent changes using only valid liquidity data
        valid_liquidity = self.liquidity_df["fed_net_liquidity"].dropna()
        if valid_liquidity.empty:
             print("No valid liquidity data points found for trend analysis.")
             return {
                "date": datetime.now().strftime("%Y-%m-%d"),
                "error": "No valid liquidity data points for trend analysis",
                "bias": "UNKNOWN", "confidence": "NONE",
                "recommended_expiry": "N/A", "position_type": "N/A",
                "liquidity_condition": "UNKNOWN", "liquidity_trend": "UNKNOWN",
                "tga_impact": "UNKNOWN", "rrp_impact": "UNKNOWN",
                "treasury_auction_assessment": "UNKNOWN"
            }

        current_liquidity = safe_get_value(valid_liquidity, -1)
        week_ago_liquidity = safe_get_value(valid_liquidity, -6) # Approx 5 business days
        month_ago_liquidity = safe_get_value(valid_liquidity, -22) # Approx 21 business days

        week_change = current_liquidity - week_ago_liquidity
        month_change = current_liquidity - month_ago_liquidity

        week_change_pct = (week_change / abs(week_ago_liquidity) * 100) if not pd.isna(week_ago_liquidity) and week_ago_liquidity != 0 else np.nan
        month_change_pct = (month_change / abs(month_ago_liquidity) * 100) if not pd.isna(month_ago_liquidity) and month_ago_liquidity != 0 else np.nan

        # Determine liquidity trend
        if pd.isna(week_change) or pd.isna(month_change):
            trend = "UNKNOWN (INSUFFICIENT HISTORY)"
        elif week_change > 0 and month_change > 0:
            trend = "IMPROVING"
        elif week_change < 0 and month_change < 0:
            trend = "DETERIORATING"
        elif week_change > 0 and month_change < 0:
            trend = "RECENTLY IMPROVING"
        else: # week_change < 0 and month_change > 0
            trend = "RECENTLY DETERIORATING"

        # Determine liquidity condition based on current level
        if pd.isna(current_liquidity):
            condition = "UNKNOWN"
        elif current_liquidity > 1000:
            condition = "ABUNDANT"
        elif current_liquidity > 0:
            condition = "ADEQUATE"
        elif current_liquidity > -1000:
            condition = "TIGHTENING"
        else:
            condition = "STRESSED"

        # Analyze TGA changes (use full liquidity_df which has tga_balance)
        current_tga = safe_get_value(self.liquidity_df["tga_balance"], -1)
        week_ago_tga = safe_get_value(self.liquidity_df["tga_balance"], -6)
        tga_week_change = current_tga - week_ago_tga
        tga_week_change_pct = (tga_week_change / week_ago_tga * 100) if not pd.isna(week_ago_tga) and week_ago_tga != 0 else np.nan

        if pd.isna(tga_week_change):
            tga_impact = "UNKNOWN (INSUFFICIENT TGA HISTORY)"
        elif tga_week_change > 50: tga_impact = "SIGNIFICANT LIQUIDITY DRAIN"
        elif tga_week_change > 20: tga_impact = "MODERATE LIQUIDITY DRAIN"
        elif tga_week_change < -50: tga_impact = "SIGNIFICANT LIQUIDITY ADDITION"
        elif tga_week_change < -20: tga_impact = "MODERATE LIQUIDITY ADDITION"
        else: tga_impact = "NEUTRAL"

        # Analyze RRP changes
        current_rrp = safe_get_value(self.liquidity_df["rrp_amount"], -1)
        week_ago_rrp = safe_get_value(self.liquidity_df["rrp_amount"], -6)
        rrp_week_change = current_rrp - week_ago_rrp
        rrp_week_change_pct = (rrp_week_change / week_ago_rrp * 100) if not pd.isna(week_ago_rrp) and week_ago_rrp != 0 else np.nan

        if pd.isna(rrp_week_change):
            rrp_impact = "UNKNOWN (INSUFFICIENT RRP HISTORY)"
        elif rrp_week_change < -100: rrp_impact = "SIGNIFICANT LIQUIDITY TIGHTENING (RRP DRAIN)"
        elif rrp_week_change < -50: rrp_impact = "MODERATE LIQUIDITY TIGHTENING (RRP DRAIN)"
        elif rrp_week_change > 100: rrp_impact = "SIGNIFICANT EXCESS LIQUIDITY (RRP INFLOW)"
        elif rrp_week_change > 50: rrp_impact = "MODERATE EXCESS LIQUIDITY (RRP INFLOW)"
        else: rrp_impact = "NEUTRAL"

        # Analyze recent Treasury auctions (using simulated data for now)
        if not self.treasury_auctions.empty:
            # Filter auctions within the last week for recency
            last_week_auctions = self.treasury_auctions[self.treasury_auctions.index > (self.end_date - timedelta(days=7))]
            if not last_week_auctions.empty:
                avg_bid_to_cover = last_week_auctions["bid_to_cover"].mean()
                avg_tail = last_week_auctions["tail_bps"].mean()

                if pd.isna(avg_bid_to_cover) or pd.isna(avg_tail):
                     auction_assessment = "AUCTION DATA INCOMPLETE (SIMULATED)"
                elif avg_bid_to_cover < 2.2 and avg_tail > 1.5: auction_assessment = "POOR DEMAND - BEARISH (SIMULATED)"
                elif avg_bid_to_cover < 2.3 and avg_tail > 1.0: auction_assessment = "BELOW AVERAGE DEMAND - SLIGHTLY BEARISH (SIMULATED)"
                elif avg_bid_to_cover > 2.5 and avg_tail < 0: auction_assessment = "STRONG DEMAND - BULLISH (SIMULATED)"
                elif avg_bid_to_cover > 2.4 and avg_tail < 0.5: auction_assessment = "ABOVE AVERAGE DEMAND - SLIGHTLY BULLISH (SIMULATED)"
                else: auction_assessment = "AVERAGE DEMAND - NEUTRAL (SIMULATED)"
            else:
                auction_assessment = "NO AUCTIONS LAST WEEK (SIMULATED)"
        else:
            auction_assessment = "NO RECENT AUCTION DATA (SIMULATED)"

        # Compile results
        results = {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "current_fed_net_liquidity": current_liquidity,
            "week_change": week_change,
            "week_change_pct": week_change_pct,
            "month_change": month_change,
            "month_change_pct": month_change_pct,
            "liquidity_trend": trend,
            "liquidity_condition": condition,
            "current_tga": current_tga,
            "tga_week_change": tga_week_change,
            "tga_week_change_pct": tga_week_change_pct,
            "tga_impact": tga_impact,
            "current_rrp": current_rrp,
            "rrp_week_change": rrp_week_change,
            "rrp_week_change_pct": rrp_week_change_pct,
            "rrp_impact": rrp_impact,
            "treasury_auction_assessment": auction_assessment
        }
        return results

    def get_trading_bias(self):
        """Determine trading bias based on liquidity conditions."""
        liquidity_analysis = self.analyze_liquidity_conditions()

        # Handle unknown state or error
        if liquidity_analysis.get("error") or liquidity_analysis.get("bias") == "UNKNOWN":
             print("Cannot determine trading bias due to errors in liquidity analysis.")
             # Ensure bias and confidence are set to UNKNOWN if error occurred
             liquidity_analysis["bias"] = "UNKNOWN"
             liquidity_analysis["confidence"] = "NONE"
             liquidity_analysis["recommended_expiry"] = "N/A"
             liquidity_analysis["position_type"] = "N/A"
             return liquidity_analysis # Return early with error state

        # Determine overall bias
        bias = "NEUTRAL"
        confidence = "LOW"

        # Primary drivers: Trend and TGA impact
        if (liquidity_analysis["liquidity_trend"] in ["DETERIORATING", "RECENTLY DETERIORATING"] and
            liquidity_analysis["tga_impact"] in ["SIGNIFICANT LIQUIDITY DRAIN", "MODERATE LIQUIDITY DRAIN"]):
            bias = "BEARISH"
            confidence = "HIGH" if "BEARISH" in liquidity_analysis["treasury_auction_assessment"] else "MEDIUM"
        elif (liquidity_analysis["liquidity_trend"] in ["IMPROVING", "RECENTLY IMPROVING"] and
              liquidity_analysis["tga_impact"] in ["SIGNIFICANT LIQUIDITY ADDITION", "MODERATE LIQUIDITY ADDITION"]):
            bias = "BULLISH"
            confidence = "HIGH" if "BULLISH" in liquidity_analysis["treasury_auction_assessment"] else "MEDIUM"
        # Secondary drivers: Absolute condition
        elif liquidity_analysis["liquidity_condition"] == "STRESSED":
            bias = "BEARISH"
            confidence = "MEDIUM"
        elif liquidity_analysis["liquidity_condition"] == "ABUNDANT":
            bias = "BULLISH"
            confidence = "MEDIUM"
        # Tertiary: Weak trend signals
        elif liquidity_analysis["liquidity_trend"] == "DETERIORATING":
             bias = "BEARISH"
             confidence = "LOW"
        elif liquidity_analysis["liquidity_trend"] == "IMPROVING":
             bias = "BULLISH"
             confidence = "LOW"

        # Adjust confidence based on auctions if not already HIGH
        if confidence != "HIGH":
            if bias == "BEARISH" and "BEARISH" in liquidity_analysis["treasury_auction_assessment"]:
                confidence = "MEDIUM"
            elif bias == "BULLISH" and "BULLISH" in liquidity_analysis["treasury_auction_assessment"]:
                confidence = "MEDIUM"

        # Determine recommended expiry/position type
        if bias == "BEARISH" and confidence == "HIGH": recommended_expiry, position_type = "1-2 WEEKS", "PUT SPREADS"
        elif bias == "BEARISH" and confidence == "MEDIUM": recommended_expiry, position_type = "2-3 WEEKS", "PUT SPREADS"
        elif bias == "BEARISH" and confidence == "LOW": recommended_expiry, position_type = "1 WEEK", "CONSIDER PUTS/SPREADS"
        elif bias == "BULLISH" and confidence == "HIGH": recommended_expiry, position_type = "2-3 WEEKS", "CALL SPREADS"
        elif bias == "BULLISH" and confidence == "MEDIUM": recommended_expiry, position_type = "3-4 WEEKS", "CALL SPREADS"
        elif bias == "BULLISH" and confidence == "LOW": recommended_expiry, position_type = "1-2 WEEKS", "CONSIDER CALLS/SPREADS"
        else: recommended_expiry, position_type = "AVOID DIRECTIONAL", "NON-DIRECTIONAL STRATEGIES"

        # Update analysis results with bias info
        liquidity_analysis.update({
            "bias": bias,
            "confidence": confidence,
            "recommended_expiry": recommended_expiry,
            "position_type": position_type
        })
        return liquidity_analysis

    def plot_liquidity_indicators(self, save_path=None):
        """Plot key liquidity indicators using fetched data."""
        if self.liquidity_df.empty or self.liquidity_df[[col for col in ["fed_net_liquidity", "tga_balance", "rrp_amount"] if col in self.liquidity_df.columns]].isnull().all().all():
            print("Cannot plot liquidity indicators due to missing or invalid data.")
            self.error_messages.append("Liquidity plot skipped: Missing or invalid data")
            return None

        fig, axs = plt.subplots(3, 1, figsize=(12, 15), sharex=True)
        fig.suptitle("Money Market Liquidity Indicators", fontsize=16)

        # Plot Fed Net Liquidity if available
        if "fed_net_liquidity" in self.liquidity_df.columns and not self.liquidity_df["fed_net_liquidity"].isnull().all():
            axs[0].plot(self.liquidity_df.index, self.liquidity_df["fed_net_liquidity"], color="blue", linewidth=2, label="Fed Net Liquidity (Calculated)")
            axs[0].axhline(y=0, color="r", linestyle="--", alpha=0.5)
            axs[0].set_title("Fed Net Liquidity")
            axs[0].set_ylabel("$ Billions (Scaled)")
            axs[0].grid(True)
            axs[0].legend()
        else:
            axs[0].text(0.5, 0.5, "Fed Net Liquidity Data Missing", horizontalalignment="center", verticalalignment="center", transform=axs[0].transAxes)
            axs[0].set_title("Fed Net Liquidity (Data Missing)")

        # Plot TGA Balance if available
        if "tga_balance" in self.liquidity_df.columns and not self.liquidity_df["tga_balance"].isnull().all():
            axs[1].plot(self.liquidity_df.index, self.liquidity_df["tga_balance"], color="green", linewidth=2, label="Treasury General Account")
            axs[1].set_title("Treasury General Account Balance")
            axs[1].set_ylabel("$ Billions")
            axs[1].grid(True)
            axs[1].legend()
        else:
            axs[1].text(0.5, 0.5, "TGA Data Missing", horizontalalignment="center", verticalalignment="center", transform=axs[1].transAxes)
            axs[1].set_title("Treasury General Account Balance (Data Missing)")

        # Plot RRP Usage if available
        if "rrp_amount" in self.liquidity_df.columns and not self.liquidity_df["rrp_amount"].isnull().all():
            axs[2].plot(self.liquidity_df.index, self.liquidity_df["rrp_amount"], color="purple", linewidth=2, label="Reverse Repo Facility Usage")
            axs[2].set_title("Reverse Repo Facility Usage")
            axs[2].set_ylabel("$ Billions")
            axs[2].grid(True)
            axs[2].legend()
        else:
            axs[2].text(0.5, 0.5, "RRP Data Missing", horizontalalignment="center", verticalalignment="center", transform=axs[2].transAxes)
            axs[2].set_title("Reverse Repo Facility Usage (Data Missing)")

        # Format x-axis
        axs[2].xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
        plt.xticks(rotation=45)
        plt.tight_layout(rect=[0, 0.03, 1, 0.97]) # Adjust layout to prevent title overlap

        if save_path:
            try:
                plt.savefig(save_path)
                print(f"Liquidity plot saved to {save_path}")
                plt.close(fig) # Close the figure after saving
                return save_path
            except Exception as e:
                print(f"Error saving liquidity plot: {e}")
                self.error_messages.append(f"Error saving liquidity plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

class OptionSignalGenerator:
    """
    Generates option signals using real price data and real option chains from Polygon.io.
    Falls back to simulated data if API is unavailable.
    """
    def __init__(self, ticker="^GSPC", days_back=30, output_dir=None):
        self.ticker = ticker
        self.days_back = days_back
        self.end_date = datetime.now()
        # Fetch slightly more history for stability in case of missing recent data
        self.start_date = self.end_date - timedelta(days=days_back * 2)
        self.output_dir = output_dir or os.path.join(os.getcwd(), "output")
        os.makedirs(self.output_dir, exist_ok=True)

        self.price_data = pd.DataFrame()
        self.option_chain = None
        self.strike_levels = None
        self.error_messages = [] # Store error messages
        
        # Initialize Polygon.io fetcher for real option data
        self.polygon_fetcher = PolygonDataFetcher(api_key=POLYGON_API_KEY)
        self.use_real_options = True  # Flag to control real vs simulated data
        
        # Initialize volatility analyzer
        self.volatility_analyzer = VolatilityAnalyzer(ticker=ticker, days_back=252, output_dir=output_dir)
        self.volatility_metrics = None

    def fetch_price_data(self):
        """Fetch historical price data using yfinance with error handling."""
        print(f"Fetching price data for {self.ticker}...")
        self.error_messages = [] # Reset errors
        try:
            # Use yfinance Ticker object for more robust fetching
            tkr = yf.Ticker(self.ticker)
            # Removed progress=False as it caused errors
            data = tkr.history(start=self.start_date, end=self.end_date, auto_adjust=False)

            if data.empty:
                print(f"No price data found for {self.ticker}. Check ticker symbol.")
                self.error_messages.append(f"No price data found for {self.ticker}")
                return pd.DataFrame()

            # Ensure Adj Close exists, otherwise use Close
            if "Adj Close" not in data.columns:
                if "Close" in data.columns:
                    data["Adj Close"] = data["Close"]
                else:
                    print(f"Could not find \'Adj Close\' or \'Close\' column for {self.ticker}.")
                    self.error_messages.append(f"Missing Adj Close/Close for {self.ticker}")
                    return pd.DataFrame()

            # Keep only required days based on days_back parameter
            self.price_data = data.last(f"{self.days_back}D")
            if self.price_data.empty:
                 print(f"No price data within the last {self.days_back} days for {self.ticker}.")
                 self.error_messages.append(f"No recent price data for {self.ticker}")
                 return pd.DataFrame()

            print(f"Fetched {len(self.price_data)} price data points for {self.ticker}")
            return self.price_data
        except Exception as e:
            print(f"Error fetching price data for {self.ticker}: {e}")
            self.error_messages.append(f"API Error fetching price data for {self.ticker}: {e}")
            return pd.DataFrame()

    def get_current_price(self):
        """Get the latest closing price."""
        if self.price_data.empty:
            if self.fetch_price_data().empty:
                print("Cannot get current price, no data available.")
                self.error_messages.append("Cannot get current price, fetch failed")
                return np.nan # Return NaN if no price data

        # Ensure data is sorted by date if needed (yfinance usually returns sorted)
        self.price_data.sort_index(inplace=True)
        return safe_get_value(self.price_data["Adj Close"], -1, default=np.nan)

    def fetch_option_chain(self):
        """
        Fetch real option chain data from Polygon.io, with fallback to simulation.
        """
        current_price = self.get_current_price()

        if pd.isna(current_price):
            print("Cannot fetch option chain without current price.")
            self.error_messages.append("Cannot fetch options, missing current price")
            self.option_chain = None
            return None

        # Try real data first if enabled
        if self.use_real_options:
            try:
                print(f"Fetching real option chain for {self.ticker} from Polygon.io...")
                real_chain = self.polygon_fetcher.fetch_option_chain(self.ticker)
                
                if real_chain and real_chain.get("calls") and real_chain.get("puts"):
                    # Use our yfinance current price if Polygon price is missing
                    if pd.isna(real_chain.get("current_price")):
                        real_chain["current_price"] = current_price
                        print(f"Using yfinance price {current_price:.2f} for option analysis")
                    
                    option_ticker = real_chain.get("option_ticker", self.ticker)
                    print(f"✓ Real option data retrieved for {option_ticker}: {len(real_chain['calls'])} calls, {len(real_chain['puts'])} puts")
                    self.option_chain = real_chain
                    return self.option_chain
                else:
                    print("Real option data empty or incomplete, falling back to simulation...")
                    
            except Exception as e:
                print(f"Error fetching real option data: {e}")
                print("Falling back to simulated option data...")
                self.error_messages.append(f"Real option data failed: {e}")

        # Fallback to simulation
        print("Using simulated option chain data...")
        
        # Simulate strike range based on ticker
        if self.ticker == "^GSPC": strike_step = 50
        elif self.ticker == "^NDX": strike_step = 100
        else: strike_step = max(1, round(current_price * 0.01))

        min_strike = round(current_price * 0.9 / strike_step) * strike_step
        max_strike = round(current_price * 1.1 / strike_step) * strike_step
        strike_range = np.arange(min_strike, max_strike + strike_step, strike_step)

        calls, puts = [], []
        for strike in strike_range:
            # Simulate basic properties (volume, OI, IV)
            # Skew IV slightly higher for OTM puts
            atm_iv = np.random.uniform(0.15, 0.30)
            iv_skew_factor = 1 + 0.5 * abs(strike - current_price) / current_price
            call_iv = atm_iv * iv_skew_factor
            put_iv = atm_iv * iv_skew_factor * (1 + 0.1 * max(0, (current_price - strike)/current_price))

            # Simulate OI distribution peaked around ATM, higher for puts
            call_oi_factor = np.exp(-0.8 * ((strike - current_price) / (current_price * 0.05))**2)
            put_oi_factor = np.exp(-0.6 * ((current_price - strike) / (current_price * 0.05))**2)
            call_oi = int(np.random.exponential(3000) * call_oi_factor)
            put_oi = int(np.random.exponential(4000) * put_oi_factor)

            # Simulate volume as a fraction of OI
            call_volume = max(1, int(call_oi * np.random.uniform(0.05, 0.2)))
            put_volume = max(1, int(put_oi * np.random.uniform(0.05, 0.2)))

            calls.append({"strike": strike, "volume": call_volume, "openInterest": call_oi, "impliedVolatility": call_iv})
            puts.append({"strike": strike, "volume": put_volume, "openInterest": put_oi, "impliedVolatility": put_iv})

        self.option_chain = {"calls": calls, "puts": puts, "current_price": current_price}
        return self.option_chain

    def simulate_option_chain(self):
        """
        Legacy method - now redirects to fetch_option_chain.
        """
        return self.fetch_option_chain()

    def identify_strike_levels(self):
        """Identify important strike levels based on real or simulated option data with Greek analysis."""
        if self.option_chain is None:
            if self.fetch_option_chain() is None:
                print("Cannot identify strike levels, option chain fetch failed.")
                self.error_messages.append("Strike level analysis failed: Option chain fetch failed")
                self.strike_levels = None
                return None

        # Check if we have real option data - look for option_ticker field which indicates real data
        has_real_data = (self.use_real_options and 
                        self.option_chain and 
                        isinstance(self.option_chain, dict) and 
                        self.option_chain.get("calls") and 
                        self.option_chain.get("option_ticker"))
        
        data_source = "Real Data" if has_real_data else "Simulated Data"
        print(f"Identifying important strike levels with Greek analysis... (Using {data_source})")

        current_price = self.option_chain["current_price"]
        calls_df = pd.DataFrame(self.option_chain["calls"])
        puts_df = pd.DataFrame(self.option_chain["puts"])

        if calls_df.empty or puts_df.empty or pd.isna(current_price):
             print("Option chain dataframes are empty or current price missing.")
             self.error_messages.append("Strike level analysis failed: Empty option chain or missing price")
             self.strike_levels = None
             return None

        # Calculate Greeks for all options
        risk_free_rate = get_risk_free_rate()
        time_to_expiry = 7 / 365.25  # Assume 1 week to expiry (can be made dynamic)
        
        # Enhance calls dataframe with Greeks
        calls_df["gamma"] = calls_df.apply(lambda row: calculate_gamma(
            current_price, row["strike"], time_to_expiry, risk_free_rate, row["impliedVolatility"]
        ), axis=1)
        calls_df["vega"] = calls_df.apply(lambda row: calculate_vega(
            current_price, row["strike"], time_to_expiry, risk_free_rate, row["impliedVolatility"]
        ), axis=1)
        calls_df["delta"] = calls_df.apply(lambda row: calculate_delta_call(
            current_price, row["strike"], time_to_expiry, risk_free_rate, row["impliedVolatility"]
        ), axis=1)
        
        # Enhance puts dataframe with Greeks
        puts_df["gamma"] = puts_df.apply(lambda row: calculate_gamma(
            current_price, row["strike"], time_to_expiry, risk_free_rate, row["impliedVolatility"]
        ), axis=1)
        puts_df["vega"] = puts_df.apply(lambda row: calculate_vega(
            current_price, row["strike"], time_to_expiry, risk_free_rate, row["impliedVolatility"]
        ), axis=1)
        puts_df["delta"] = puts_df.apply(lambda row: calculate_delta_put(
            current_price, row["strike"], time_to_expiry, risk_free_rate, row["impliedVolatility"]
        ), axis=1)

        # Calculate position-weighted Greeks
        calls_df["gamma_exposure"] = calls_df["openInterest"] * calls_df["gamma"] * 100  # Multiply by 100 for contract size
        calls_df["vega_exposure"] = calls_df["openInterest"] * calls_df["vega"] * 100
        puts_df["gamma_exposure"] = puts_df["openInterest"] * puts_df["gamma"] * 100
        puts_df["vega_exposure"] = puts_df["openInterest"] * puts_df["vega"] * 100

        # Identify walls (top 3 OI strikes within +/- 10% of current price)
        price_range = (current_price * 0.9, current_price * 1.1)
        calls_nearby = calls_df[(calls_df["strike"] > current_price) & (calls_df["strike"] <= price_range[1])]
        puts_nearby = puts_df[(puts_df["strike"] < current_price) & (puts_df["strike"] >= price_range[0])]

        call_walls = calls_nearby.nlargest(3, "openInterest").to_dict("records") if not calls_nearby.empty else []
        put_walls = puts_nearby.nlargest(3, "openInterest").to_dict("records") if not puts_nearby.empty else []

        # Calculate net Greek exposures (calls are positive, puts are negative gamma)
        net_gamma_exposure = calls_df["gamma_exposure"].sum() - puts_df["gamma_exposure"].sum()
        net_vega_exposure = calls_df["vega_exposure"].sum() + puts_df["vega_exposure"].sum()  # Both contribute positively to vega
        
        # Calculate Greek concentrations at ATM (within 2% of current price)
        atm_range = (current_price * 0.98, current_price * 1.02)
        atm_calls = calls_df[(calls_df["strike"] >= atm_range[0]) & (calls_df["strike"] <= atm_range[1])]
        atm_puts = puts_df[(puts_df["strike"] >= atm_range[0]) & (puts_df["strike"] <= atm_range[1])]
        
        atm_gamma = atm_calls["gamma_exposure"].sum() - atm_puts["gamma_exposure"].sum()
        atm_vega = atm_calls["vega_exposure"].sum() + atm_puts["vega_exposure"].sum()

        # Determine gamma and vega trends
        gamma_exposure_sign = "POSITIVE" if net_gamma_exposure > 0 else "NEGATIVE" if net_gamma_exposure < 0 else "NEUTRAL"
        
        # Vega trend based on concentration and IV levels
        avg_iv = (calls_df["impliedVolatility"].mean() + puts_df["impliedVolatility"].mean()) / 2
        if avg_iv > 0.25 and net_vega_exposure > calls_df["vega_exposure"].quantile(0.75):
            vega_trend = "HIGH_VEGA_RISK"
        elif avg_iv < 0.15 and net_vega_exposure < calls_df["vega_exposure"].quantile(0.25):
            vega_trend = "LOW_VEGA_RISK"
        else:
            vega_trend = "MODERATE_VEGA_RISK"

        # Gamma trend based on concentration and price proximity
        if abs(atm_gamma) > abs(net_gamma_exposure) * 0.3:  # ATM gamma is >30% of total
            if net_gamma_exposure > 0:
                gamma_trend = "POSITIVE_GAMMA_WALL"  # Price likely to be sticky
            else:
                gamma_trend = "NEGATIVE_GAMMA_WALL"  # Price likely to be volatile
        else:
            gamma_trend = "DISTRIBUTED_GAMMA"

        self.strike_levels = {
            "current_price": current_price,
            "call_walls": call_walls,
            "put_walls": put_walls,
            "net_gamma": net_gamma_exposure,
            "net_vega": net_vega_exposure,
            "atm_gamma": atm_gamma,
            "atm_vega": atm_vega,
            "gamma_exposure": gamma_exposure_sign,
            "gamma_trend": gamma_trend,
            "vega_trend": vega_trend,
            "avg_implied_vol": avg_iv,
            "data_source": data_source,
            "greeks_summary": {
                "total_call_gamma": calls_df["gamma_exposure"].sum(),
                "total_put_gamma": puts_df["gamma_exposure"].sum(),
                "total_call_vega": calls_df["vega_exposure"].sum(),
                "total_put_vega": puts_df["vega_exposure"].sum(),
                "max_gamma_strike": calls_df.loc[calls_df["gamma_exposure"].idxmax(), "strike"] if not calls_df.empty else None,
                "max_vega_strike": calls_df.loc[calls_df["vega_exposure"].idxmax(), "strike"] if not calls_df.empty else None
            }
        }
        return self.strike_levels

    def run_volatility_analysis(self):
        """Run comprehensive volatility and VRP analysis."""
        print("Running volatility and VRP analysis...")
        
        try:
            # Run volatility analysis with option chain data
            self.volatility_metrics = self.volatility_analyzer.analyze_volatility_metrics(self.option_chain)
            
            if self.volatility_metrics:
                print(f"✓ Volatility analysis completed")
                print(f"  Current 20-day HV: {self.volatility_metrics['current_hv_20']:.1%}")
                print(f"  Current IV: {self.volatility_metrics['current_iv']:.1%}")
                print(f"  Current 20-day VRP: {self.volatility_metrics['current_vrp_20']:.1%}")
                return self.volatility_metrics
            else:
                print("⚠ Volatility analysis failed")
                return None
                
        except Exception as e:
            print(f"Error in volatility analysis: {e}")
            self.error_messages.append(f"Volatility analysis failed: {e}")
            return None

    def create_volatility_table_and_charts(self, save_path_prefix=None):
        """Create volatility table and charts."""
        if not self.volatility_metrics:
            print("No volatility metrics available for table/charts")
            return None, None, None
        
        try:
            # Create volatility table
            vol_table = self.volatility_analyzer.create_volatility_table(self.volatility_metrics)
            
            # Create VRP trend chart
            vrp_chart_path = None
            if save_path_prefix:
                vrp_chart_path = f"{save_path_prefix}_vrp_trend.png"
            vrp_fig = self.volatility_analyzer.plot_vrp_trend(vrp_chart_path)
            
            # Create volatility comparison chart
            vol_chart_path = None
            if save_path_prefix:
                vol_chart_path = f"{save_path_prefix}_volatility_comparison.png"
            vol_fig = self.volatility_analyzer.plot_volatility_comparison(vol_chart_path)
            
            return vol_table, vrp_fig, vol_fig
            
        except Exception as e:
            print(f"Error creating volatility table/charts: {e}")
            return None, None, None

    def plot_strike_levels(self, save_path=None):
        """Plot option open interest and strike levels (real or simulated data) - separate call and put graphs."""
        if self.strike_levels is None:
            if self.identify_strike_levels() is None:
                print("Cannot plot strike levels, analysis failed.")
                self.error_messages.append("Strike level plot skipped: Analysis failed")
                return None

        calls_df = pd.DataFrame(self.option_chain["calls"])
        puts_df = pd.DataFrame(self.option_chain["puts"])
        current_price = self.strike_levels["current_price"]
        data_source = self.strike_levels.get("data_source", "Simulated Data")

        if calls_df.empty or puts_df.empty or pd.isna(current_price):
            print("Cannot plot strike levels due to missing data.")
            self.error_messages.append("Strike level plot skipped: Missing data")
            return None

        # Filter data: calls above current price, puts below current price
        price_buffer = 250
        
        # Calls: from current price upward (resistance levels)
        max_call_strike = current_price + price_buffer
        calls_filtered = calls_df[(calls_df["strike"] >= current_price) & (calls_df["strike"] <= max_call_strike)]
        
        # Puts: from current price downward (support levels)  
        min_put_strike = current_price - price_buffer
        puts_filtered = puts_df[(puts_df["strike"] <= current_price) & (puts_df["strike"] >= min_put_strike)]
        
        if calls_filtered.empty and puts_filtered.empty:
            print(f"No option data: calls above {current_price:.0f} or puts below {current_price:.0f}")
            self.error_messages.append("Strike level plot skipped: No relevant call/put data")
            return None

        # Create subplots with extra space for tables below
        fig = plt.figure(figsize=(18, 14))
        
        # Create chart areas (top 60% of figure)
        ax1 = plt.subplot2grid((10, 2), (0, 0), rowspan=6)  # Calls chart
        ax2 = plt.subplot2grid((10, 2), (0, 1), rowspan=6)  # Puts chart
        
        # Calculate width based on strike difference in filtered data
        all_strikes = pd.concat([calls_filtered["strike"], puts_filtered["strike"]]).sort_values().unique()
        if len(all_strikes) > 1:
            strike_diff = np.diff(all_strikes)
            strike_width = np.median(strike_diff) * 0.4
        else:
            strike_width = 10

        # Get wall data and Greek information
        call_walls = self.strike_levels.get("call_walls", [])
        put_walls = self.strike_levels.get("put_walls", [])
        gamma_exposure = self.strike_levels.get("gamma_exposure", "UNKNOWN")
        gamma_trend = self.strike_levels.get("gamma_trend", "UNKNOWN")
        vega_trend = self.strike_levels.get("vega_trend", "UNKNOWN")
        avg_iv = self.strike_levels.get("avg_implied_vol", 0)
        net_gamma = self.strike_levels.get("net_gamma", 0)
        net_vega = self.strike_levels.get("net_vega", 0)

        # === LEFT PLOT: CALLS ===
        if not calls_filtered.empty:
            ax1.bar(calls_filtered["strike"], calls_filtered["openInterest"], 
                   width=strike_width, color="green", alpha=0.7, label=f"Call OI ({data_source})")

        # Current price line for calls
        ax1.axvline(x=current_price, color="blue", linestyle="--", linewidth=2, 
                   label=f"Current Price: {current_price:.2f}")

        # Plot call walls with enhanced visibility
        for i, wall in enumerate(call_walls):
            strike_val = wall["strike"]
            oi_val = wall["openInterest"]
            if current_price <= strike_val <= max_call_strike:  # Only plot calls above current price
                ax1.axvline(x=strike_val, color="darkgreen", linestyle="-", linewidth=4, alpha=0.9, 
                          label=f"Call Wall {i+1}: {strike_val:.0f}")
                # Add annotation for the wall
                max_oi = calls_filtered["openInterest"].max() if not calls_filtered.empty else 10000
                ax1.annotate(f'WALL {i+1}\n{strike_val:.0f}\nOI: {oi_val:,}', 
                           xy=(strike_val, max_oi * 0.8), 
                           xytext=(0, 10), textcoords='offset points',
                           ha='center', va='bottom', fontsize=10, fontweight='bold',
                           bbox=dict(boxstyle='round,pad=0.4', facecolor='lightgreen', alpha=0.8, edgecolor='darkgreen'))

        # Formatting for calls plot
        ax1.set_xlim(current_price, max_call_strike)
        ax1.set_title(f"CALL OPTIONS - Resistance Levels\n{self.ticker} ({data_source}) - Above Current Price", 
                     fontsize=14, fontweight='bold', color='darkgreen')
        ax1.set_xlabel("Strike Price", fontsize=12)
        ax1.set_ylabel("Open Interest", fontsize=12)
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax1.legend(loc="upper left", fontsize=9, framealpha=0.9)
        ax1.grid(True, axis="y", alpha=0.3)
        ax1.grid(True, axis="x", alpha=0.2)
        
        # Add call wall and gamma summary
        call_wall_text = f"Call Walls Detected: {len(call_walls)}"
        if call_walls:
            call_wall_text += f"\nStrongest: {call_walls[0]['strike']:.0f} (OI: {call_walls[0]['openInterest']:,})"
        call_wall_text += f"\n\nGamma Trend: {gamma_trend}"
        call_wall_text += f"\nNet Gamma: {net_gamma:,.0f}"
        ax1.text(0.02, 0.98, call_wall_text, 
                transform=ax1.transAxes, fontsize=9, fontweight='bold',
                verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.4', facecolor='lightgreen', alpha=0.8))

        # === RIGHT PLOT: PUTS ===
        if not puts_filtered.empty:
            ax2.bar(puts_filtered["strike"], puts_filtered["openInterest"], 
                   width=strike_width, color="red", alpha=0.7, label=f"Put OI ({data_source})")

        # Current price line for puts
        ax2.axvline(x=current_price, color="blue", linestyle="--", linewidth=2, 
                   label=f"Current Price: {current_price:.2f}")

        # Plot put walls with enhanced visibility
        for i, wall in enumerate(put_walls):
            strike_val = wall["strike"]
            oi_val = wall["openInterest"]
            if min_put_strike <= strike_val <= current_price:  # Only plot puts below current price
                ax2.axvline(x=strike_val, color="darkred", linestyle="-", linewidth=4, alpha=0.9, 
                          label=f"Put Wall {i+1}: {strike_val:.0f}")
                # Add annotation for the wall
                max_oi = puts_filtered["openInterest"].max() if not puts_filtered.empty else 10000
                ax2.annotate(f'WALL {i+1}\n{strike_val:.0f}\nOI: {oi_val:,}', 
                           xy=(strike_val, max_oi * 0.8), 
                           xytext=(0, 10), textcoords='offset points',
                           ha='center', va='bottom', fontsize=10, fontweight='bold',
                           bbox=dict(boxstyle='round,pad=0.4', facecolor='lightcoral', alpha=0.8, edgecolor='darkred'))

        # Formatting for puts plot
        ax2.set_xlim(min_put_strike, current_price)
        ax2.set_title(f"PUT OPTIONS - Support Levels\n{self.ticker} ({data_source}) - Below Current Price", 
                     fontsize=14, fontweight='bold', color='darkred')
        ax2.set_xlabel("Strike Price", fontsize=12)
        ax2.set_ylabel("Open Interest", fontsize=12)
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax2.legend(loc="upper left", fontsize=9, framealpha=0.9)
        ax2.grid(True, axis="y", alpha=0.3)
        ax2.grid(True, axis="x", alpha=0.2)
        
        # Add put wall and vega summary
        put_wall_text = f"Put Walls Detected: {len(put_walls)}"
        if put_walls:
            put_wall_text += f"\nStrongest: {put_walls[0]['strike']:.0f} (OI: {put_walls[0]['openInterest']:,})"
        put_wall_text += f"\n\nVega Trend: {vega_trend}"
        put_wall_text += f"\nNet Vega: {net_vega:,.0f}"
        put_wall_text += f"\nAvg IV: {avg_iv:.1%}"
        ax2.text(0.02, 0.98, put_wall_text, 
                transform=ax2.transAxes, fontsize=9, fontweight='bold',
                verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.4', facecolor='lightcoral', alpha=0.8))

        # Add overall Greek exposure info
        fig.suptitle(f"Option Greek Analysis - Gamma: {gamma_exposure} | Vega: {vega_trend} | IV: {avg_iv:.1%}", 
                    fontsize=16, fontweight='bold', y=0.95)
        
        # Add tables below the plots for better readability
        self._add_strike_level_tables(fig, call_walls, put_walls, current_price, 
                                     net_gamma, net_vega, avg_iv, gamma_trend, vega_trend)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)  # Make room for suptitle

        if save_path:
            try:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"Strike level plot saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving strike level plot: {e}")
                self.error_messages.append(f"Error saving strike level plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

    def _add_strike_level_tables(self, fig, call_walls, put_walls, current_price, 
                                net_gamma, net_vega, avg_iv, gamma_trend, vega_trend):
        """Add tables below the strike level plots for better readability"""
        try:
            # Create table data
            call_table_data = []
            put_table_data = []
            
            # Prepare call walls table
            if call_walls:
                for i, wall in enumerate(call_walls[:5]):  # Show top 5
                    distance = (wall['strike'] - current_price) / current_price * 100
                    call_table_data.append([
                        f"{i+1}",
                        f"{wall['strike']:.0f}",
                        f"+{distance:.1f}%",
                        f"{wall['openInterest']:,}",
                        f"{wall.get('volume', 0):,}",
                        f"{wall.get('impliedVolatility', 0):.1%}"
                    ])
            else:
                call_table_data.append(["No significant call walls detected", "", "", "", "", ""])
            
            # Prepare put walls table
            if put_walls:
                for i, wall in enumerate(put_walls[:5]):  # Show top 5
                    distance = (current_price - wall['strike']) / current_price * 100
                    put_table_data.append([
                        f"{i+1}",
                        f"{wall['strike']:.0f}",
                        f"-{distance:.1f}%",
                        f"{wall['openInterest']:,}",
                        f"{wall.get('volume', 0):,}",
                        f"{wall.get('impliedVolatility', 0):.1%}"
                    ])
            else:
                put_table_data.append(["No significant put walls detected", "", "", "", "", ""])
            
            # Gamma analysis table
            gamma_table_data = [
                ["Net Gamma", f"{net_gamma:,.0f}"],
                ["Net Vega", f"{net_vega:,.0f}"],
                ["Avg Implied Vol", f"{avg_iv:.1%}"],
                ["Gamma Trend", gamma_trend],
                ["Vega Trend", vega_trend],
                ["Current Price", f"{current_price:.2f}"]
            ]
            
            # Table headers
            wall_headers = ["Rank", "Strike", "Distance", "Open Interest", "Volume", "IV"]
            gamma_headers = ["Metric", "Value"]
            
            # Create table areas below the charts using subplot2grid
            # Call table (below left chart)
            call_table_ax = plt.subplot2grid((10, 2), (7, 0), rowspan=2)
            call_table_ax.axis('off')
            call_table = call_table_ax.table(cellText=call_table_data, 
                                           colLabels=wall_headers,
                                           cellLoc='center',
                                           loc='center',
                                           colWidths=[0.1, 0.15, 0.15, 0.2, 0.15, 0.15])
            call_table.auto_set_font_size(False)
            call_table.set_fontsize(8)
            call_table.scale(1, 1.2)
            
            # Style the call table
            for (i, j), cell in call_table.get_celld().items():
                if i == 0:  # Header row
                    cell.set_facecolor('#2ecc71')
                    cell.set_text_props(weight='bold', color='white')
                else:
                    cell.set_facecolor('#e8f5e8' if i % 2 == 0 else 'white')
            
            call_table_ax.set_title('CALL WALLS (Resistance)', 
                                   fontsize=12, fontweight='bold', 
                                   color='darkgreen', pad=20)
            
            # Put table (below right chart)
            put_table_ax = plt.subplot2grid((10, 2), (7, 1), rowspan=2)
            put_table_ax.axis('off')
            put_table = put_table_ax.table(cellText=put_table_data, 
                                         colLabels=wall_headers,
                                         cellLoc='center',
                                         loc='center',
                                         colWidths=[0.1, 0.15, 0.15, 0.2, 0.15, 0.15])
            put_table.auto_set_font_size(False)
            put_table.set_fontsize(8)
            put_table.scale(1, 1.2)
            
            # Style the put table
            for (i, j), cell in put_table.get_celld().items():
                if i == 0:  # Header row
                    cell.set_facecolor('#e74c3c')
                    cell.set_text_props(weight='bold', color='white')
                else:
                    cell.set_facecolor('#fdeaea' if i % 2 == 0 else 'white')
            
            put_table_ax.set_title('PUT WALLS (Support)', 
                                  fontsize=12, fontweight='bold', 
                                  color='darkred', pad=20)
            
        except Exception as e:
            print(f"Error adding tables to strike level plot: {e}")
            # Don't add to error_messages since this is cosmetic

    def _add_gamma_analysis_table(self, fig, net_gamma, strike_gamma_df, current_price, current_cum_gamma):
        """Add gamma analysis table below the gamma wall charts"""
        try:
            # Calculate additional metrics
            total_positive_gamma = strike_gamma_df[strike_gamma_df['gamma_exposure'] > 0]['gamma_exposure'].sum()
            total_negative_gamma = strike_gamma_df[strike_gamma_df['gamma_exposure'] < 0]['gamma_exposure'].sum()
            max_gamma_strike = strike_gamma_df.loc[strike_gamma_df['gamma_exposure'].abs().idxmax(), 'strike']
            max_gamma_value = strike_gamma_df.loc[strike_gamma_df['gamma_exposure'].abs().idxmax(), 'gamma_exposure']
            
            # Determine gamma concentration around current price (within 2%)
            atm_range = (current_price * 0.98, current_price * 1.02)
            atm_strikes = strike_gamma_df[
                (strike_gamma_df['strike'] >= atm_range[0]) & 
                (strike_gamma_df['strike'] <= atm_range[1])
            ]
            atm_gamma_total = atm_strikes['gamma_exposure'].sum()
            
            # Create table data
            gamma_table_data = [
                ["Net Gamma", f"{net_gamma:,.0f}"],
                ["Positive Gamma", f"{total_positive_gamma:,.0f}"],
                ["Negative Gamma", f"{total_negative_gamma:,.0f}"],
                ["Current Price", f"{current_price:.2f}"],
                ["Current Cum Gamma", f"{current_cum_gamma:,.0f}"],
                ["ATM Gamma (±2%)", f"{atm_gamma_total:,.0f}"],
                ["Max Gamma Strike", f"{max_gamma_strike:.0f}"],
                ["Max Gamma Value", f"{max_gamma_value:,.0f}"]
            ]
            
            # Table headers
            gamma_headers = ["Metric", "Value"]
            
            # Create table area below the charts
            gamma_table_ax = plt.subplot2grid((12, 1), (10, 0), rowspan=2)
            gamma_table_ax.axis('off')
            gamma_table = gamma_table_ax.table(cellText=gamma_table_data, 
                                             colLabels=gamma_headers,
                                             cellLoc='center',
                                             loc='center',
                                             colWidths=[0.5, 0.5])
            gamma_table.auto_set_font_size(False)
            gamma_table.set_fontsize(9)
            gamma_table.scale(1, 1.5)
            
            # Style the gamma table
            for (i, j), cell in gamma_table.get_celld().items():
                if i == 0:  # Header row
                    cell.set_facecolor('#3498db')
                    cell.set_text_props(weight='bold', color='white')
                else:
                    cell.set_facecolor('#ebf3fd' if i % 2 == 0 else 'white')
            
            gamma_table_ax.set_title('GAMMA ANALYSIS SUMMARY', 
                                   fontsize=12, fontweight='bold', 
                                   color='#2980b9', pad=20)
            
        except Exception as e:
            print(f"Error adding gamma analysis table: {e}")
            # Don't add to error_messages since this is cosmetic

    def generate_option_signals(self, money_market_bias):
        """Generate option signals based on strike levels and money market bias."""
        if self.strike_levels is None:
            if self.identify_strike_levels() is None:
                print("Cannot generate signals, strike level analysis failed.")
                return {"error": "Strike level analysis failed", "signal": "UNKNOWN", "confidence": "NONE", "rationale": "Strike level analysis failed"}

        # Get data source from strike levels analysis
        data_source = self.strike_levels.get("data_source", "Unknown Data")
        print(f"Generating option trading signals... (Using {data_source})")

        current_price = self.strike_levels["current_price"]
        if pd.isna(current_price):
             print("Cannot generate signals without current price.")
             return {"error": "Missing current price", "signal": "UNKNOWN", "confidence": "NONE", "rationale": "Missing current price"}

        # Check proximity to walls
        approaching_call_wall, nearest_call_wall_strike = False, None
        call_walls = self.strike_levels.get("call_walls", [])
        if call_walls:
            # Find nearest call wall *above* current price
            walls_above = [w for w in call_walls if w["strike"] > current_price]
            if walls_above:
                nearest_call = min(walls_above, key=lambda x: x["strike"])
                if 0 < (nearest_call["strike"] - current_price) / current_price < 0.02: # Within 2%
                    approaching_call_wall = True
                    nearest_call_wall_strike = nearest_call["strike"]

        approaching_put_wall, nearest_put_wall_strike = False, None
        put_walls = self.strike_levels.get("put_walls", [])
        if put_walls:
            # Find nearest put wall *below* current price
            walls_below = [w for w in put_walls if w["strike"] < current_price]
            if walls_below:
                nearest_put = max(walls_below, key=lambda x: x["strike"])
                if 0 < (current_price - nearest_put["strike"]) / current_price < 0.02: # Within 2%
                    approaching_put_wall = True
                    nearest_put_wall_strike = nearest_put["strike"]

        # Determine signal based on bias and structure
        mm_bias = money_market_bias.get("bias", "UNKNOWN")
        gamma_exposure = self.strike_levels["gamma_exposure"]
        gamma_trend = self.strike_levels.get("gamma_trend", "UNKNOWN")
        vega_trend = self.strike_levels.get("vega_trend", "UNKNOWN")
        avg_iv = self.strike_levels.get("avg_implied_vol", 0)
        signal, confidence, rationale = "NEUTRAL", "LOW", "Default neutral signal."

        # Extract values before f-string
        nearest_call_strike_fmt = f"{nearest_call_wall_strike:.0f}" if nearest_call_wall_strike else "N/A"
        nearest_put_strike_fmt = f"{nearest_put_wall_strike:.0f}" if nearest_put_wall_strike else "N/A"

        if mm_bias == "BEARISH":
            if approaching_call_wall and gamma_trend == "NEGATIVE_GAMMA_WALL": 
                signal, confidence, rationale = "STRONG SHORT", "HIGH", f"Bearish MM bias, approaching Call Wall @ {nearest_call_strike_fmt} with Negative Gamma Wall"
            elif approaching_call_wall: 
                signal, confidence, rationale = "STRONG SHORT", "HIGH", f"Bearish MM bias, approaching Call Wall @ {nearest_call_strike_fmt}"
            elif gamma_exposure == "NEGATIVE" and vega_trend == "HIGH_VEGA_RISK": 
                signal, confidence, rationale = "SHORT", "HIGH", f"Bearish MM bias, Negative Gamma, High Vega Risk (IV: {avg_iv:.1%})"
            elif gamma_exposure == "NEGATIVE": 
                signal, confidence, rationale = "SHORT", "MEDIUM", "Bearish MM bias, Negative Gamma"
            else: signal, confidence, rationale = "SHORT", "LOW", "Bearish MM bias"
        elif mm_bias == "BULLISH":
            if approaching_put_wall and gamma_trend == "POSITIVE_GAMMA_WALL": 
                signal, confidence, rationale = "STRONG LONG", "HIGH", f"Bullish MM bias, approaching Put Wall @ {nearest_put_strike_fmt} with Positive Gamma Wall"
            elif approaching_put_wall: 
                signal, confidence, rationale = "STRONG LONG", "HIGH", f"Bullish MM bias, approaching Put Wall @ {nearest_put_strike_fmt}"
            elif gamma_exposure == "POSITIVE" and vega_trend == "LOW_VEGA_RISK": 
                signal, confidence, rationale = "LONG", "HIGH", f"Bullish MM bias, Positive Gamma, Low Vega Risk (IV: {avg_iv:.1%})"
            elif gamma_exposure == "POSITIVE": 
                signal, confidence, rationale = "LONG", "MEDIUM", "Bullish MM bias, Positive Gamma"
            else: signal, confidence, rationale = "LONG", "LOW", "Bullish MM bias"
        elif mm_bias == "NEUTRAL":
            if approaching_call_wall and gamma_trend == "NEGATIVE_GAMMA_WALL": 
                signal, confidence, rationale = "SHORT", "MEDIUM", f"Neutral MM bias, approaching Call Wall @ {nearest_call_strike_fmt}, Negative Gamma Wall"
            elif approaching_put_wall and gamma_trend == "POSITIVE_GAMMA_WALL": 
                signal, confidence, rationale = "LONG", "MEDIUM", f"Neutral MM bias, approaching Put Wall @ {nearest_put_strike_fmt}, Positive Gamma Wall"
            elif vega_trend == "HIGH_VEGA_RISK": 
                signal, confidence, rationale = "NEUTRAL", "LOW", f"Neutral MM bias, High Vega Risk suggests volatility plays (IV: {avg_iv:.1%})"
            else: rationale = "Neutral MM bias, no clear option structure signal"
        else: # UNKNOWN bias
             rationale = "Money market bias unknown, cannot generate reliable signal."
             signal = "UNKNOWN"

        # Determine position type, expiry, size
        if "SHORT" in signal: position_type = "PUT SPREADS"
        elif "LONG" in signal: position_type = "CALL SPREADS"
        else: position_type = "AVOID DIRECTIONAL"

        if confidence == "HIGH": expiry, size = ("1-2 WEEKS" if "SHORT" in signal else "2-3 WEEKS"), "3-5%"
        elif confidence == "MEDIUM": expiry, size = ("1-2 WEEKS" if "SHORT" in signal else "1-2 WEEKS"), "2-3%"
        elif confidence == "LOW" and signal not in ["NEUTRAL", "UNKNOWN"]: expiry, size = ("1 WEEK" if "SHORT" in signal else "1 WEEK"), "1-2%"
        else: expiry, size = "N/A", "0%"

        return {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "ticker": self.ticker,
            "current_price": current_price,
            "signal": signal,
            "confidence": confidence,
            "rationale": rationale,
            "position_type": position_type,
            "recommended_expiry": expiry,
            "position_size": size,
            "approaching_call_wall": approaching_call_wall,
            "nearest_call_wall": nearest_call_wall_strike,
            "approaching_put_wall": approaching_put_wall,
            "nearest_put_wall": nearest_put_wall_strike,
            "gamma_exposure": gamma_exposure,
            "gamma_trend": gamma_trend,
            "vega_trend": vega_trend,
            "avg_implied_vol": avg_iv,
            "net_gamma": self.strike_levels.get("net_gamma", 0),
            "net_vega": self.strike_levels.get("net_vega", 0),
            "money_market_bias": mm_bias,
            "data_source": data_source
        }

    def plot_gamma_wall(self, save_path=None):
        """Plot gamma exposure by strike level to visualize gamma walls."""
        if self.strike_levels is None:
            if self.identify_strike_levels() is None:
                print("Cannot plot gamma wall, analysis failed.")
                self.error_messages.append("Gamma wall plot skipped: Analysis failed")
                return None

        calls_df = pd.DataFrame(self.option_chain["calls"])
        puts_df = pd.DataFrame(self.option_chain["puts"])
        current_price = self.strike_levels["current_price"]
        data_source = self.strike_levels.get("data_source", "Simulated Data")

        if calls_df.empty or puts_df.empty or pd.isna(current_price):
            print("Cannot plot gamma wall due to missing data.")
            self.error_messages.append("Gamma wall plot skipped: Missing data")
            return None

        # Calculate Greeks for the plotting (same as in identify_strike_levels)
        risk_free_rate = get_risk_free_rate()
        time_to_expiry = 7 / 365.25  # Assume 1 week to expiry
        
        # Add gamma calculations to calls and puts dataframes
        calls_df["gamma"] = calls_df.apply(lambda row: calculate_gamma(
            current_price, row["strike"], time_to_expiry, risk_free_rate, row["impliedVolatility"]
        ), axis=1)
        
        puts_df["gamma"] = puts_df.apply(lambda row: calculate_gamma(
            current_price, row["strike"], time_to_expiry, risk_free_rate, row["impliedVolatility"]
        ), axis=1)

        # Filter to reasonable range around current price
        price_buffer = 300
        min_strike = current_price - price_buffer
        max_strike = current_price + price_buffer
        
        # Combine and filter data
        all_strikes_data = []
        
        # Process calls (positive gamma exposure for market makers)
        calls_filtered = calls_df[(calls_df["strike"] >= min_strike) & (calls_df["strike"] <= max_strike)]
        for _, row in calls_filtered.iterrows():
            gamma_exposure = row["openInterest"] * row["gamma"] * 100  # *100 for contract multiplier
            all_strikes_data.append({
                "strike": row["strike"],
                "gamma_exposure": gamma_exposure,  # Positive for calls
                "option_type": "calls",
                "open_interest": row["openInterest"]
            })
        
        # Process puts (negative gamma exposure for market makers when they're short puts)
        puts_filtered = puts_df[(puts_df["strike"] >= min_strike) & (puts_df["strike"] <= max_strike)]
        for _, row in puts_filtered.iterrows():
            gamma_exposure = -(row["openInterest"] * row["gamma"] * 100)  # Negative for puts (*100 for contract multiplier)
            all_strikes_data.append({
                "strike": row["strike"],
                "gamma_exposure": gamma_exposure,  # Negative for puts
                "option_type": "puts",
                "open_interest": row["openInterest"]
            })
        
        if not all_strikes_data:
            print("No gamma data available for plotting.")
            self.error_messages.append("Gamma wall plot skipped: No gamma data")
            return None
        
        # Convert to DataFrame and aggregate by strike
        gamma_df = pd.DataFrame(all_strikes_data)
        strike_gamma = gamma_df.groupby("strike").agg({
            "gamma_exposure": "sum",
            "open_interest": "sum"
        }).reset_index()
        
        # Sort by strike
        strike_gamma = strike_gamma.sort_values("strike")
        
        # Calculate cumulative gamma exposure
        strike_gamma["cumulative_gamma"] = strike_gamma["gamma_exposure"].cumsum()
        
        # Create the plot with space for table
        fig = plt.figure(figsize=(15, 16))
        
        # Create chart areas (top 75% of figure)
        ax1 = plt.subplot2grid((12, 1), (0, 0), rowspan=5)  # Gamma exposure chart
        ax2 = plt.subplot2grid((12, 1), (5, 0), rowspan=5)  # Cumulative gamma chart
        
        # === TOP PLOT: Gamma Exposure by Strike ===
        colors = ["red" if x < 0 else "green" for x in strike_gamma["gamma_exposure"]]
        bars = ax1.bar(strike_gamma["strike"], strike_gamma["gamma_exposure"], 
                      color=colors, alpha=0.7, width=10)
        
        # Current price line
        ax1.axvline(x=current_price, color="blue", linestyle="--", linewidth=3, 
                   label=f"Current Price: {current_price:.2f}")
        
        # Zero line
        ax1.axhline(y=0, color="black", linestyle="-", linewidth=1, alpha=0.5)
        
        # Highlight significant gamma walls
        gamma_threshold = strike_gamma["gamma_exposure"].abs().quantile(0.90)  # Top 10% by absolute gamma
        significant_strikes = strike_gamma[strike_gamma["gamma_exposure"].abs() >= gamma_threshold]
        
        for _, row in significant_strikes.iterrows():
            strike_val = row["strike"]
            gamma_val = row["gamma_exposure"]
            wall_type = "CALL WALL" if gamma_val > 0 else "PUT WALL"
            color = "darkgreen" if gamma_val > 0 else "darkred"
            
            # Add vertical line for wall
            ax1.axvline(x=strike_val, color=color, linestyle="-", linewidth=2, alpha=0.8)
            
            # Add annotation
            ax1.annotate(f'{wall_type}\n{strike_val:.0f}\nγ: {gamma_val:,.0f}', 
                        xy=(strike_val, gamma_val), 
                        xytext=(10, 10), textcoords='offset points',
                        ha='left', va='bottom', fontsize=9, fontweight='bold',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.2))
        
        ax1.set_title(f"Gamma Wall Analysis - {self.ticker} ({data_source})\nNet Gamma: {strike_gamma['gamma_exposure'].sum():,.0f}", 
                     fontsize=14, fontweight='bold')
        ax1.set_ylabel("Gamma Exposure\n(Market Maker Perspective)", fontsize=12)
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax1.legend(loc="upper left")
        ax1.grid(True, alpha=0.3)
        
        # Add explanatory text
        explanation = ("Positive γ: MM buy as price rises (resistance)\n"
                      "Negative γ: MM sell as price rises (support)")
        ax1.text(0.98, 0.02, explanation, transform=ax1.transAxes, 
                fontsize=9, ha='right', va='bottom',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8))
        
        # === BOTTOM PLOT: Cumulative Gamma Profile ===
        ax2.plot(strike_gamma["strike"], strike_gamma["cumulative_gamma"], 
                color="purple", linewidth=3, label="Cumulative Gamma")
        ax2.fill_between(strike_gamma["strike"], 0, strike_gamma["cumulative_gamma"], 
                        alpha=0.3, color="purple")
        
        # Current price line
        ax2.axvline(x=current_price, color="blue", linestyle="--", linewidth=3, 
                   label=f"Current Price: {current_price:.2f}")
        
        # Zero line
        ax2.axhline(y=0, color="black", linestyle="-", linewidth=1, alpha=0.5)
        
        # Find and highlight inflection points (where cumulative gamma changes direction significantly)
        current_price_idx = strike_gamma.iloc[(strike_gamma["strike"] - current_price).abs().argsort()[:1]].index[0]
        current_cum_gamma = strike_gamma.loc[current_price_idx, "cumulative_gamma"]
        
        ax2.scatter([current_price], [current_cum_gamma], color="red", s=100, zorder=5, 
                   label=f"Current Cum γ: {current_cum_gamma:,.0f}")
        
        ax2.set_title("Cumulative Gamma Profile - Market Flow Direction", fontsize=12, fontweight='bold')
        ax2.set_xlabel("Strike Price", fontsize=12)
        ax2.set_ylabel("Cumulative Gamma", fontsize=12)
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        ax2.legend(loc="upper left")
        ax2.grid(True, alpha=0.3)
        
        # Add market maker impact explanation
        if current_cum_gamma > 0:
            mm_impact = "MM are net long gamma → stabilizing/supportive"
            impact_color = "green"
        else:
            mm_impact = "MM are net short gamma → destabilizing/momentum"
            impact_color = "red"
            
        ax2.text(0.98, 0.98, f"Market Impact: {mm_impact}", transform=ax2.transAxes, 
                fontsize=10, ha='right', va='top', fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor=impact_color, alpha=0.2))
        
        # Calculate net gamma for table
        net_gamma = strike_gamma['gamma_exposure'].sum()
        
        # Add gamma analysis table below the charts
        self._add_gamma_analysis_table(fig, net_gamma, strike_gamma, current_price, current_cum_gamma)
        
        plt.tight_layout()
        
        if save_path:
            try:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"Gamma wall plot saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving gamma wall plot: {e}")
                self.error_messages.append(f"Error saving gamma wall plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

class ChecklistDriver:
    """
    Main driver class using real data (where available).
    """
    def __init__(self, ticker="^GSPC", days_back=3000, output_dir=None):
        self.ticker = ticker
        self.days_back = days_back
        self.output_dir = output_dir or os.path.join(os.getcwd(), "output")
        os.makedirs(self.output_dir, exist_ok=True)

        self.money_market = MoneyMarketMonitor(days_back=days_back, output_dir=self.output_dir)
        self.option_signals = OptionSignalGenerator(ticker=ticker, days_back=days_back, output_dir=self.output_dir)

        self.money_market_analysis = {}
        self.option_signals_analysis = {}
        self.checklist_status = {}
        self.plots_generated = {} # Initialize plot paths dictionary
        self.error_summary = [] # Store summary of errors

    def run_money_market_analysis(self):
        """Run money market analysis using real data."""
        print("\n1. Running Money Market Analysis...")
        self.money_market.fetch_all_data()
        self.money_market.calculate_fed_net_liquidity()
        self.money_market_analysis = self.money_market.get_trading_bias()
        # Combine error messages from fetcher and monitor
        self.error_summary.extend(self.money_market.error_messages)
        return self.money_market_analysis

    def run_option_signals_analysis(self):
        """Run comprehensive option signals analysis with enhanced VRP and risk management."""
        print("\n2. Running Enhanced Option Signals Analysis...")
        if not self.money_market_analysis: # Ensure MM analysis ran first
            print("Money market analysis missing, running it first...")
            self.run_money_market_analysis()

        self.option_signals.fetch_price_data()
        # Option chain simulation and strike level identification happen within generate_option_signals
        self.option_signals_analysis = self.option_signals.generate_option_signals(self.money_market_analysis)
        
        # Enhanced VRP Analysis
        print("\n2a. Running Comprehensive VRP Analysis...")
        volatility_metrics = self.option_signals.run_volatility_analysis()
        if volatility_metrics:
            # Add enhanced volatility metrics to option signals analysis
            self.option_signals_analysis.update({
                'volatility_metrics': volatility_metrics,
                'current_hv_10': volatility_metrics.get('current_hv_10', 0),
                'current_hv_20': volatility_metrics.get('current_hv_20', 0),
                'current_hv_30': volatility_metrics.get('current_hv_30', 0),
                'current_hv_60': volatility_metrics.get('current_hv_60', 0),
                'current_iv': volatility_metrics.get('current_iv', 0),
                'current_vrp_10': volatility_metrics.get('current_vrp_10', 0),
                'current_vrp_20': volatility_metrics.get('current_vrp_20', 0),
                'current_vrp_30': volatility_metrics.get('current_vrp_30', 0),
                'current_vrp_60': volatility_metrics.get('current_vrp_60', 0),
                'vrp_percentile': volatility_metrics.get('vrp_percentile', 50),
                'vrp_signal': volatility_metrics.get('signal_strength', 'Neutral'),
                'vrp_confidence': volatility_metrics.get('confidence', 0.5),
                'vrp_strategy': volatility_metrics.get('recommended_strategy', 'Monitor')
            })
            
            # Generate VRP-based trading signals
            print("\n2b. Generating VRP-Based Trading Signals...")
            vrp_signals = self._generate_vrp_trading_signals(volatility_metrics)
            self.option_signals_analysis.update(vrp_signals)
            
        # Enhanced Risk Management Assessment
        print("\n2c. Running Risk Management Assessment...")
        risk_assessment = self._generate_risk_management_recommendations()
        self.option_signals_analysis.update(risk_assessment)
        
        self.error_summary.extend(self.option_signals.error_messages)
        return self.option_signals_analysis

    def _generate_vrp_trading_signals(self, volatility_metrics):
        """Generate enhanced VRP-based trading signals."""
        vrp_30d = volatility_metrics.get('current_vrp_30', 0)
        vrp_60d = volatility_metrics.get('current_vrp_60', 0)
        vrp_percentile = volatility_metrics.get('vrp_percentile', 50)
        current_iv = volatility_metrics.get('current_iv', 0.2)
        
        # VRP Signal Logic
        avg_vrp = (vrp_30d + vrp_60d) / 2 if vrp_30d and vrp_60d else vrp_30d or vrp_60d or 0
        
        vrp_signals = {
            'vrp_analysis': {
                'avg_vrp': avg_vrp,
                'vrp_30d': vrp_30d,
                'vrp_60d': vrp_60d,
                'vrp_percentile': vrp_percentile,
                'iv_level': current_iv
            }
        }
        
        # Enhanced VRP Trading Logic
        if avg_vrp >= 0.08 and vrp_percentile >= 80:
            vrp_signals.update({
                'vrp_signal': 'STRONG_SELL_VOLATILITY',
                'vrp_confidence': 0.9,
                'vrp_strategy': 'Short volatility: Sell straddles/strangles, iron condors',
                'vrp_rationale': f'Very high VRP ({avg_vrp:.1%}) in top 20th percentile - options overpriced'
            })
        elif avg_vrp >= 0.04 and vrp_percentile >= 60:
            vrp_signals.update({
                'vrp_signal': 'SELL_VOLATILITY',
                'vrp_confidence': 0.7,
                'vrp_strategy': 'Moderate short volatility: Credit spreads, covered calls',
                'vrp_rationale': f'Elevated VRP ({avg_vrp:.1%}) suggests options are expensive'
            })
        elif avg_vrp <= -0.08 and vrp_percentile <= 20:
            vrp_signals.update({
                'vrp_signal': 'STRONG_BUY_VOLATILITY',
                'vrp_confidence': 0.9,
                'vrp_strategy': 'Long volatility: Buy straddles/strangles, protective puts',
                'vrp_rationale': f'Very low VRP ({avg_vrp:.1%}) in bottom 20th percentile - options underpriced'
            })
        elif avg_vrp <= -0.04 and vrp_percentile <= 40:
            vrp_signals.update({
                'vrp_signal': 'BUY_VOLATILITY',
                'vrp_confidence': 0.7,
                'vrp_strategy': 'Moderate long volatility: Debit spreads, long puts for hedging',
                'vrp_rationale': f'Negative VRP ({avg_vrp:.1%}) suggests options are underpriced'
            })
        else:
            vrp_signals.update({
                'vrp_signal': 'NEUTRAL_VOLATILITY',
                'vrp_confidence': 0.5,
                'vrp_strategy': 'Delta-neutral strategies, calendar spreads',
                'vrp_rationale': f'Moderate VRP ({avg_vrp:.1%}) suggests fair value options'
            })
        
        return vrp_signals

    def _generate_risk_management_recommendations(self):
        """Generate comprehensive risk management recommendations."""
        mm = self.money_market_analysis
        opt = self.option_signals_analysis
        
        # Fed Net Liquidity Assessment
        net_liquidity = mm.get('fed_net_liquidity', 0)
        liquidity_trend = mm.get('liquidity_trend', 'UNKNOWN')
        liquidity_condition = mm.get('liquidity_condition', 'UNKNOWN')
        
        # Risk Assessment Factors
        current_price = opt.get('current_price', 0)
        gamma_exposure = opt.get('net_gamma', 0)
        vega_exposure = opt.get('net_vega', 0)
        
        risk_assessment = {
            'fed_net_liquidity_assessment': {
                'net_liquidity': net_liquidity,
                'liquidity_trend': liquidity_trend,
                'liquidity_condition': liquidity_condition,
                'liquidity_impact': self._assess_liquidity_impact(net_liquidity, liquidity_trend)
            },
            'risk_management_recommendations': {
                'position_sizing': self._get_position_sizing_recommendations(),
                'gamma_risk': self._assess_gamma_risk(gamma_exposure, current_price),
                'vega_risk': self._assess_vega_risk(vega_exposure),
                'liquidity_risk': self._assess_liquidity_risk(liquidity_condition, liquidity_trend),
                'overall_risk_level': self._calculate_overall_risk_level()
            }
        }
        
        return risk_assessment
    
    def _assess_liquidity_impact(self, net_liquidity, liquidity_trend):
        """Assess the impact of Fed liquidity on markets."""
        if net_liquidity > 1000:  # Billions
            if liquidity_trend == 'EXPANDING':
                return 'STRONG_BULLISH - Abundant liquidity supporting risk assets'
            else:
                return 'BULLISH - High liquidity levels provide market support'
        elif net_liquidity < -1000:
            if liquidity_trend == 'CONTRACTING':
                return 'STRONG_BEARISH - Liquidity drain pressuring risk assets'
            else:
                return 'BEARISH - Low liquidity creates market headwinds'
        else:
            return 'NEUTRAL - Moderate liquidity levels'
    
    def _get_position_sizing_recommendations(self):
        """Get position sizing recommendations based on market conditions."""
        mm = self.money_market_analysis
        opt = self.option_signals_analysis
        
        confidence = mm.get('confidence', 'LOW')
        vrp_confidence = opt.get('vrp_confidence', 0.5)
        
        if confidence == 'HIGH' and vrp_confidence >= 0.8:
            return 'LARGE - High conviction setup (3-5% of portfolio)'
        elif confidence == 'MEDIUM' or vrp_confidence >= 0.6:
            return 'MEDIUM - Moderate conviction (1-3% of portfolio)'
        else:
            return 'SMALL - Low conviction (0.5-1% of portfolio)'
    
    def _assess_gamma_risk(self, gamma_exposure, current_price):
        """Assess gamma risk from current positions."""
        if abs(gamma_exposure) > current_price * 0.01:  # > 1% of current price
            return 'HIGH - Large gamma exposure requires active management'
        elif abs(gamma_exposure) > current_price * 0.005:  # > 0.5% of current price
            return 'MEDIUM - Moderate gamma exposure, monitor closely'
        else:
            return 'LOW - Minimal gamma risk'
    
    def _assess_vega_risk(self, vega_exposure):
        """Assess vega risk from current positions."""
        if abs(vega_exposure) > 1000:
            return 'HIGH - Significant volatility sensitivity'
        elif abs(vega_exposure) > 500:
            return 'MEDIUM - Moderate volatility sensitivity'
        else:
            return 'LOW - Minimal volatility risk'
    
    def _assess_liquidity_risk(self, liquidity_condition, liquidity_trend):
        """Assess liquidity-related risks."""
        if liquidity_condition == 'TIGHT' and liquidity_trend == 'CONTRACTING':
            return 'HIGH - Deteriorating liquidity conditions increase market risk'
        elif liquidity_condition == 'TIGHT' or liquidity_trend == 'CONTRACTING':
            return 'MEDIUM - Some liquidity concerns present'
        else:
            return 'LOW - Adequate liquidity conditions'
    
    def _calculate_overall_risk_level(self):
        """Calculate overall risk level based on all factors."""
        mm = self.money_market_analysis
        opt = self.option_signals_analysis
        
        # Factors contributing to risk
        liquidity_risk = mm.get('liquidity_condition', 'UNKNOWN') == 'TIGHT'
        high_volatility = opt.get('current_iv', 0.2) > 0.25
        conflicting_signals = mm.get('bias', 'UNKNOWN') != opt.get('signal', 'UNKNOWN')
        
        risk_score = sum([liquidity_risk, high_volatility, conflicting_signals])
        
        if risk_score >= 2:
            return 'HIGH - Multiple risk factors present, reduce position sizes'
        elif risk_score == 1:
            return 'MEDIUM - Some risk factors present, standard position sizing'
        else:
            return 'LOW - Favorable risk environment'

    def generate_checklist_status(self):
        """Generate checklist status based on analysis results."""
        print("\n3. Generating Checklist Status...")
        if not self.money_market_analysis or not self.option_signals_analysis:
            print("Cannot generate checklist status, analysis incomplete.")
            self.error_summary.append("Checklist generation failed: Analysis incomplete")
            return {}

        mm = self.money_market_analysis
        opt = self.option_signals_analysis

        # Helper to create checklist item structure
        def create_item(result_key, action_logic, result_prefix="", action_prefix="", data_source=mm, error_msg="UNKNOWN"):
            result = data_source.get(result_key, error_msg)
            # Consider error state from MM analysis
            mm_error = mm.get("error")
            status = "FAILED" if result == error_msg or mm_error else "COMPLETED"
            # Handle cases where result might be NaN
            if pd.isna(result):
                status = "FAILED"
                result_str = "N/A (Data Missing)"
                action = "N/A (Data Missing)"
            elif status == "COMPLETED":
                action = action_logic(result)
                # Format numeric results for display
                if isinstance(result, (int, float, np.number)):
                    if "pct" in result_key: result_str = format_percent(result)
                    elif any(k in result_key for k in ["tga", "rrp", "liquidity", "gamma"]): result_str = format_currency(result)
                    else: result_str = f"{result:.2f}"
                else:
                    result_str = str(result)
            else: # Status is FAILED
                result_str = f"N/A ({error_msg})"
                action = "N/A (Analysis Failed)"

            return {"status": status, "result": f"{result_prefix}{result_str}", "action": f"{action_prefix}{action}"}

        # Extract values safely before using in f-strings
        nearest_call_wall_val = opt.get("nearest_call_wall", "N/A")
        nearest_put_wall_val = opt.get("nearest_put_wall", "N/A")
        approaching_call_wall_val = opt.get("approaching_call_wall", False)
        approaching_put_wall_val = opt.get("approaching_put_wall", False)

        # Extract values for MM Overlay and Option Signals before f-strings
        mm_pos_type = mm.get("position_type", "N/A")
        mm_expiry = mm.get("recommended_expiry", "N/A")
        mm_bias_val = mm.get("bias", "UNK")
        mm_conf_val = mm.get("confidence", "UNK")

        opt_pos_type = opt.get("position_type", "N/A")
        opt_expiry = opt.get("recommended_expiry", "N/A")
        opt_signal_val = opt.get("signal", "UNK")
        opt_conf_val = opt.get("confidence", "UNK")

        # Extract values for liquidity_conditions before f-string
        liq_cond_val = mm.get("liquidity_condition", "UNK")
        liq_trend_val = mm.get("liquidity_trend", "UNK")
        liq_cond_result_str = f"Condition: {liq_cond_val}, Trend: {liq_trend_val}"
        liq_cond_status = "COMPLETED" if liq_cond_val != "UNK" and liq_trend_val != "UNK" and not mm.get("error") else "FAILED"

        # Determine status for option strike levels based on signal status
        opt_error = opt.get("error")
        opt_strike_status = "COMPLETED" if opt.get("signal") != "UNKNOWN" and not opt_error else "FAILED"
        opt_strike_result = f"Nearest Call Wall: {nearest_call_wall_val}, Nearest Put Wall: {nearest_put_wall_val}" if opt_strike_status == "COMPLETED" else "N/A (Analysis Failed)"
        opt_strike_action = f"Monitor Resistance: {approaching_call_wall_val}, Monitor Support: {approaching_put_wall_val}" if opt_strike_status == "COMPLETED" else "N/A"

        # Determine status for combined signal
        combined_signal_val = self._get_combined_signal()
        combined_signal_status = "COMPLETED" if combined_signal_val != "UNKNOWN" else "FAILED"
        combined_action_val = self._get_combined_action()

        # Extract VRP analysis results
        vrp_signal = opt.get("vrp_signal", "NEUTRAL_VOLATILITY")
        vrp_strategy = opt.get("vrp_strategy", "Monitor")
        vrp_rationale = opt.get("vrp_rationale", "No VRP analysis available")
        vrp_30d = opt.get("current_vrp_30", 0)
        vrp_60d = opt.get("current_vrp_60", 0)
        
        # Extract Fed Net Liquidity Assessment
        fed_assessment = opt.get("fed_net_liquidity_assessment", {})
        liquidity_impact = fed_assessment.get("liquidity_impact", "UNKNOWN")
        
        # Extract Risk Management Recommendations
        risk_mgmt = opt.get("risk_management_recommendations", {})
        position_sizing = risk_mgmt.get("position_sizing", "UNKNOWN")
        overall_risk = risk_mgmt.get("overall_risk_level", "UNKNOWN")

        self.checklist_status = {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "ticker": self.ticker,
            "pre_market": {
                "tga_balance_changes": create_item("tga_impact", lambda r: "BEARISH BIAS" if "DRAIN" in r else ("BULLISH BIAS" if "ADDITION" in r else "NEUTRAL"), "TGA Impact: ", "Implied Bias: ", error_msg="TGA_FAIL"),
                "rrp_usage": create_item("rrp_impact", lambda r: "MONITOR VOLATILITY" if "TIGHTENING" in r else "NO ACTION", "RRP Impact: ", "Action: ", error_msg="RRP_FAIL"),
                "treasury_auction_schedule": create_item("treasury_auction_assessment", lambda r: "BEARISH BIAS" if "BEARISH" in r else ("BULLISH BIAS" if "BULLISH" in r else "NEUTRAL"), "Auction Assessment: ", "Implied Bias: ", error_msg="AUCTION_FAIL"),
                "fed_net_liquidity": {
                    "status": "COMPLETED" if liquidity_impact != "UNKNOWN" else "FAILED",
                    "result": f"Fed Liquidity Impact: {liquidity_impact}",
                    "action": f"Liquidity Bias: {liquidity_impact.split(' - ')[0] if ' - ' in liquidity_impact else 'MONITOR'}"
                }
            },
            "market_open": {
                "option_strike_levels": {
                    "status": opt_strike_status,
                    "result": opt_strike_result,
                    "action": opt_strike_action
                },
                "vrp_analysis": {
                    "status": "COMPLETED" if vrp_signal != "NEUTRAL_VOLATILITY" or vrp_30d != 0 else "FAILED",
                    "result": f"VRP 30D: {vrp_30d*100:+.1f}%, VRP 60D: {vrp_60d*100:+.1f}% | Signal: {vrp_signal}",
                    "action": f"VRP Strategy: {vrp_strategy}"
                }
            },
            "mid_day": {
                "money_market_overlay": create_item("bias", lambda r: f"USE {mm_pos_type} WITH {mm_expiry} EXPIRY", f"MM Bias: {mm_bias_val} ({mm_conf_val})", "MM Action: ", error_msg="MM_BIAS_FAIL"),
                "option_signals": {
                    "status": "COMPLETED" if opt.get("signal") != "UNKNOWN" and not opt_error else "FAILED",
                    "result": f"Option Signal: {opt_signal_val} ({opt_conf_val})",
                    "action": f"Option Action: USE {opt_pos_type} WITH {opt_expiry} EXPIRY"
                },
                "combined_signal": {
                    "status": combined_signal_status,
                    "result": f"Combined Signal: {combined_signal_val}",
                    "action": f"Combined Action: {combined_action_val}"
                },
                "risk_management": {
                    "status": "COMPLETED" if overall_risk != "UNKNOWN" else "FAILED",
                    "result": f"Risk Level: {overall_risk} | Position Size: {position_sizing}",
                    "action": f"Risk Action: {overall_risk.split(' - ')[1] if ' - ' in overall_risk else 'MONITOR RISK'}"
                }
            },
            "end_of_day": {
                "liquidity_conditions": {
                    "status": liq_cond_status,
                    "result": liq_cond_result_str,
                    "action": "Action: PREPARE FOR NEXT DAY"
                },
                "volatility_assessment": {
                    "status": "COMPLETED" if vrp_rationale != "No VRP analysis available" else "FAILED",
                    "result": f"VRP Rationale: {vrp_rationale}",
                    "action": "Review volatility positioning for next day"
                }
            }
        }
        return self.checklist_status

    def _get_combined_signal(self):
        """Get combined signal from analysis results."""
        mm_bias = self.money_market_analysis.get("bias", "UNKNOWN")
        opt_signal = self.option_signals_analysis.get("signal", "UNKNOWN")

        if mm_bias == "UNKNOWN" or opt_signal == "UNKNOWN": return "UNKNOWN"
        if mm_bias == "BEARISH" and "SHORT" in opt_signal: return "STRONG SHORT"
        if mm_bias == "BULLISH" and "LONG" in opt_signal: return "STRONG LONG"
        if mm_bias == "BEARISH" and "LONG" in opt_signal: return "CONFLICTING - NEUTRAL"
        if mm_bias == "BULLISH" and "SHORT" in opt_signal: return "CONFLICTING - NEUTRAL"
        # If MM is neutral, return option signal. If signals match, return stronger signal.
        if mm_bias == "NEUTRAL": return opt_signal
        if mm_bias == "BEARISH" and opt_signal == "SHORT": return "SHORT" # MM Low conf, Opt Low conf
        if mm_bias == "BULLISH" and opt_signal == "LONG": return "LONG"
        return opt_signal # Fallback

    def _get_combined_action(self):
        """Get combined action based on the combined signal."""
        combined_signal = self._get_combined_signal()
        opt = self.option_signals_analysis
        mm = self.money_market_analysis

        # Use the most confident recommendation if signals align or MM is neutral
        # Prioritize MM confidence if signals conflict?
        # Current logic: Use option signal details for action if combined signal is directional

        opt_expiry = opt.get("recommended_expiry", "N/A")
        opt_size = opt.get("position_size", "N/A")

        if combined_signal == "STRONG SHORT": return f"PUT SPREADS, {opt_expiry}, {opt_size}"
        if combined_signal == "STRONG LONG": return f"CALL SPREADS, {opt_expiry}, {opt_size}"
        if combined_signal == "SHORT": return f"PUT SPREADS, {opt_expiry}, {opt_size}"
        if combined_signal == "LONG": return f"CALL SPREADS, {opt_expiry}, {opt_size}"
        return "AVOID DIRECTIONAL, CONSIDER NON-DIRECTIONAL"

    def generate_plots(self):
        """Generate plots for analysis results."""
        print("\n4. Generating Plots...")
        plots = {}
        plots["liquidity"] = self.money_market.plot_liquidity_indicators(save_path=os.path.join(self.output_dir, "money_market_indicators.png"))
        plots["strike_levels"] = self.option_signals.plot_strike_levels(save_path=os.path.join(self.output_dir, "option_strike_levels.png"))
        plots["gamma_wall"] = self.option_signals.plot_gamma_wall(save_path=os.path.join(self.output_dir, "gamma_wall_analysis.png"))
        
        # Generate volatility charts
        print("\n4a. Generating Volatility Charts...")
        vol_table, vrp_fig, vol_fig = self.option_signals.create_volatility_table_and_charts(
            save_path_prefix=os.path.join(self.output_dir, "volatility")
        )
        
        if vrp_fig:
            plots["vrp_trend"] = os.path.join(self.output_dir, "volatility_vrp_trend.png")
        if vol_fig:
            plots["volatility_comparison"] = os.path.join(self.output_dir, "volatility_volatility_comparison.png")
        if vol_table is not None and not vol_table.empty:
            # Save volatility table as CSV
            vol_table_path = os.path.join(self.output_dir, "volatility_table.csv")
            vol_table.to_csv(vol_table_path, index=False)
            plots["volatility_table"] = vol_table_path
            print(f"✓ Volatility table saved to {vol_table_path}")
        
        self.plots_generated = plots # Store plot paths
        return plots

    def generate_html_report(self, save_path=None):
        """Generate HTML report of checklist status."""
        print("\n5. Generating HTML Report...")
        if not self.checklist_status:
            print("Checklist status not generated, cannot create HTML report.")
            self.error_summary.append("HTML Report generation failed: Checklist status missing")
            return None

        save_path = save_path or os.path.join(self.output_dir, "checklist_report.html")
        mm = self.money_market_analysis
        opt = self.option_signals_analysis
        chk = self.checklist_status

        # Function to generate HTML for a checklist section
        def generate_section_html(title, section_data):
            html = f"<div class=\"section\"><h2>{title}</h2>"
            if not section_data:
                html += "<p>No data available for this section.</p>"
            else:
                for key, item in section_data.items():
                    status_class = "completed" if item.get("status") == "COMPLETED" else "failed" if item.get("status") == "FAILED" else "info"
                    # Safely format result and action
                    result_text = item.get("result", "N/A")
                    action_text = item.get("action", "N/A")
                    formatted_key = key.replace("_", " ").title()
                    item_status = item.get("status", "?")
                    html += f"""
                    <div class="item">
                        <div class="item-header">{formatted_key}<span class="status {status_class}">{item_status}</span></div>
                        <div class="result">Result: {result_text}</div>
                        <div class="action">Action: {action_text}</div>
                    </div>"""
            html += "</div>"
            return html

        # Extract values before f-string
        chk_date = chk.get("date", "N/A")
        chk_ticker = chk.get("ticker", "N/A")
        mm_bias = mm.get("bias", "neutral").lower()
        mm_bias_text = mm.get("bias", "UNKNOWN")
        mm_conf = mm.get("confidence", "UNKNOWN")
        opt_pos_type = opt.get("position_type", "N/A")
        opt_expiry = opt.get("recommended_expiry", "N/A")
        opt_size = opt.get("position_size", "N/A")
        opt_rationale = opt.get("rationale", "N/A")
        gamma_trend = opt.get("gamma_trend", "UNKNOWN")
        vega_trend = opt.get("vega_trend", "UNKNOWN")
        avg_iv = opt.get("avg_implied_vol", 0)
        net_gamma = opt.get("net_gamma", 0)
        net_vega = opt.get("net_vega", 0)

        # HTML structure
        html_content = f"""
        <!DOCTYPE html><html><head><title>Money Market Checklist Report - {chk_date}</title>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; color: #333; }}
            h1, h2, h3 {{ color: #2c3e50; }}
            .container {{ max-width: 1200px; margin: auto; }}
            .header {{ background-color: #2c3e50; color: white; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; margin-bottom: 10px; }}
            .section {{ background-color: #f9f9f9; border: 1px solid #eee; border-radius: 5px; padding: 15px; margin-bottom: 15px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); }}
            .item {{ margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #eee; }}
            .item:last-child {{ border-bottom: none; }}
            .item-header {{ font-weight: bold; margin-bottom: 3px; }}
            .status {{ display: inline-block; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; margin-left: 8px; color: white; }}
            .completed {{ background-color: #2ecc71; }}
            .failed {{ background-color: #e74c3c; }}
            .info {{ background-color: #3498db; }}
            .result, .action {{ margin-top: 3px; font-size: 0.95em; }}
            .action {{ font-weight: bold; color: #c0392b; }}
            .bearish {{ color: #e74c3c; font-weight: bold; }}
            .bullish {{ color: #27ae60; font-weight: bold; }}
            .neutral {{ color: #2980b9; font-weight: bold; }}
            .summary {{ background-color: #ecf0f1; border: 1px solid #bdc3c7; padding: 15px; border-radius: 5px; margin-top: 20px; }}
            .images {{ display: flex; flex-wrap: wrap; justify-content: space-around; margin-top: 20px; }}
            .image-container {{ width: 48%; margin-bottom: 15px; text-align: center; }}
            @media (max-width: 768px) {{ .image-container {{ width: 100%; }} }}
            img {{ max-width: 100%; height: auto; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
            .simulated-note {{ font-style: italic; font-size: 0.9em; color: #7f8c8d; margin-top: 5px; }}
            .error-summary {{ background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin-top: 20px; }}
            .error-summary ul {{ margin: 0; padding-left: 20px; }}
        </style></head><body><div class="container">
        <div class="header"><h1>Money Market Checklist Report</h1><p>Date: {chk_date} | Ticker: {chk_ticker}</p></div>
        """
        # Add error summary if errors exist
        # Use set to avoid duplicate error messages
        unique_errors = sorted(list(set(self.error_summary)))
        if unique_errors:
            html_content += "<div class=\"error-summary\"><strong>Data Fetching/Analysis Issues:</strong><ul>"
            for error in unique_errors:
                html_content += f"<li>{error}</li>"
            html_content += "</ul></div>"

        html_content += f"""
        {generate_section_html("Pre-Market Preparation (7:00-9:30 AM ET)", chk.get("pre_market", {}))}
        {generate_section_html("Market Open Analysis (9:30-10:30 AM ET)", chk.get("market_open", {}))}
        {generate_section_html("Mid-Day Analysis (11:00 AM-1:00 PM ET) - Primary Trading Window", chk.get("mid_day", {}))}
        {generate_section_html("End-of-Day Analysis (3:00-4:00 PM ET)", chk.get("end_of_day", {}))}
        <div class="summary"><h2>Trading Summary</h2>
            <p><strong>Overall Bias:</strong> <span class="{mm_bias}">{mm_bias_text}</span> ({mm_conf})</p>
            <p><strong>Recommended Position:</strong> {opt_pos_type}</p>
            <p><strong>Recommended Expiry:</strong> {opt_expiry}</p>
            <p><strong>Position Size:</strong> {opt_size}</p>
            <p><strong>Rationale:</strong> {opt_rationale}</p>
            <h3>Greek Analysis</h3>
            <p><strong>Gamma Trend:</strong> {gamma_trend} (Net: {net_gamma:,.0f})</p>
            <p><strong>Vega Trend:</strong> {vega_trend} (Net: {net_vega:,.0f})</p>
            <p><strong>Average Implied Volatility:</strong> {avg_iv:.1%}</p>"""
        
        # Add VRP information to summary if available
        if 'volatility_metrics' in opt:
            vm = opt['volatility_metrics']
            current_hv_20 = vm.get('current_hv_20', 0)
            current_vrp_20 = vm.get('current_vrp_20', 0)
            vrp_percentile = vm.get('vrp_percentile', 50)
            html_content += f"""
            <h3>Volatility Risk Premium (VRP) Analysis</h3>
            <p><strong>20-Day Historical Vol:</strong> {current_hv_20:.1%}</p>
            <p><strong>20-Day VRP:</strong> {current_vrp_20:+.1%} ({vrp_percentile:.0f}th percentile)</p>"""
        
        html_content += """
        </div>
        <div class="images">
            <div class="image-container"><h3>Money Market Indicators</h3>
        """
        # Embed image or show placeholder if plot failed
        if self.plots_generated.get("liquidity") and os.path.exists(self.plots_generated["liquidity"]):
            html_content += f"<img src=\"money_market_indicators.png\" alt=\"Money Market Indicators\">"
        else:
            html_content += "<p><i>Liquidity plot could not be generated due to data issues.</i></p>"
        html_content += "</div><div class=\"image-container\"><h3>Option Strike Levels</h3>"
        if self.plots_generated.get("strike_levels") and os.path.exists(self.plots_generated["strike_levels"]):
            html_content += f"<img src=\"option_strike_levels.png\" alt=\"Option Strike Levels\">"
        else:
            html_content += "<p><i>Strike level plot could not be generated due to data issues.</i></p>"
        
        # Get data source from option signals analysis
        data_source = self.option_signals_analysis.get("data_source", "Unknown Data")
        html_content += f"<p class=\"simulated-note\">Option data source: {data_source}.</p></div>"
        
        # Add gamma wall plot
        html_content += "<div class=\"image-container\"><h3>Gamma Wall Analysis</h3>"
        if self.plots_generated.get("gamma_wall") and os.path.exists(self.plots_generated["gamma_wall"]):
            html_content += f"<img src=\"gamma_wall_analysis.png\" alt=\"Gamma Wall Analysis\">"
        else:
            html_content += "<p><i>Gamma wall plot could not be generated due to data issues.</i></p>"
        html_content += f"<p class=\"simulated-note\">Gamma analysis based on {data_source}.</p></div>"
        
        # Add VRP trend chart
        html_content += "<div class=\"image-container\"><h3>VRP Trend (20-Day)</h3>"
        if self.plots_generated.get("vrp_trend") and os.path.exists(self.plots_generated["vrp_trend"]):
            html_content += f"<img src=\"volatility_vrp_trend.png\" alt=\"VRP Trend Analysis\">"
        else:
            html_content += "<p><i>VRP trend chart could not be generated due to data issues.</i></p>"
        html_content += "</div>"
        
        # Add volatility comparison chart
        html_content += "<div class=\"image-container\"><h3>Historical vs Implied Volatility</h3>"
        if self.plots_generated.get("volatility_comparison") and os.path.exists(self.plots_generated["volatility_comparison"]):
            html_content += f"<img src=\"volatility_volatility_comparison.png\" alt=\"Volatility Comparison\">"
        else:
            html_content += "<p><i>Volatility comparison chart could not be generated due to data issues.</i></p>"
        html_content += "</div></div>"
        
        # Add volatility table section
        if self.plots_generated.get("volatility_table") and os.path.exists(self.plots_generated["volatility_table"]):
            html_content += "<div class=\"section\"><h2>Volatility & VRP Metrics</h2>"
            try:
                import pandas as pd
                vol_table = pd.read_csv(self.plots_generated["volatility_table"])
                html_content += "<table style=\"width: 100%; border-collapse: collapse; margin-top: 10px;\">"
                html_content += "<tr style=\"background-color: #2c3e50; color: white;\">"
                for col in vol_table.columns:
                    html_content += f"<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left;\">{col}</th>"
                html_content += "</tr>"
                for _, row in vol_table.iterrows():
                    html_content += "<tr>"
                    for col in vol_table.columns:
                        html_content += f"<td style=\"border: 1px solid #ddd; padding: 8px;\">{row[col]}</td>"
                    html_content += "</tr>"
                html_content += "</table>"
                
                # Add VRP interpretation
                if 'volatility_metrics' in self.option_signals_analysis:
                    vm = self.option_signals_analysis['volatility_metrics']
                    vrp_20 = vm.get('current_vrp_20', 0)
                    if vrp_20 > 0.05:
                        vrp_interpretation = "🔴 Options appear OVERPRICED - consider selling premium strategies"
                    elif vrp_20 < -0.05:
                        vrp_interpretation = "🟢 Options appear UNDERPRICED - consider buying strategies"
                    else:
                        vrp_interpretation = "🟡 Options appear FAIRLY VALUED - neutral positioning"
                    html_content += f"<p style=\"margin-top: 15px; font-weight: bold;\">{vrp_interpretation}</p>"
                
            except Exception as e:
                html_content += f"<p><i>Error loading volatility table: {e}</i></p>"
            html_content += "</div>"
        
        html_content += "</div></body></html>"

        try:
            with open(save_path, "w", encoding="utf-8") as f:
                f.write(html_content)
            print(f"HTML report saved to {save_path}")
            return save_path
        except Exception as e:
            print(f"Error writing HTML report: {e}")
            self.error_summary.append(f"Error writing HTML report: {e}")
            return None

    def generate_pdf_report(self, save_path=None):
        """
        Generate PDF report summarizing the checklist status.
        (Simplified text-based version)
        """
        print("\n6. Generating PDF Report...")
        if not self.checklist_status:
            print("Checklist status not generated, cannot create PDF report.")
            self.error_summary.append("PDF Report generation failed: Checklist status missing")
            return None

        save_path = save_path or os.path.join(self.output_dir, "checklist_report.pdf")
        mm = self.money_market_analysis
        opt = self.option_signals_analysis
        chk = self.checklist_status

        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=10)
        pdf.set_auto_page_break(auto=True, margin=15)

        def add_section(title, section_data):
            pdf.set_font("Arial", "B", 12)
            pdf.cell(0, 8, title, 0, 1, "L")
            pdf.set_font("Arial", size=9)
            if not section_data:
                pdf.cell(0, 4, "    No data available for this section.", 0, 1, "L")
            else:
                for key, item in section_data.items():
                    pdf.set_font("Arial", "B", 9)
                    # Extract status and formatted key before f-string
                    item_status = item.get("status", "?")
                    formatted_key = key.replace("_", " ").title()
                    pdf.cell(0, 5, f"> {formatted_key} ({item_status})", 0, 1, "L")
                    pdf.set_font("Arial", size=9)
                    
                    # Extract result and action before f-string
                    result_text = item.get("result", "N/A")
                    action_text = item.get("action", "N/A")
                    
                    # Use proper width calculation for indented multi_cell
                    available_width = pdf.w - pdf.l_margin - pdf.r_margin - 10  # 10 for indentation
                    
                    # Set position for indented text
                    pdf.set_x(pdf.l_margin + 10)
                    pdf.multi_cell(available_width, 4, f"Result: {result_text}", 0, "L")
                    pdf.set_x(pdf.l_margin + 10)
                    pdf.multi_cell(available_width, 4, f"Action: {action_text}", 0, "L")
                    pdf.ln(2)
            pdf.ln(3)

        # Header
        pdf.set_font("Arial", "B", 16)
        # Corrected f-string usage: extract values first
        chk_date = chk.get("date", "N/A")
        chk_ticker = chk.get("ticker", "N/A")
        pdf.cell(0, 10, f"Money Market Checklist Report - {chk_date}", 0, 1, "C")
        pdf.cell(0, 10, f"Ticker: {chk_ticker}", 0, 1, "C")
        pdf.ln(5)

        # Add error summary if errors exist
        unique_errors = sorted(list(set(self.error_summary)))
        if unique_errors:
            pdf.set_font("Arial", "B", 10)
            pdf.set_text_color(255, 0, 0) # Red color for errors
            pdf.cell(0, 6, "Data Fetching/Analysis Issues:", 0, 1, "L")
            pdf.set_font("Arial", size=9)
            for error in unique_errors:
                pdf.multi_cell(0, 4, f"- {error}", 0, "L")
            pdf.set_text_color(0, 0, 0) # Reset color
            pdf.ln(3)

        # Sections
        add_section("Pre-Market Preparation (7:00-9:30 AM ET)", chk.get("pre_market", {}))
        add_section("Market Open Analysis (9:30-10:30 AM ET)", chk.get("market_open", {}))
        add_section("Mid-Day Analysis (11:00 AM-1:00 PM ET)", chk.get("mid_day", {}))
        add_section("End-of-Day Analysis (3:00-4:00 PM ET)", chk.get("end_of_day", {}))

        # Trading Summary
        pdf.set_font("Arial", "B", 12)
        pdf.cell(0, 8, "Trading Summary", 0, 1, "L")
        pdf.set_font("Arial", size=9)
        # Extract values before f-string
        mm_bias = mm.get("bias", "UNKNOWN")
        mm_conf = mm.get("confidence", "UNKNOWN")
        opt_pos_type = opt.get("position_type", "N/A")
        opt_expiry = opt.get("recommended_expiry", "N/A")
        opt_size = opt.get("position_size", "N/A")
        opt_rationale = opt.get("rationale", "N/A")
        pdf.cell(0, 5, f"Overall Bias: {mm_bias} ({mm_conf})", 0, 1, "L")
        pdf.cell(0, 5, f"Recommended Position: {opt_pos_type}", 0, 1, "L")
        pdf.cell(0, 5, f"Recommended Expiry: {opt_expiry}", 0, 1, "L")
        pdf.cell(0, 5, f"Position Size: {opt_size}", 0, 1, "L")
        pdf.multi_cell(0, 5, f"Rationale: {opt_rationale}", 0, "L")
        pdf.ln(5)

        # Enhanced VRP Analysis Section
        pdf.set_font("Arial", "B", 12)
        pdf.cell(0, 8, "Volatility Risk Premium (VRP) Analysis", 0, 1, "L")
        pdf.set_font("Arial", size=9)
        
        # Extract VRP values
        vrp_30d = opt.get("current_vrp_30", 0)
        vrp_60d = opt.get("current_vrp_60", 0)
        vrp_signal = opt.get("vrp_signal", "N/A")
        vrp_strategy = opt.get("vrp_strategy", "N/A")
        vrp_rationale = opt.get("vrp_rationale", "N/A")
        current_iv = opt.get("current_iv", 0)
        hv_20 = opt.get("current_hv_20", 0)
        hv_30 = opt.get("current_hv_30", 0)
        
        pdf.cell(0, 5, f"Current Implied Volatility: {current_iv*100:.1f}%", 0, 1, "L")
        pdf.cell(0, 5, f"20-Day Historical Volatility: {hv_20*100:.1f}%", 0, 1, "L")
        pdf.cell(0, 5, f"30-Day Historical Volatility: {hv_30*100:.1f}%", 0, 1, "L")
        pdf.cell(0, 5, f"30-Day VRP: {vrp_30d*100:+.1f}%", 0, 1, "L")
        pdf.cell(0, 5, f"60-Day VRP: {vrp_60d*100:+.1f}%", 0, 1, "L")
        pdf.cell(0, 5, f"VRP Signal: {vrp_signal}", 0, 1, "L")
        pdf.multi_cell(0, 5, f"VRP Strategy: {vrp_strategy}", 0, "L")
        pdf.multi_cell(0, 5, f"VRP Rationale: {vrp_rationale}", 0, "L")
        pdf.ln(5)

        # Fed Net Liquidity Assessment Section
        pdf.set_font("Arial", "B", 12)
        pdf.cell(0, 8, "Fed Net Liquidity Assessment", 0, 1, "L")
        pdf.set_font("Arial", size=9)
        
        # Extract Fed liquidity values
        fed_assessment = opt.get("fed_net_liquidity_assessment", {})
        net_liquidity = fed_assessment.get("net_liquidity", 0)
        liquidity_trend = fed_assessment.get("liquidity_trend", "UNKNOWN")
        liquidity_condition = fed_assessment.get("liquidity_condition", "UNKNOWN")
        liquidity_impact = fed_assessment.get("liquidity_impact", "UNKNOWN")
        
        pdf.cell(0, 5, f"Fed Net Liquidity: ${net_liquidity:.0f}B", 0, 1, "L")
        pdf.cell(0, 5, f"Liquidity Trend: {liquidity_trend}", 0, 1, "L")
        pdf.cell(0, 5, f"Liquidity Condition: {liquidity_condition}", 0, 1, "L")
        pdf.multi_cell(0, 5, f"Market Impact: {liquidity_impact}", 0, "L")
        pdf.ln(5)

        # Risk Management Recommendations Section
        pdf.set_font("Arial", "B", 12)
        pdf.cell(0, 8, "Risk Management Recommendations", 0, 1, "L")
        pdf.set_font("Arial", size=9)
        
        # Extract risk management values
        risk_mgmt = opt.get("risk_management_recommendations", {})
        position_sizing = risk_mgmt.get("position_sizing", "UNKNOWN")
        gamma_risk = risk_mgmt.get("gamma_risk", "UNKNOWN")
        vega_risk = risk_mgmt.get("vega_risk", "UNKNOWN")
        liquidity_risk = risk_mgmt.get("liquidity_risk", "UNKNOWN")
        overall_risk = risk_mgmt.get("overall_risk_level", "UNKNOWN")
        
        pdf.multi_cell(0, 5, f"Overall Risk Level: {overall_risk}", 0, "L")
        pdf.multi_cell(0, 5, f"Position Sizing: {position_sizing}", 0, "L")
        pdf.multi_cell(0, 5, f"Gamma Risk: {gamma_risk}", 0, "L")
        pdf.multi_cell(0, 5, f"Vega Risk: {vega_risk}", 0, "L")
        pdf.multi_cell(0, 5, f"Liquidity Risk: {liquidity_risk}", 0, "L")
        pdf.ln(5)

        # Option Strike Level Analysis Section
        pdf.set_font("Arial", "B", 12)
        pdf.cell(0, 8, "Option Strike Level Analysis", 0, 1, "L")
        pdf.set_font("Arial", size=9)
        
        # Extract option analysis values
        net_gamma = opt.get("net_gamma", 0)
        net_vega = opt.get("net_vega", 0)
        gamma_trend = opt.get("gamma_trend", "UNKNOWN")
        vega_trend = opt.get("vega_trend", "UNKNOWN")
        nearest_call_wall = opt.get("nearest_call_wall", "N/A")
        nearest_put_wall = opt.get("nearest_put_wall", "N/A")
        
        pdf.cell(0, 5, f"Net Gamma: {net_gamma:,.0f}", 0, 1, "L")
        pdf.cell(0, 5, f"Net Vega: {net_vega:,.0f}", 0, 1, "L")
        pdf.cell(0, 5, f"Gamma Trend: {gamma_trend}", 0, 1, "L")
        pdf.cell(0, 5, f"Vega Trend: {vega_trend}", 0, 1, "L")
        pdf.cell(0, 5, f"Nearest Call Wall: {nearest_call_wall}", 0, 1, "L")
        pdf.cell(0, 5, f"Nearest Put Wall: {nearest_put_wall}", 0, 1, "L")
        pdf.ln(5)

        # Note about data sources
        pdf.set_font("Arial", "I", 9)
        option_data_source = self.option_signals_analysis.get("data_source", "Unknown Data")
        pdf.cell(0, 5, f"Note: Option data source: {option_data_source}. Treasury auction results are currently simulated.", 0, 1, "L")

        # Add Plots if available
        plots = self.plots_generated
        # Define page_width here, before using it
        page_width = pdf.w - 2 * pdf.l_margin

        if plots.get("liquidity") and os.path.exists(plots["liquidity"]):
            try:
                pdf.add_page()
                pdf.set_font("Arial", "B", 12)
                pdf.cell(0, 10, "Money Market Indicators Plot", 0, 1, "C")
                pdf.image(plots["liquidity"], x=pdf.l_margin, y=None, w=page_width)
                pdf.ln(5)
            except Exception as e:
                print(f"Error adding liquidity plot to PDF: {e}")
                self.error_summary.append(f"Error adding liquidity plot to PDF: {e}")
                # Add text placeholder if image fails
                if pdf.page_no() > 0: # Check if page exists
                    pdf.set_font("Arial", "I", 9)
                    pdf.cell(0, 5, "[Liquidity plot could not be embedded]", 0, 1, "C")

        if plots.get("strike_levels") and os.path.exists(plots["strike_levels"]):
            try:
                pdf.add_page()
                pdf.set_font("Arial", "B", 12)
                option_data_source = self.option_signals_analysis.get("data_source", "Unknown Data")
                pdf.cell(0, 10, f"Option Strike Levels Plot ({option_data_source})", 0, 1, "C")
                pdf.image(plots["strike_levels"], x=pdf.l_margin, y=None, w=page_width)
            except Exception as e:
                print(f"Error adding strike level plot to PDF: {e}")
                self.error_summary.append(f"Error adding strike level plot to PDF: {e}")
                # Add text placeholder if image fails
                if pdf.page_no() > 0: # Check if page exists
                    pdf.set_font("Arial", "I", 9)
                    pdf.cell(0, 5, "[Strike level plot could not be embedded]", 0, 1, "C")

        if plots.get("gamma_wall") and os.path.exists(plots["gamma_wall"]):
            try:
                pdf.add_page()
                pdf.set_font("Arial", "B", 12)
                option_data_source = self.option_signals_analysis.get("data_source", "Unknown Data")
                pdf.cell(0, 10, f"Gamma Wall Analysis ({option_data_source})", 0, 1, "C")
                pdf.image(plots["gamma_wall"], x=pdf.l_margin, y=None, w=page_width)
            except Exception as e:
                print(f"Error adding gamma wall plot to PDF: {e}")
                self.error_summary.append(f"Error adding gamma wall plot to PDF: {e}")
                # Add text placeholder if image fails
                if pdf.page_no() > 0: # Check if page exists
                    pdf.set_font("Arial", "I", 9)
                    pdf.cell(0, 5, "[Gamma wall plot could not be embedded]", 0, 1, "C")

        # Add VRP Trend Chart
        if plots.get("vrp_trend") and os.path.exists(plots["vrp_trend"]):
            try:
                pdf.add_page()
                pdf.set_font("Arial", "B", 12)
                pdf.cell(0, 10, "Volatility Risk Premium (VRP) Trend Analysis", 0, 1, "C")
                pdf.image(plots["vrp_trend"], x=pdf.l_margin, y=None, w=page_width)
            except Exception as e:
                print(f"Error adding VRP trend plot to PDF: {e}")
                self.error_summary.append(f"Error adding VRP trend plot to PDF: {e}")
                if pdf.page_no() > 0:
                    pdf.set_font("Arial", "I", 9)
                    pdf.cell(0, 5, "[VRP trend plot could not be embedded]", 0, 1, "C")

        # Add Volatility Comparison Chart
        if plots.get("volatility_comparison") and os.path.exists(plots["volatility_comparison"]):
            try:
                pdf.add_page()
                pdf.set_font("Arial", "B", 12)
                pdf.cell(0, 10, "Historical vs Implied Volatility Comparison", 0, 1, "C")
                pdf.image(plots["volatility_comparison"], x=pdf.l_margin, y=None, w=page_width)
            except Exception as e:
                print(f"Error adding volatility comparison plot to PDF: {e}")
                self.error_summary.append(f"Error adding volatility comparison plot to PDF: {e}")
                if pdf.page_no() > 0:
                    pdf.set_font("Arial", "I", 9)
                    pdf.cell(0, 5, "[Volatility comparison plot could not be embedded]", 0, 1, "C")

        try:
            pdf.output(save_path)
            print(f"PDF report saved to {save_path}")
            return save_path
        except Exception as e:
            print(f"Error writing PDF report: {e}")
            self.error_summary.append(f"Error writing PDF report: {e}")
            return None

    def run_full_analysis(self, generate_html=True, generate_pdf=True):
        """Run the complete analysis pipeline with real data."""
        start_time = time.time()
        self.run_money_market_analysis()
        self.run_option_signals_analysis()
        self.generate_checklist_status()
        self.plots_generated = self.generate_plots() # Store plot paths

        html_path, pdf_path = None, None
        if generate_html: html_path = self.generate_html_report()
        if generate_pdf: pdf_path = self.generate_pdf_report()

        end_time = time.time()
        print(f"\nFull analysis completed in {end_time - start_time:.2f} seconds.")

        return {
            "money_market_analysis": self.money_market_analysis,
            "option_signals_analysis": self.option_signals_analysis,
            "checklist_status": self.checklist_status,
            "plots": self.plots_generated,
            "html_report": html_path,
            "pdf_report": pdf_path
        }

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run Money Market Checklist Driver with Real Data")
    parser.add_argument("--ticker", type=str, default="^GSPC", help="Ticker symbol (default: ^GSPC)")
    parser.add_argument("--days_back", type=int, default=90, help="Days of history (default: 90)")
    parser.add_argument("--output_dir", type=str, default=None, help="Output directory (default: ./output)")
    parser.add_argument("--output_format", type=str, default="both", choices=["html", "pdf", "both"], help="Report format (default: both)")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    driver = ChecklistDriver(ticker=args.ticker, days_back=args.days_back, output_dir=args.output_dir)
    results = driver.run_full_analysis(
        generate_html=args.output_format in ["html", "both"],
        generate_pdf=args.output_format in ["pdf", "both"]
    )

    # Print summary
    print("\n--- Enhanced Market Analysis Summary ---")
    if results and results.get("checklist_status") and results.get("checklist_status").get("date"):
        chk = results["checklist_status"]
        mm = results["money_market_analysis"]
        opt = results["option_signals_analysis"]
        # Extract values before f-string
        chk_date = chk.get("date", "N/A")
        chk_ticker = chk.get("ticker", "N/A")
        mm_bias = mm.get("bias", "UNKNOWN")
        mm_conf = mm.get("confidence", "UNKNOWN")
        # Get combined action from checklist status, not directly from function
        combined_action = chk.get("mid_day", {}).get("combined_signal", {}).get("action", "N/A")
        html_report_path = results.get("html_report")
        pdf_report_path = results.get("pdf_report")

        print(f"Date: {chk_date}")
        print(f"Ticker: {chk_ticker}")
        print(f"Overall Bias: {mm_bias} ({mm_conf})", flush=True)
        print(f"Recommended Action: {combined_action}", flush=True)
        
        # Enhanced VRP Analysis Summary
        print("\n📈 VRP ANALYSIS:")
        vrp_30d = opt.get("current_vrp_30", 0)
        vrp_60d = opt.get("current_vrp_60", 0)
        vrp_signal = opt.get("vrp_signal", "N/A")
        print(f"  • 30-Day VRP: {vrp_30d*100:+.1f}%")
        print(f"  • 60-Day VRP: {vrp_60d*100:+.1f}%")
        print(f"  • VRP Signal: {vrp_signal}")
        
        # Fed Net Liquidity Summary
        print("\n🏦 FED LIQUIDITY:")
        fed_assessment = opt.get("fed_net_liquidity_assessment", {})
        net_liquidity = fed_assessment.get("net_liquidity", 0)
        liquidity_impact = fed_assessment.get("liquidity_impact", "N/A")
        print(f"  • Net Liquidity: ${net_liquidity:.0f}B")
        print(f"  • Market Impact: {liquidity_impact}")
        
        # Risk Management Summary
        print("\n⚠️ RISK MANAGEMENT:")
        risk_mgmt = opt.get("risk_management_recommendations", {})
        overall_risk = risk_mgmt.get("overall_risk_level", "N/A")
        position_sizing = risk_mgmt.get("position_sizing", "N/A")
        print(f"  • Risk Level: {overall_risk}")
        print(f"  • Position Sizing: {position_sizing}")
        
        # Option Analysis Summary
        print("\n🎯 OPTION ANALYSIS:")
        net_gamma = opt.get("net_gamma", 0)
        current_iv = opt.get("current_iv", 0)
        print(f"  • Net Gamma: {net_gamma:,.0f}")
        print(f"  • Current IV: {current_iv*100:.1f}%")
        
        print("\n📊 REPORTS GENERATED:")
        if html_report_path: print(f"  • HTML Report: {html_report_path}", flush=True)
        if pdf_report_path: print(f"  • PDF Report: {pdf_report_path}", flush=True)

        # Print error summary if any errors occurred
        unique_errors = sorted(list(set(driver.error_summary)))
        if unique_errors:
            print("\n--- Errors/Warnings ---", flush=True)
            for error in unique_errors:
                print(f"- {error}", flush=True)
    else:
        print("Analysis could not be completed or checklist status is missing.", flush=True)

