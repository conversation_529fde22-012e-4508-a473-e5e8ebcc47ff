"""
Main Driver
Orchestrates the complete market analysis workflow.
"""

import os
import sys
import argparse
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# Load environment variables FIRST before any other imports
load_dotenv()

# Add src directory to path (same approach as test scripts)
sys.path.append('src')

# Import all modules
from market_data.polygon_client import PolygonDataFetcher
from market_data.price_data import PriceDataFetcher
from economic_data.fred_client import FredDataFetcher
from economic_data.treasury_client import FiscalDataFetcher
from analytics.volatility_analyzer import VolatilityAnalyzer
from analytics.option_analyzer import OptionAnalyzer
from analytics.options_flow_analyzer import OptionsFlowAnalyzer
from analytics.put_call_analyzer import PutCallAnalyzer
from analytics.money_market_analyzer import MoneyMarketAnalyzer
from reporting.html_generator import HTMLReportGenerator
from reporting.chart_generator import ChartGenerator
from reporting.pdf_generator import PDFReportGenerator
from reporting.checklist_formatter import ChecklistFormatter
from reporting.narrative_generator import NarrativeGenerator
from reporting.summary_table_generator import SummaryTableGenerator


class MarketAnalysisDriver:
    """Main driver for comprehensive market analysis."""
    
    def __init__(self, ticker: str = None, price_ticker: str = None, options_ticker: str = None, 
                 days_back: int = 30, output_dir: str = None, enable_narrative: bool = False,
                 analysis_type: str = "gamma_vanna"):
        # Configuration - handle ticker mapping properly
        base_ticker = ticker or os.getenv('DEFAULT_TICKER', 'SPX')
        
        # Set up tickers for different data sources
        if base_ticker.upper() == 'SPX':
            # SPX special case - different tickers for different APIs
            self.price_ticker = price_ticker or "^GSPC"  # Yahoo Finance
            self.options_ticker = options_ticker or "SPX"  # Polygon API (clean ticker)
            self.display_ticker = "SPX"  # For display purposes
        elif base_ticker.upper() == 'NDX':
            # NDX special case
            self.price_ticker = price_ticker or "^NDX"  # Yahoo Finance
            self.options_ticker = options_ticker or "NDX"  # Polygon API
            self.display_ticker = "NDX"  # For display purposes
        else:
            # Other tickers - use provided tickers or fall back to base ticker
            self.price_ticker = price_ticker or base_ticker
            self.options_ticker = options_ticker or base_ticker
            self.display_ticker = base_ticker
        
        # Legacy compatibility
        self.ticker = self.options_ticker  # For backward compatibility with existing code
        
        self.days_back = days_back
        self.output_dir = output_dir or os.getenv('DEFAULT_OUTPUT_DIR', 'output')
        self.enable_narrative = enable_narrative
        self.analysis_type = analysis_type
        
        # Date range - match original script requirements
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=max(365, days_back * 12))
        
        # Initialize components
        self.polygon_client = PolygonDataFetcher()
        self.price_fetcher = PriceDataFetcher()
        self.fred_client = FredDataFetcher()
        self.fiscal_client = FiscalDataFetcher()
        
        self.volatility_analyzer = VolatilityAnalyzer(self.display_ticker, 252, self.output_dir)  # Match original: 252 days
        self.option_analyzer = OptionAnalyzer(self.output_dir)
        self.options_flow_analyzer = OptionsFlowAnalyzer(self.output_dir)
        self.put_call_analyzer = PutCallAnalyzer(self.output_dir)
        self.money_market_analyzer = MoneyMarketAnalyzer()
        
        self.chart_generator = ChartGenerator(self.output_dir)
        self.html_generator = HTMLReportGenerator(self.output_dir)
        self.pdf_generator = PDFReportGenerator(self.output_dir)
        self.checklist_formatter = ChecklistFormatter()
        self.narrative_generator = NarrativeGenerator()
        self.summary_table_generator = SummaryTableGenerator()
        
        # Results storage
        self.price_data = pd.DataFrame()
        self.option_chain = None
        self.money_market_analysis = {}
        self.option_signals_analysis = {}
        self.options_flow_analysis = {}
        self.put_call_analysis = {}
        self.volatility_metrics = {}
        self.volatility_table = pd.DataFrame()
        self.treasury_auction_analysis = {}
        self.treasury_auction_table = pd.DataFrame()
        self.executive_summary = pd.DataFrame()
        self.plots_generated = {}
        self.error_summary = []
        self.macro_analysis = ""
        self.trading_narrative = ""
        
        print(f"Initialized Market Analysis Driver for {self.display_ticker}")
        print(f"  - Options ticker (Polygon): {self.options_ticker}")
        print(f"  - Price ticker (yfinance): {self.price_ticker}")
        print(f"  - Display ticker: {self.display_ticker}")
        print(f"Analysis period: {self.start_date.date()} to {self.end_date.date()}")
        print(f"Output directory: {self.output_dir}")
        
    def run_complete_analysis(self) -> Dict[str, Any]:
        """Run the complete market analysis workflow."""
        print("\n" + "="*60)
        print("STARTING GAMMA/VANNA ANALYSIS")
        print("="*60)
        
        try:
            # Step 1: Fetch price data
            self._fetch_price_data()
            
            # Step 2: Fetch option chain data
            self._fetch_option_data()
            
            # Step 3: Run money market analysis for liquidity indicators
            self._run_money_market_analysis()
            
            # Step 4: Run option signals analysis
            self._run_option_signals_analysis()

            # Step 5: Run volatility analysis
            self._run_volatility_analysis()

            # Step 6: Run treasury auction analysis
            self._run_treasury_analysis()

            # Step 7: Run put/call analysis
            self._run_put_call_analysis()

            # Step 8: Generate charts
            self._generate_charts()
            
            # Step 9: Generate narrative (always enabled for comprehensive analysis)
            self._generate_narrative()

            # Step 10: Generate reports
            report_path = self._generate_reports()
            
            print("\n" + "="*60)
            print("ANALYSIS COMPLETE")
            print("="*80)
            print(f"Report generated: {report_path}")
            
            return {
                "success": True,
                "html_report_path": report_path,
                "money_market_analysis": self.money_market_analysis,
                "option_signals_analysis": self.option_signals_analysis,
                "options_flow_analysis": self.options_flow_analysis,
                "error_summary": self.error_summary
            }
            
        except Exception as e:
            error_msg = f"Analysis failed: {str(e)}"
            print(f"\n❌ {error_msg}")
            self.error_summary.append(error_msg)
            
            return {
                "success": False,
                "error": error_msg,
                "error_summary": self.error_summary
            }
    
    def _fetch_price_data(self):
        """Fetch historical price data."""
        print("\n1. Fetching Price Data...")
        
        try:
            # Calculate days needed for analysis
            days_needed = max(365, self.days_back * 12)
            
            self.price_data = self.price_fetcher.get_historical_prices(
                self.price_ticker, days_needed
            )
            
            if self.price_data.empty:
                raise ValueError(f"No price data available for {self.price_ticker}")
            
            print(f"✓ Price data fetched: {len(self.price_data)} records")
            print(f"  Latest price: ${self.price_data['Close'].iloc[-1]:.2f}")
            
            # Set price data for volatility analyzer
            self.volatility_analyzer.set_price_data(self.price_data)
            
        except Exception as e:
            error_msg = f"Price data fetch failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
            raise
    
    def _fetch_option_data(self):
        """Fetch option chain data."""
        print("\n2. Fetching Option Chain Data...")
        
        try:
            self.option_chain = self.polygon_client.fetch_option_chain(self.options_ticker)
            
            if not self.option_chain:
                raise ValueError(f"No option chain data available for {self.options_ticker}")
            
            calls_count = len(self.option_chain.get("calls", []))
            puts_count = len(self.option_chain.get("puts", []))
            current_price = self.option_chain.get("current_price", 0)
            
            print(f"✓ Option chain fetched: {calls_count} calls, {puts_count} puts")
            print(f"  Current price: ${current_price:.2f}")
            
        except Exception as e:
            error_msg = f"Option chain fetch failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
            
            # Create fallback option chain
            self.option_chain = {
                "current_price": self.price_data['Close'].iloc[-1] if not self.price_data.empty else 0,
                "calls": [],
                "puts": [],
                "error": error_msg
            }

    def _run_money_market_analysis(self):
        """Run money market analysis for liquidity indicators."""
        print("\n3. Running Money Market Analysis...")
        
        try:
            # Fetch money market data using correct method names
            fed_data = self.fred_client.get_fed_balance_sheet(self.start_date, self.end_date)
            rrp_data = self.fred_client.get_reverse_repo(self.start_date, self.end_date)
            tga_data = self.fiscal_client.fetch_tga_balance(self.start_date, self.end_date)
            treasury_data = self.fred_client.get_treasury_yield(self.start_date, self.end_date)
            
            # Calculate Fed Net Liquidity
            self.money_market_analyzer.liquidity_df = self.money_market_analyzer.calculate_fed_net_liquidity(
                fed_data, rrp_data, tga_data, treasury_data, self.start_date, self.end_date
            )
            
            # Analyze liquidity conditions
            liquidity_analysis = self.money_market_analyzer.analyze_liquidity_conditions()
            
            # Generate trading bias
            self.money_market_analysis = self.money_market_analyzer.get_trading_bias(liquidity_analysis)
            
            print(f"✓ Money market analysis complete")
            print(f"  Market Bias: {self.money_market_analysis.get('bias', 'Unknown')}")
            print(f"  Liquidity Condition: {self.money_market_analysis.get('liquidity_condition', 'Unknown')}")
            
        except Exception as e:
            error_msg = f"Money market analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
            
            # Create fallback analysis
            self.money_market_analysis = {
                "bias": "NEUTRAL",
                "confidence": "LOW",
                "liquidity_condition": "UNKNOWN",
                "liquidity_trend": "UNKNOWN",
                "error": error_msg
            }
    
    def _run_option_signals_analysis(self):
        """Run comprehensive option signals analysis."""
        print("\n4. Running Option Signals Analysis...")
        
        try:
            # Check if we have option data
            if not self.option_chain:
                raise ValueError("No option chain data available")
            
            # Basic option signals analysis
            self.option_analyzer.set_option_chain(self.option_chain)
            strike_levels = self.option_analyzer.identify_strike_levels()
            
            if not strike_levels:
                raise ValueError("Strike level analysis failed")
            
            self.option_signals_analysis = self.option_analyzer.generate_option_signals(self.money_market_analysis)
            
            # Advanced options flow analysis
            print("   Running advanced options flow analysis...")
            self.options_flow_analysis = self.options_flow_analyzer.analyze_dealer_positioning()
            
            print(f"✓ Option signals analysis complete")
            print(f"  Basic Signal: {self.option_signals_analysis.get('signal', 'Unknown')}")
            print(f"  Flow Signal: {self.options_flow_analysis.get('flow_signals', {}).get('primary_signal', 'Unknown')}")
            
        except Exception as e:
            error_msg = f"Option signals analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
            
            # Create fallback analysis
            self.option_signals_analysis = {
                "signal": "NO_DATA",
                "confidence": "NONE",
                "error": error_msg,
                "rationale": "No option data available from Polygon API",
                "data_source": "None"
            }
            self.options_flow_analysis = {"error": error_msg}

    def _run_volatility_analysis(self):
        """Run comprehensive volatility and VRP analysis."""
        print("\n5. Running Volatility Analysis...")

        try:
            # Run volatility analysis with option chain data
            self.volatility_metrics = self.volatility_analyzer.analyze_volatility_metrics(self.option_chain)

            if self.volatility_metrics:
                print(f"✓ Volatility analysis completed")
                print(f"  Current 20-day HV: {self.volatility_metrics.get('current_hv_20', 0):.1%}")
                print(f"  Current IV: {self.volatility_metrics.get('current_iv', 0):.1%}")
                print(f"  Current 20-day VRP: {self.volatility_metrics.get('current_vrp_20', 0):+.1%}")

                # Create volatility table
                self.volatility_table = self.volatility_analyzer.create_volatility_table(self.volatility_metrics)

                # Initialize other analysis components for PDF
                self.executive_summary_table = None
                self.macro_analysis = ""
                self.put_call_analysis = {}
                self.treasury_auction_analysis = {}
                self.treasury_auction_table = None

            else:
                print("⚠️ Volatility analysis failed")
                self.volatility_metrics = {}
                self.volatility_table = None

        except Exception as e:
            error_msg = f"Volatility analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
            self.volatility_metrics = {}
            self.volatility_table = None

    def _run_treasury_analysis(self):
        """Run treasury auction analysis."""
        print("\n6. Running Treasury Auction Analysis...")

        try:
            # Fetch treasury auction data
            self.treasury_auction_table = self.fiscal_client.fetch_treasury_auctions(self.start_date, self.end_date)

            if not self.treasury_auction_table.empty:
                # Analyze auction data
                avg_bid_to_cover = self.treasury_auction_table['bid_to_cover_ratio'].mean() if 'bid_to_cover_ratio' in self.treasury_auction_table.columns else 0
                avg_yield = self.treasury_auction_table['high_yield'].mean() if 'high_yield' in self.treasury_auction_table.columns else 0
                total_issued = self.treasury_auction_table['issue_amount'].sum() if 'issue_amount' in self.treasury_auction_table.columns else 0

                # Determine trend
                recent_btc = self.treasury_auction_table['bid_to_cover_ratio'].tail(5).mean() if 'bid_to_cover_ratio' in self.treasury_auction_table.columns else 0
                historical_btc = self.treasury_auction_table['bid_to_cover_ratio'].head(-5).mean() if len(self.treasury_auction_table) > 5 and 'bid_to_cover_ratio' in self.treasury_auction_table.columns else recent_btc

                if recent_btc > historical_btc * 1.05:
                    trend = "Improving"
                elif recent_btc < historical_btc * 0.95:
                    trend = "Deteriorating"
                else:
                    trend = "Stable"

                self.treasury_auction_analysis = {
                    "avg_bid_to_cover": avg_bid_to_cover,
                    "avg_yield": avg_yield,
                    "total_issued_billions": total_issued / 1e9,
                    "bid_to_cover_trend": trend,
                    "auction_count": len(self.treasury_auction_table),
                    "assessment": "Strong demand" if avg_bid_to_cover > 2.5 else "Weak demand" if avg_bid_to_cover < 2.0 else "Average demand"
                }

                print(f"✓ Treasury auction analysis completed")
                print(f"  Average Bid-to-Cover: {avg_bid_to_cover:.2f}")
                print(f"  Trend: {trend}")

            else:
                print("⚠️ No treasury auction data available")
                self.treasury_auction_analysis = {
                    "avg_bid_to_cover": 0,
                    "avg_yield": 0,
                    "total_issued_billions": 0,
                    "bid_to_cover_trend": "Unknown",
                    "auction_count": 0,
                    "assessment": "No data available"
                }

        except Exception as e:
            error_msg = f"Treasury auction analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
            self.treasury_auction_analysis = {}
            self.treasury_auction_table = pd.DataFrame()

    def _run_put_call_analysis(self):
        """Run comprehensive put/call ratio analysis."""
        print("\n7. Running Put/Call Analysis...")

        try:
            if not self.option_chain:
                print("⚠️ No option chain data for put/call analysis")
                self.put_call_analysis = {}
                return

            calls = self.option_chain.get('calls', [])
            puts = self.option_chain.get('puts', [])
            current_price = self.option_chain.get('current_price', 0)

            if not calls or not puts:
                print("⚠️ Insufficient option data for put/call analysis")
                self.put_call_analysis = {}
                return

            # Calculate put/call ratios
            total_call_volume = sum(call.get('volume', 0) for call in calls)
            total_put_volume = sum(put.get('volume', 0) for put in puts)
            total_call_oi = sum(call.get('open_interest', 0) for call in calls)
            total_put_oi = sum(put.get('open_interest', 0) for put in puts)

            # Volume-based P/C ratio
            pc_volume_ratio = total_put_volume / total_call_volume if total_call_volume > 0 else 0

            # Open Interest-based P/C ratio
            pc_oi_ratio = total_put_oi / total_call_oi if total_call_oi > 0 else 0

            # ATM put/call analysis (within 2% of current price)
            atm_range = current_price * 0.02
            atm_calls = [c for c in calls if abs(c.get('strike', 0) - current_price) <= atm_range]
            atm_puts = [p for p in puts if abs(p.get('strike', 0) - current_price) <= atm_range]

            atm_call_volume = sum(call.get('volume', 0) for call in atm_calls)
            atm_put_volume = sum(put.get('volume', 0) for put in atm_puts)
            atm_pc_ratio = atm_put_volume / atm_call_volume if atm_call_volume > 0 else 0

            # Sentiment analysis
            if pc_volume_ratio > 1.2:
                sentiment = "BEARISH"
                sentiment_strength = "Strong"
            elif pc_volume_ratio > 0.8:
                sentiment = "NEUTRAL"
                sentiment_strength = "Moderate"
            else:
                sentiment = "BULLISH"
                sentiment_strength = "Strong"

            # Skew analysis (simplified)
            otm_puts = [p for p in puts if p.get('strike', 0) < current_price * 0.95]
            otm_calls = [c for c in calls if c.get('strike', 0) > current_price * 1.05]

            otm_put_volume = sum(put.get('volume', 0) for put in otm_puts)
            otm_call_volume = sum(call.get('volume', 0) for call in otm_calls)

            skew_ratio = otm_put_volume / otm_call_volume if otm_call_volume > 0 else 0

            self.put_call_analysis = {
                "pc_volume_ratio": pc_volume_ratio,
                "pc_oi_ratio": pc_oi_ratio,
                "atm_pc_ratio": atm_pc_ratio,
                "sentiment": sentiment,
                "sentiment_strength": sentiment_strength,
                "skew_ratio": skew_ratio,
                "total_call_volume": total_call_volume,
                "total_put_volume": total_put_volume,
                "total_call_oi": total_call_oi,
                "total_put_oi": total_put_oi,
                "interpretation": f"{sentiment_strength} {sentiment.lower()} sentiment based on P/C ratio of {pc_volume_ratio:.2f}"
            }

            print(f"✓ Put/Call analysis completed")
            print(f"  P/C Volume Ratio: {pc_volume_ratio:.2f}")
            print(f"  Sentiment: {sentiment_strength} {sentiment}")

        except Exception as e:
            error_msg = f"Put/Call analysis failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
            self.put_call_analysis = {}

    def _generate_charts(self):
        """Generate analysis charts."""
        print("\n8. Generating Charts...")

        try:
            self.plots_generated = {}

            # Generate strike levels chart
            if self.option_chain and hasattr(self.option_analyzer, 'strike_levels') and self.option_analyzer.strike_levels:
                strike_path = os.path.join(self.output_dir, "option_strike_levels.png")
                self.plots_generated["strike_levels"] = self.chart_generator.plot_strike_levels(
                    self.option_chain, self.option_analyzer.strike_levels, strike_path
                )
                print(f"  ✓ Strike levels chart: {strike_path}")

                # Generate gamma wall chart
                gamma_path = os.path.join(self.output_dir, "gamma_wall_analysis.png")
                self.plots_generated["gamma_wall"] = self.chart_generator.plot_gamma_wall(
                    self.option_chain, self.option_analyzer.strike_levels, gamma_path
                )
                print(f"  ✓ Gamma wall chart: {gamma_path}")

            # Generate liquidity chart if we have money market data
            if hasattr(self.money_market_analyzer, 'liquidity_df') and not self.money_market_analyzer.liquidity_df.empty:
                liquidity_path = os.path.join(self.output_dir, "liquidity_indicators.png")
                self.plots_generated["liquidity"] = self.chart_generator.plot_liquidity_indicators(
                    self.money_market_analyzer.liquidity_df, liquidity_path
                )
                print(f"  ✓ Liquidity chart: {liquidity_path}")

            # Generate volatility charts
            if self.volatility_metrics:
                print("  Generating volatility charts...")

                # VRP trend chart
                vrp_path = os.path.join(self.output_dir, "volatility_vrp_trend.png")
                vrp_fig = self.volatility_analyzer.plot_vrp_trend(vrp_path)
                if vrp_fig:
                    self.plots_generated["vrp_trend"] = vrp_path
                    print(f"  ✓ VRP trend chart: {vrp_path}")

                # Volatility comparison chart
                vol_comp_path = os.path.join(self.output_dir, "volatility_comparison.png")
                vol_fig = self.volatility_analyzer.plot_volatility_comparison(vol_comp_path)
                if vol_fig:
                    self.plots_generated["volatility_comparison"] = vol_comp_path
                    print(f"  ✓ Volatility comparison chart: {vol_comp_path}")

            # Generate 2nd and 3rd order Greeks charts
            if self.option_chain:
                print("  Generating advanced Greeks charts...")

                # Vanna exposure chart
                vanna_path = os.path.join(self.output_dir, "vanna_exposure.png")
                vanna_chart = self._generate_vanna_chart(vanna_path)
                if vanna_chart:
                    self.plots_generated["vanna_exposure"] = vanna_path
                    print(f"  ✓ Vanna exposure chart: {vanna_path}")

                # Charm exposure chart
                charm_path = os.path.join(self.output_dir, "charm_exposure.png")
                charm_chart = self._generate_charm_chart(charm_path)
                if charm_chart:
                    self.plots_generated["charm_exposure"] = charm_path
                    print(f"  ✓ Charm exposure chart: {charm_path}")

                # Volga exposure chart
                volga_path = os.path.join(self.output_dir, "volga_exposure.png")
                volga_chart = self._generate_volga_chart(volga_path)
                if volga_chart:
                    self.plots_generated["volga_exposure"] = volga_path
                    print(f"  ✓ Volga exposure chart: {volga_path}")

            # Generate treasury auction chart if we have data
            if not self.treasury_auction_table.empty:
                treasury_path = os.path.join(self.output_dir, "treasury_auctions.png")
                treasury_chart = self.fiscal_client.plot_auction_analysis(self.treasury_auction_table, treasury_path)
                if treasury_chart:
                    self.plots_generated["treasury_auctions"] = treasury_path
                    print(f"  ✓ Treasury auction chart: {treasury_path}")

            # Generate put/call analysis chart
            if self.put_call_analysis:
                putcall_path = os.path.join(self.output_dir, "put_call_analysis.png")
                putcall_chart = self._generate_put_call_chart(putcall_path)
                if putcall_chart:
                    self.plots_generated["put_call"] = putcall_path
                    print(f"  ✓ Put/Call analysis chart: {putcall_path}")

            print(f"✓ Charts generated: {len([p for p in self.plots_generated.values() if p])} charts")

        except Exception as e:
            error_msg = f"Chart generation failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)

    def _generate_vanna_chart(self, save_path: str) -> bool:
        """Generate Vanna (dDelta/dVol) exposure chart."""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            fig, ax = plt.subplots(figsize=(12, 8))

            # Extract option data
            calls = self.option_chain.get('calls', [])
            puts = self.option_chain.get('puts', [])
            current_price = self.option_chain.get('current_price', 0)

            if not calls and not puts:
                ax.text(0.5, 0.5, "No option data available for Vanna analysis",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title("Vanna Exposure Analysis", fontsize=16, fontweight='bold')
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                plt.close(fig)
                return True

            # Calculate Vanna for each option with realistic values
            strikes = []
            vanna_values = []

            for option in calls + puts:
                strike = option.get('strike', 0)
                open_interest = option.get('open_interest', 0)
                volume = option.get('volume', 0)

                if strike > 0 and (open_interest > 0 or volume > 0):
                    # More realistic Vanna calculation
                    moneyness = strike / current_price if current_price > 0 else 1

                    # Vanna peaks around 0.95-1.05 moneyness (slightly OTM/ITM)
                    if 0.85 <= moneyness <= 1.15:
                        # Use a more realistic Vanna profile
                        distance_from_atm = abs(moneyness - 1.0)
                        vanna_multiplier = np.exp(-10 * distance_from_atm) * (1 - distance_from_atm)

                        # Scale by open interest and add some volume impact
                        base_exposure = open_interest * 100 + volume * 10  # Contract multiplier
                        vanna = vanna_multiplier * base_exposure * 0.001  # Scale to reasonable values

                        # Calls have positive vanna, puts have negative vanna for dealers
                        if option in puts:
                            vanna = -vanna

                        strikes.append(strike)
                        vanna_values.append(vanna)

            if strikes:
                ax.bar(strikes, vanna_values, alpha=0.7, color='purple', width=25)
                ax.axvline(x=current_price, color='red', linestyle='--', linewidth=2, label=f'Current Price: ${current_price:.0f}')
                ax.set_xlabel('Strike Price', fontsize=12)
                ax.set_ylabel('Vanna Exposure', fontsize=12)
                ax.set_title('Vanna (dDelta/dVol) Exposure by Strike', fontsize=16, fontweight='bold')
                ax.legend()
                ax.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            return True

        except Exception as e:
            print(f"Error generating Vanna chart: {e}")
            return False

    def _generate_charm_chart(self, save_path: str) -> bool:
        """Generate Charm (dDelta/dTime) exposure chart."""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            fig, ax = plt.subplots(figsize=(12, 8))

            calls = self.option_chain.get('calls', [])
            puts = self.option_chain.get('puts', [])
            current_price = self.option_chain.get('current_price', 0)

            if not calls and not puts:
                ax.text(0.5, 0.5, "No option data available for Charm analysis",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title("Charm Exposure Analysis", fontsize=16, fontweight='bold')
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                plt.close(fig)
                return True

            # Calculate Charm for each option with realistic values
            strikes = []
            charm_values = []

            for option in calls + puts:
                strike = option.get('strike', 0)
                open_interest = option.get('open_interest', 0)
                volume = option.get('volume', 0)

                if strike > 0 and (open_interest > 0 or volume > 0):
                    # More realistic Charm calculation
                    moneyness = strike / current_price if current_price > 0 else 1

                    # Charm is highest for ATM options and represents time decay of delta
                    if 0.80 <= moneyness <= 1.20:
                        # Charm profile - peaks at ATM
                        distance_from_atm = abs(moneyness - 1.0)
                        charm_multiplier = np.exp(-8 * distance_from_atm) * (1 - distance_from_atm * 0.5)

                        # Scale by open interest and volume
                        base_exposure = open_interest * 100 + volume * 10
                        charm = charm_multiplier * base_exposure * 0.0001  # Scale appropriately

                        # Charm is typically negative for long options (time decay)
                        if option in calls:
                            charm = -abs(charm)  # Negative for calls
                        else:
                            charm = abs(charm)   # Positive for puts (dealer perspective)

                        strikes.append(strike)
                        charm_values.append(charm)

            if strikes:
                ax.bar(strikes, charm_values, alpha=0.7, color='orange', width=25)
                ax.axvline(x=current_price, color='red', linestyle='--', linewidth=2, label=f'Current Price: ${current_price:.0f}')
                ax.set_xlabel('Strike Price', fontsize=12)
                ax.set_ylabel('Charm Exposure', fontsize=12)
                ax.set_title('Charm (dDelta/dTime) Exposure by Strike', fontsize=16, fontweight='bold')
                ax.legend()
                ax.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            return True

        except Exception as e:
            print(f"Error generating Charm chart: {e}")
            return False

    def _generate_volga_chart(self, save_path: str) -> bool:
        """Generate Volga (dVega/dVol) exposure chart."""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            fig, ax = plt.subplots(figsize=(12, 8))

            calls = self.option_chain.get('calls', [])
            puts = self.option_chain.get('puts', [])
            current_price = self.option_chain.get('current_price', 0)

            if not calls and not puts:
                ax.text(0.5, 0.5, "No option data available for Volga analysis",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title("Volga Exposure Analysis", fontsize=16, fontweight='bold')
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                plt.close(fig)
                return True

            # Calculate Volga for each option with realistic values
            strikes = []
            volga_values = []

            for option in calls + puts:
                strike = option.get('strike', 0)
                open_interest = option.get('open_interest', 0)
                volume = option.get('volume', 0)

                if strike > 0 and (open_interest > 0 or volume > 0):
                    # More realistic Volga calculation
                    moneyness = strike / current_price if current_price > 0 else 1

                    # Volga measures convexity of vega - highest for ATM options
                    if 0.75 <= moneyness <= 1.25:
                        # Volga profile - peaks at ATM, measures vega sensitivity to vol changes
                        distance_from_atm = abs(moneyness - 1.0)
                        volga_multiplier = np.exp(-6 * distance_from_atm) * (1 - distance_from_atm * 0.3)

                        # Scale by open interest and volume
                        base_exposure = open_interest * 100 + volume * 10
                        volga = volga_multiplier * base_exposure * 0.00001  # Scale appropriately

                        # Volga is typically positive for long options
                        if option in puts:
                            volga = -volga  # Negative for dealer short puts

                        strikes.append(strike)
                        volga_values.append(volga)

            if strikes:
                ax.bar(strikes, volga_values, alpha=0.7, color='green', width=25)
                ax.axvline(x=current_price, color='red', linestyle='--', linewidth=2, label=f'Current Price: ${current_price:.0f}')
                ax.set_xlabel('Strike Price', fontsize=12)
                ax.set_ylabel('Volga Exposure', fontsize=12)
                ax.set_title('Volga (dVega/dVol) Exposure by Strike', fontsize=16, fontweight='bold')
                ax.legend()
                ax.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            return True

        except Exception as e:
            print(f"Error generating Volga chart: {e}")
            return False

    def _generate_put_call_chart(self, save_path: str) -> bool:
        """Generate Put/Call ratio analysis chart."""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('Put/Call Ratio Analysis', fontsize=16, fontweight='bold')

            if not self.put_call_analysis:
                for ax in [ax1, ax2, ax3, ax4]:
                    ax.text(0.5, 0.5, "No Put/Call data available",
                           ha='center', va='center', transform=ax.transAxes, fontsize=12)
                plt.tight_layout()
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                plt.close(fig)
                return True

            # Chart 1: P/C Ratios Comparison
            ratios = ['Volume P/C', 'OI P/C', 'ATM P/C']
            values = [
                self.put_call_analysis.get('pc_volume_ratio', 0),
                self.put_call_analysis.get('pc_oi_ratio', 0),
                self.put_call_analysis.get('atm_pc_ratio', 0)
            ]
            colors = ['red' if v > 1 else 'green' for v in values]

            bars1 = ax1.bar(ratios, values, color=colors, alpha=0.7)
            ax1.axhline(y=1.0, color='black', linestyle='--', alpha=0.5, label='Neutral (1.0)')
            ax1.set_title('Put/Call Ratios')
            ax1.set_ylabel('Ratio')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # Add value labels on bars
            for bar, value in zip(bars1, values):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{value:.2f}', ha='center', va='bottom')

            # Chart 2: Volume Distribution
            call_vol = self.put_call_analysis.get('total_call_volume', 0)
            put_vol = self.put_call_analysis.get('total_put_volume', 0)

            if call_vol > 0 or put_vol > 0:
                ax2.pie([call_vol, put_vol], labels=['Calls', 'Puts'],
                       colors=['green', 'red'], autopct='%1.1f%%', startangle=90)
                ax2.set_title(f'Volume Distribution\nTotal: {call_vol + put_vol:,}')
            else:
                ax2.text(0.5, 0.5, "No volume data", ha='center', va='center', transform=ax2.transAxes)
                ax2.set_title('Volume Distribution')

            # Chart 3: Open Interest Distribution
            call_oi = self.put_call_analysis.get('total_call_oi', 0)
            put_oi = self.put_call_analysis.get('total_put_oi', 0)

            if call_oi > 0 or put_oi > 0:
                ax3.pie([call_oi, put_oi], labels=['Calls', 'Puts'],
                       colors=['green', 'red'], autopct='%1.1f%%', startangle=90)
                ax3.set_title(f'Open Interest Distribution\nTotal: {call_oi + put_oi:,}')
            else:
                ax3.text(0.5, 0.5, "No OI data", ha='center', va='center', transform=ax3.transAxes)
                ax3.set_title('Open Interest Distribution')

            # Chart 4: Sentiment Gauge
            sentiment = self.put_call_analysis.get('sentiment', 'NEUTRAL')
            pc_ratio = self.put_call_analysis.get('pc_volume_ratio', 1.0)

            # Create a gauge-like visualization
            theta = np.linspace(0, np.pi, 100)
            r = np.ones_like(theta)

            # Color zones
            ax4.fill_between(theta[0:33], 0, r[0:33], color='green', alpha=0.3, label='Bullish')
            ax4.fill_between(theta[33:66], 0, r[33:66], color='yellow', alpha=0.3, label='Neutral')
            ax4.fill_between(theta[66:100], 0, r[66:100], color='red', alpha=0.3, label='Bearish')

            # Add needle based on P/C ratio
            if pc_ratio <= 0.8:
                needle_angle = np.pi * 0.2  # Bullish
            elif pc_ratio >= 1.2:
                needle_angle = np.pi * 0.8  # Bearish
            else:
                needle_angle = np.pi * 0.5  # Neutral

            ax4.arrow(0, 0, 0.8 * np.cos(needle_angle), 0.8 * np.sin(needle_angle),
                     head_width=0.1, head_length=0.1, fc='black', ec='black')

            ax4.set_xlim(-1.2, 1.2)
            ax4.set_ylim(0, 1.2)
            ax4.set_title(f'Market Sentiment\n{sentiment} ({pc_ratio:.2f})')
            ax4.legend(loc='upper right')
            ax4.set_aspect('equal')
            ax4.axis('off')

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            return True

        except Exception as e:
            print(f"Error generating Put/Call chart: {e}")
            return False
    
    def _generate_narrative(self):
        """Generate ChatGPT narrative analysis."""
        print("\n9. Generating Narrative Analysis...")
        
        try:
            self.trading_narrative = self.narrative_generator.generate_narrative(
                self.option_signals_analysis,
                self.money_market_analysis
            )
            
            if self.trading_narrative:
                narrative_path = self.narrative_generator.save_narrative(
                    self.trading_narrative, 
                    self.output_dir
                )
                print(f"✓ Narrative generated and saved: {narrative_path}")
            else:
                print("⚠️ Narrative generation returned empty result")
                
        except Exception as e:
            error_msg = f"Narrative generation failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
    
    def _generate_reports(self) -> str:
        """Generate HTML and PDF reports."""
        print("\n10. Generating Reports...")
        
        try:
            # Generate HTML report with correct parameters
            html_path = self.html_generator.generate_report(
                self.money_market_analysis,
                self.option_signals_analysis,
                {},  # volatility_metrics
                None,  # volatility_table
                self.plots_generated,
                self.error_summary,
                self.options_flow_analysis,
                {}  # put_call_analysis
            )
            
            # Generate PDF report if HTML was successful
            if html_path:
                try:
                    pdf_path = self.pdf_generator.generate_report(
                        self.money_market_analysis,
                        self.option_signals_analysis,
                        self.volatility_metrics,  # Pass actual volatility metrics
                        self.volatility_table,    # Pass actual volatility table
                        self.plots_generated,
                        self.error_summary,
                        self.display_ticker,
                        self.executive_summary_table,  # Pass actual summary table
                        self.macro_analysis,           # Pass actual macro analysis
                        self.trading_narrative,
                        self.options_flow_analysis,
                        self.put_call_analysis,        # Pass actual put/call analysis
                        self.treasury_auction_analysis, # Pass actual treasury data
                        self.treasury_auction_table     # Pass actual treasury table
                    )
                    if pdf_path:
                        print(f"✓ PDF report generated: {pdf_path}")
                except Exception as pdf_error:
                    print(f"⚠️ PDF generation failed: {pdf_error}")
            
            print(f"✓ HTML report generated: {html_path}")
            return html_path
            
        except Exception as e:
            error_msg = f"Report generation failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.error_summary.append(error_msg)
            return ""


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Gamma/Vanna Market Analysis")
    parser.add_argument("--ticker", default="SPX", help="Base ticker symbol (default: SPX)")
    parser.add_argument("--price-ticker", help="Price ticker for Yahoo Finance (auto-detected if not specified)")
    parser.add_argument("--options-ticker", help="Options ticker for Polygon API (auto-detected if not specified)")
    parser.add_argument("--days", type=int, default=30, help="Days of analysis (default: 30)")
    parser.add_argument("--output-dir", default="output", help="Output directory (default: output)")
    parser.add_argument("--narrative", action="store_true", help="Enable ChatGPT narrative generation")
    parser.add_argument("--analysis-type", default="gamma_vanna", help="Type of analysis to run")
    
    args = parser.parse_args()
    
    # Create and run analysis
    driver = MarketAnalysisDriver(
        ticker=args.ticker,
        price_ticker=args.price_ticker,
        options_ticker=args.options_ticker,
        days_back=args.days,
        output_dir=args.output_dir,
        enable_narrative=args.narrative,
        analysis_type=args.analysis_type
    )
    
    results = driver.run_complete_analysis()
    
    if results["success"]:
        print(f"\n🎉 Analysis completed successfully!")
        print(f"📊 HTML Report: {results['html_report_path']}")
        # Check if PDF was also generated
        pdf_files = [f for f in os.listdir(args.output_dir) if f.startswith('market_analysis_report_') and f.endswith('.pdf')]
        if pdf_files:
            print(f"📄 PDF Report: {os.path.join(args.output_dir, pdf_files[-1])}")
    else:
        print(f"\n💥 Analysis failed: {results.get('error', 'Unknown error')}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 
