"""
Checklist Formatter
Generates the original checklist format with proper time-based sections and formatted output.
"""

import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional


class ChecklistFormatter:
    """Formats analysis results into the original checklist format."""
    
    def __init__(self):
        pass
    
    def generate_checklist_status(self, money_market_analysis: Dict[str, Any], 
                                option_signals_analysis: Dict[str, Any],
                                volatility_metrics: Dict[str, Any]) -> str:
        """Generate the formatted checklist status matching the original format."""
        
        # Extract key values
        mm = money_market_analysis
        opt = option_signals_analysis
        vol = volatility_metrics
        
        # Helper function to format status
        def format_status(status: str) -> str:
            return f"({status})" if status else "(COMPLETED)"
        
        # Helper function to safely get values
        def safe_get(data: dict, key: str, default: str = "N/A") -> str:
            value = data.get(key, default)
            return str(value) if value is not None else default
        
        # Extract specific values
        tga_impact = safe_get(mm, "tga_impact", "NEUTRAL")
        rrp_impact = safe_get(mm, "rrp_impact", "NEUTRAL") 
        auction_assessment = safe_get(mm, "treasury_auction_assessment", "AVERAGE DEMAND - NEUTRAL (SIMULATED)")
        
        nearest_call_wall = safe_get(opt, "nearest_call_wall", "None")
        nearest_put_wall = safe_get(opt, "nearest_put_wall", "None")
        approaching_call_wall = opt.get("approaching_call_wall", False)
        approaching_put_wall = opt.get("approaching_put_wall", False)
        
        mm_bias = safe_get(mm, "bias", "NEUTRAL")
        mm_confidence = safe_get(mm, "confidence", "LOW")
        mm_position_type = safe_get(mm, "position_type", "NON-DIRECTIONAL STRATEGIES")
        mm_expiry = safe_get(mm, "recommended_expiry", "AVOID DIRECTIONAL")
        
        opt_signal = safe_get(opt, "signal", "NEUTRAL")
        opt_confidence = safe_get(opt, "confidence", "LOW")
        opt_position_type = safe_get(opt, "position_type", "AVOID DIRECTIONAL")
        opt_expiry = safe_get(opt, "recommended_expiry", "N/A")
        
        liquidity_condition = safe_get(mm, "liquidity_condition", "TIGHTENING")
        liquidity_trend = safe_get(mm, "liquidity_trend", "RECENTLY IMPROVING")
        
        # Combined signal logic
        if mm_bias == "BEARISH" and "SHORT" in opt_signal:
            combined_signal = "BEARISH"
        elif mm_bias == "BULLISH" and "LONG" in opt_signal:
            combined_signal = "BULLISH"
        elif mm_bias == "NEUTRAL" or opt_signal == "NEUTRAL":
            combined_signal = "NEUTRAL"
        else:
            combined_signal = "NEUTRAL"
        
        # Combined action
        if combined_signal == "NEUTRAL":
            combined_action = "AVOID DIRECTIONAL, CONSIDER NON-DIRECTIONAL"
        else:
            combined_action = f"EXECUTE {combined_signal} STRATEGY"
        
        # Overall bias and position
        overall_bias = f"{mm_bias} ({mm_confidence})"
        recommended_position = opt_position_type
        recommended_expiry = opt_expiry
        position_size = safe_get(opt, "position_size", "0%")
        rationale = safe_get(opt, "rationale", "No clear signal")
        
        # VRP information
        current_iv = vol.get("current_iv", 0)
        current_vrp_20 = vol.get("current_vrp_20", 0)
        avg_iv_pct = f"{current_iv:.1%}" if current_iv else "N/A"
        vrp_20_pct = f"{current_vrp_20:+.1%}" if current_vrp_20 else "N/A"
        
        # Data source note
        option_data_source = safe_get(opt, "data_source", "Real Data")
        
        # Format the checklist
        checklist = f"""Preparation (7:00-9:30 AM ET)
> Tga Balance Changes {format_status("COMPLETED")}
Result: TGA Impact: {tga_impact}
Action: Implied Bias: {self._get_bias_from_impact(tga_impact)}

> Rrp Usage {format_status("COMPLETED")}
Result: RRP Impact: {rrp_impact}
Action: Action: {self._get_rrp_action(rrp_impact)}

> Treasury Auction Schedule {format_status("COMPLETED")}
Result: Auction Assessment: {auction_assessment}
Action: Implied Bias: {self._get_bias_from_assessment(auction_assessment)}

Market Open Analysis (9:30-10:30 AM ET)
> Option Strike Levels {format_status("COMPLETED")}
Result: Nearest Call Wall: {nearest_call_wall}, Nearest Put Wall: {nearest_put_wall}
Action: Monitor Resistance: {approaching_call_wall}, Monitor Support: {approaching_put_wall}

Mid-Day Analysis (11:00 AM-1:00 PM ET)
> Money Market Overlay {format_status("COMPLETED")}
Result: MM Bias: {mm_bias} ({mm_confidence}){mm_bias}
Action: MM Action: USE {mm_position_type} WITH {mm_expiry} EXPIRY

> Option Signals {format_status("COMPLETED")}
Result: Option Signal: {opt_signal} ({opt_confidence})
Action: Option Action: USE {opt_position_type} WITH {opt_expiry} EXPIRY

> Combined Signal {format_status("COMPLETED")}
Result: Combined Signal: {combined_signal}
Action: Combined Action: {combined_action}

End-of-Day Analysis (3:00-4:00 PM ET)
> Liquidity Conditions {format_status("COMPLETED")}
Result: Condition: {liquidity_condition}, Trend: {liquidity_trend}
Action: Action: PREPARE FOR NEXT DAY

Trading Summary
Overall Bias: {overall_bias}
Recommended Position: {recommended_position}
Recommended Expiry: {recommended_expiry}
Position Size: {position_size}
Rationale: {rationale}

VRP Analysis
Current IV: {avg_iv_pct}
20-Day VRP: {vrp_20_pct}

Note: Option data source: {option_data_source}. Treasury auction results are currently simulated."""
        
        return checklist
    
    def _get_bias_from_impact(self, impact: str) -> str:
        """Get bias from TGA impact."""
        if "DRAIN" in impact.upper():
            return "BEARISH"
        elif "ADDITION" in impact.upper():
            return "BULLISH"
        else:
            return "NEUTRAL"
    
    def _get_rrp_action(self, impact: str) -> str:
        """Get action from RRP impact."""
        if "TIGHTENING" in impact.upper() or "DRAIN" in impact.upper():
            return "MONITOR VOLATILITY"
        else:
            return "NO ACTION"
    
    def _get_bias_from_assessment(self, assessment: str) -> str:
        """Get bias from auction assessment."""
        if "BEARISH" in assessment.upper():
            return "BEARISH"
        elif "BULLISH" in assessment.upper():
            return "BULLISH"
        else:
            return "NEUTRAL"
    
    def generate_formatted_table(self, money_market_analysis: Dict[str, Any], 
                                option_signals_analysis: Dict[str, Any],
                                volatility_metrics: Dict[str, Any]) -> pd.DataFrame:
        """Generate a formatted table version of the checklist."""
        
        checklist_text = self.generate_checklist_status(
            money_market_analysis, option_signals_analysis, volatility_metrics
        )
        
        # Parse the checklist into a structured format
        lines = checklist_text.split('\n')
        data = []
        current_section = ""
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if line.endswith("ET)") or line == "Trading Summary":
                current_section = line
            elif line.startswith(">"):
                # Parse checklist item
                parts = line.split("(")
                if len(parts) >= 2:
                    item_name = parts[0].replace(">", "").strip()
                    status = parts[1].replace(")", "").strip()
                    data.append({
                        "Section": current_section,
                        "Item": item_name,
                        "Status": status,
                        "Result": "",
                        "Action": ""
                    })
            elif line.startswith("Result:"):
                if data:
                    data[-1]["Result"] = line.replace("Result:", "").strip()
            elif line.startswith("Action:"):
                if data:
                    data[-1]["Action"] = line.replace("Action:", "").strip()
            elif ":" in line and current_section == "Trading Summary":
                # Handle trading summary items
                key, value = line.split(":", 1)
                data.append({
                    "Section": "Trading Summary",
                    "Item": key.strip(),
                    "Status": "SUMMARY",
                    "Result": value.strip(),
                    "Action": ""
                })
        
        return pd.DataFrame(data) 