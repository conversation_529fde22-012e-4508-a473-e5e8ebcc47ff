"""
PDF Report Generator
Generates comprehensive PDF reports with VRP analysis, volatility metrics, and market analysis.
"""

import os
import io
import base64
import re
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages
import numpy as np

try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.pdfgen import canvas
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("Warning: reportlab not installed. PDF generation will be limited.")


class PDFReportGenerator:
    """Generates comprehensive PDF reports with VRP analysis and market data."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        if not REPORTLAB_AVAILABLE:
            print("Warning: reportlab not available. Install with: pip install reportlab")
        
        # Initialize styles
        self.styles = getSampleStyleSheet() if REPORTLAB_AVAILABLE else None
        if self.styles:
            self._setup_custom_styles()
        
    def _setup_custom_styles(self):
        """Setup custom paragraph styles for the PDF."""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=colors.darkblue,
            borderWidth=1,
            borderColor=colors.darkblue,
            borderPadding=5
        ))
        
        # Subsection style
        self.styles.add(ParagraphStyle(
            name='SubSection',
            parent=self.styles['Heading3'],
            fontSize=14,
            spaceAfter=8,
            spaceBefore=12,
            textColor=colors.darkgreen
        ))
        
        # Key metrics style
        self.styles.add(ParagraphStyle(
            name='KeyMetric',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            leftIndent=20,
            bulletIndent=10
        ))
        
        # Signal style
        self.styles.add(ParagraphStyle(
            name='Signal',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=10,
            alignment=TA_CENTER,
            backColor=colors.lightgrey,
            borderWidth=1,
            borderColor=colors.black,
            borderPadding=8
        ))

    def generate_report(self, money_market_analysis: Dict[str, Any], 
                       option_signals_analysis: Dict[str, Any],
                       volatility_metrics: Optional[Dict[str, Any]] = None,
                       volatility_table: Optional[pd.DataFrame] = None,
                       plots_generated: Optional[Dict[str, str]] = None,
                       error_summary: Optional[List[str]] = None,
                       ticker: str = "SPX",
                       executive_summary_table: Optional[pd.DataFrame] = None,
                       macro_analysis: Optional[str] = None,
                       trading_narrative: Optional[str] = None,
                       options_flow_analysis: Optional[Dict[str, Any]] = None,
                       put_call_analysis: Optional[Dict[str, Any]] = None,
                       treasury_auction_analysis: Optional[Dict[str, Any]] = None,
                       treasury_auction_table: Optional[pd.DataFrame] = None) -> str:
        """
        Generate comprehensive PDF report with VRP analysis.
        
        Args:
            money_market_analysis: Money market analysis results
            option_signals_analysis: Option signals analysis results
            volatility_metrics: Volatility analysis metrics
            volatility_table: Volatility data table
            plots_generated: Dictionary of generated plot paths
            error_summary: List of errors encountered
            ticker: Ticker symbol
            
        Returns:
            Path to generated PDF report
        """
        if not REPORTLAB_AVAILABLE:
            print("❌ Cannot generate PDF: reportlab not installed")
            return ""
        
        try:
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"market_analysis_report_{ticker}_{timestamp}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(filepath, pagesize=letter,
                                  rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            
            # Build story (content)
            story = []
            
            # Title page
            story.extend(self._create_title_page(ticker))
            
            # Executive summary
            story.extend(self._create_executive_summary(
                money_market_analysis, option_signals_analysis, volatility_metrics, executive_summary_table
            ))
            
            # VRP Analysis section
            if volatility_metrics:
                story.extend(self._create_vrp_analysis_section(volatility_metrics, volatility_table))
            
            # Money market analysis
            story.extend(self._create_money_market_section(money_market_analysis))
            
            # Option signals analysis
            story.extend(self._create_option_signals_section(option_signals_analysis))
            
            # Advanced options flow analysis
            if options_flow_analysis and 'error' not in options_flow_analysis:
                story.extend(self._create_options_flow_section(options_flow_analysis))
            
            # Put/Call ratio analysis
            if put_call_analysis and 'error' not in put_call_analysis:
                story.extend(self._create_put_call_section(put_call_analysis))
            
            # Treasury auction analysis
            if treasury_auction_analysis and 'error' not in treasury_auction_analysis:
                story.extend(self._create_treasury_auction_section(treasury_auction_analysis, treasury_auction_table))
            
            # Volatility analysis details
            if volatility_metrics:
                story.extend(self._create_volatility_details_section(volatility_metrics))
            
            # Charts section
            if plots_generated:
                story.extend(self._create_charts_section(plots_generated))
            
            # Macro analysis section
            if macro_analysis:
                story.extend(self._create_macro_analysis_section(macro_analysis))
            
            # Trading narrative section
            if trading_narrative:
                story.extend(self._create_trading_narrative_section(trading_narrative))
            
            # Error summary
            if error_summary:
                story.extend(self._create_error_summary_section(error_summary))
            
            # Build PDF
            doc.build(story)
            
            print(f"✓ PDF report generated: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ PDF generation failed: {str(e)}")
            return ""

    def _create_title_page(self, ticker: str) -> List:
        """Create title page content."""
        story = []
        
        # Title
        title = f"Market Analysis Report - {ticker}"
        story.append(Paragraph(title, self.styles['CustomTitle']))
        story.append(Spacer(1, 0.5*inch))
        
        # Subtitle
        subtitle = "Comprehensive VRP, Volatility & Options Analysis"
        story.append(Paragraph(subtitle, self.styles['Heading2']))
        story.append(Spacer(1, 0.3*inch))
        
        # Date and time
        current_time = datetime.now().strftime("%B %d, %Y at %I:%M %p")
        story.append(Paragraph(f"Generated: {current_time}", self.styles['Normal']))
        story.append(Spacer(1, 1*inch))
        
        # Key features
        features = [
            "• Comprehensive Volatility Risk Premium (VRP) Analysis",
            "• Historical vs Implied Volatility Comparison with Regime Analysis",
            "• Fed Net Liquidity Assessment & Market Impact Analysis",
            "• Option Strike Level Analysis with Gamma/Vanna Positioning",
            "• Advanced Trading Signal Generation with Confidence Levels",
            "• Comprehensive Risk Management & Position Sizing Recommendations",
            "• VRP Percentile Analysis & Historical Context",
            "• Term Structure Analysis & Mean Reversion Signals",
            "• Volatility Regime Identification & Trading Strategies"
        ]
        
        story.append(Paragraph("Report Features:", self.styles['Heading3']))
        for feature in features:
            story.append(Paragraph(feature, self.styles['Normal']))
        
        story.append(PageBreak())
        return story

    def _create_executive_summary(self, money_market_analysis: Dict[str, Any],
                                option_signals_analysis: Dict[str, Any],
                                volatility_metrics: Optional[Dict[str, Any]],
                                executive_summary_table: Optional[pd.DataFrame] = None) -> List:
        """Create executive summary section with comprehensive table."""
        story = []
        
        story.append(Paragraph("Executive Summary", self.styles['SectionHeader']))
        
        # Add executive summary table if provided
        if executive_summary_table is not None and not executive_summary_table.empty:
            # Convert DataFrame to table data
            table_data = [executive_summary_table.columns.tolist()]
            for _, row in executive_summary_table.iterrows():
                table_data.append([str(val) for val in row])
            
            # Create table with appropriate column widths - wider Value column for wrapping
            exec_table = Table(table_data, colWidths=[2.2*inch, 3.3*inch, 2*inch])
            exec_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),  # Changed to TOP for better text wrapping
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgrey, colors.white]),
                ('WORDWRAP', (0, 0), (-1, -1), True),  # Enable word wrapping
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8)
            ]))
            
            story.append(exec_table)
            story.append(Spacer(1, 0.3*inch))
        else:
            # Fallback to original format if no table provided
            market_bias = money_market_analysis.get('bias', 'UNKNOWN')
            confidence = money_market_analysis.get('confidence', 'UNKNOWN')
            
            bias_text = f"<b>Market Bias:</b> {market_bias} (Confidence: {confidence})"
            story.append(Paragraph(bias_text, self.styles['Signal']))
            story.append(Spacer(1, 0.2*inch))
            
            # VRP Summary
            if volatility_metrics:
                current_vrp = volatility_metrics.get('current_vrp_20', 0)
                current_iv = volatility_metrics.get('current_iv', 0)
                current_hv = volatility_metrics.get('current_hv_20', 0)
                
                vrp_summary = f"""
                <b>VRP Analysis Summary:</b><br/>
                • Current 20-day VRP: {current_vrp:.1%}<br/>
                • Current Implied Volatility: {current_iv:.1%}<br/>
                • Current 20-day Historical Volatility: {current_hv:.1%}<br/>
                • VRP Signal: {'Elevated IV' if current_vrp > 0.05 else 'Compressed IV' if current_vrp < -0.05 else 'Neutral'}
                """
                story.append(Paragraph(vrp_summary, self.styles['Normal']))
            
            # Option signals summary
            option_signal = option_signals_analysis.get('signal', 'UNKNOWN')
            option_confidence = option_signals_analysis.get('confidence', 'UNKNOWN')
            
            option_summary = f"""
            <b>Option Signals:</b><br/>
            • Primary Signal: {option_signal}<br/>
            • Signal Confidence: {option_confidence}<br/>
            • Data Source: {option_signals_analysis.get('data_source', 'Unknown')}
            """
            story.append(Paragraph(option_summary, self.styles['Normal']))
            
            # Trading recommendations
            mm_recommended_expiry = money_market_analysis.get('recommended_expiry', 'N/A')
            mm_position_type = money_market_analysis.get('position_type', 'N/A')
            
            option_position_type = option_signals_analysis.get('position_type', 'N/A')
            option_recommended_expiry = option_signals_analysis.get('recommended_expiry', 'N/A')
            option_position_size = option_signals_analysis.get('position_size', 'N/A')
            
            recommendations = f"""
            <b>Trading Recommendations:</b><br/>
            • Money Market Signal: {mm_position_type} ({mm_recommended_expiry})<br/>
            • Option Signal: {option_position_type} ({option_recommended_expiry})<br/>
            • Suggested Position Size: {option_position_size}<br/>
            • Risk Management: Monitor liquidity conditions and VRP changes
            """
            story.append(Paragraph(recommendations, self.styles['Normal']))
        
        story.append(PageBreak())
        return story

    def _create_vrp_analysis_section(self, volatility_metrics: Dict[str, Any],
                                   volatility_table: Optional[pd.DataFrame]) -> List:
        """Create comprehensive VRP analysis section."""
        story = []

        story.append(Paragraph("Comprehensive Volatility Risk Premium (VRP) Analysis", self.styles['SectionHeader']))

        # Enhanced VRP explanation
        vrp_explanation = """
        The Volatility Risk Premium (VRP) represents the difference between implied volatility (IV)
        and realized historical volatility (HV). This premium reflects the market's willingness to pay
        for volatility protection and provides critical insights for options trading strategies.
        <br/><br/>
        <b>Key Concepts:</b><br/>
        • <b>Positive VRP:</b> Options are expensive relative to realized volatility (volatility selling opportunity)<br/>
        • <b>Negative VRP:</b> Options are cheap relative to realized volatility (volatility buying opportunity)<br/>
        • <b>VRP Percentile:</b> Current VRP level relative to historical distribution<br/>
        • <b>Mean Reversion:</b> VRP tends to revert to long-term average over time
        """
        story.append(Paragraph(vrp_explanation, self.styles['Normal']))
        story.append(Spacer(1, 0.3*inch))
        
        # Current VRP metrics
        story.append(Paragraph("Current VRP Metrics", self.styles['SubSection']))
        
        vrp_data = [
            ['Metric', 'Value', 'Interpretation'],
            ['Current IV', f"{volatility_metrics.get('current_iv', 0):.1%}", 'Market expectation of future volatility'],
            ['20-day HV', f"{volatility_metrics.get('current_hv_20', 0):.1%}", 'Recent realized volatility'],
            ['20-day VRP', f"{volatility_metrics.get('current_vrp_20', 0):.1%}", 'IV premium over realized volatility'],
            ['30-day HV', f"{volatility_metrics.get('current_hv_30', 0):.1%}", 'Medium-term realized volatility'],
            ['30-day VRP', f"{volatility_metrics.get('current_vrp_30', 0):.1%}", 'IV premium over 30-day HV'],
        ]
        
        vrp_table = Table(vrp_data, colWidths=[2*inch, 1.5*inch, 3*inch])
        vrp_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(vrp_table)
        story.append(Spacer(1, 0.3*inch))
        
        # Enhanced VRP interpretation with regime analysis
        current_vrp_20 = volatility_metrics.get('current_vrp_20', 0)
        vrp_percentile = volatility_metrics.get('vrp_percentile', 50)

        # Determine VRP regime
        if current_vrp_20 > 0.10:
            regime = "EXTREME HIGH VRP"
            regime_color = "red"
            interpretation = "Extremely high VRP indicates significant options overpricing. Strong volatility selling opportunity with high probability of mean reversion."
        elif current_vrp_20 > 0.05:
            regime = "HIGH VRP"
            regime_color = "orange"
            interpretation = "High VRP suggests options are expensive relative to realized volatility. Consider volatility selling strategies with appropriate risk management."
        elif current_vrp_20 < -0.10:
            regime = "EXTREME LOW VRP"
            regime_color = "darkgreen"
            interpretation = "Extremely negative VRP indicates severe options underpricing. Strong volatility buying opportunity, but monitor for potential volatility expansion."
        elif current_vrp_20 < -0.05:
            regime = "LOW VRP"
            regime_color = "green"
            interpretation = "Negative VRP suggests options are cheap relative to realized volatility. Consider volatility buying strategies."
        else:
            regime = "NEUTRAL VRP"
            regime_color = "gray"
            interpretation = "VRP is near neutral, suggesting fair option pricing relative to recent volatility. Monitor for regime changes."

        # VRP regime summary
        regime_summary = f"""
        <b>Current VRP Regime:</b> <font color="{regime_color}">{regime}</font><br/>
        <b>VRP Percentile:</b> {vrp_percentile:.0f}th percentile<br/>
        <b>Interpretation:</b> {interpretation}
        """
        story.append(Paragraph(regime_summary, self.styles['Normal']))
        
        # Volatility table if available
        if volatility_table is not None and not volatility_table.empty:
            story.append(Spacer(1, 0.2*inch))
            story.append(Paragraph("Detailed Volatility Metrics", self.styles['SubSection']))
            
            # Convert DataFrame to table data
            table_data = [volatility_table.columns.tolist()]
            for _, row in volatility_table.iterrows():
                table_data.append([f"{val:.1%}" if isinstance(val, (int, float)) else str(val) for val in row])
            
            # Calculate column widths based on number of columns to fit page width
            num_cols = len(volatility_table.columns)
            col_width = 7.5*inch / num_cols  # Distribute 7.5 inches across all columns
            
            vol_table = Table(table_data, colWidths=[col_width] * num_cols)
            vol_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkgrey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 11),  # Larger header font
                ('FONTSIZE', (0, 1), (-1, -1), 9),  # Smaller data font
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgrey, colors.white])  # Alternating rows
            ]))
            
            story.append(vol_table)
        
        # Add VRP-based risk management recommendations
        story.append(Spacer(1, 0.3*inch))
        story.extend(self._create_vrp_risk_management_section(volatility_metrics))

        story.append(PageBreak())
        return story

    def _create_vrp_risk_management_section(self, volatility_metrics: Dict[str, Any]) -> List:
        """Create VRP-based risk management recommendations section."""
        story = []

        story.append(Paragraph("VRP-Based Risk Management Recommendations", self.styles['SubSection']))

        current_vrp_20 = volatility_metrics.get('current_vrp_20', 0)
        current_iv = volatility_metrics.get('current_iv', 0)
        vrp_percentile = volatility_metrics.get('vrp_percentile', 50)

        # Position sizing recommendations
        if current_vrp_20 > 0.10:
            position_size = "Large (3-5% of portfolio)"
            risk_level = "Low-Medium"
            strategy_focus = "Aggressive volatility selling"
        elif current_vrp_20 > 0.05:
            position_size = "Medium (2-3% of portfolio)"
            risk_level = "Medium"
            strategy_focus = "Moderate volatility selling"
        elif current_vrp_20 < -0.10:
            position_size = "Large (3-5% of portfolio)"
            risk_level = "Medium-High"
            strategy_focus = "Aggressive volatility buying"
        elif current_vrp_20 < -0.05:
            position_size = "Medium (2-3% of portfolio)"
            risk_level = "Medium"
            strategy_focus = "Moderate volatility buying"
        else:
            position_size = "Small (1-2% of portfolio)"
            risk_level = "Low"
            strategy_focus = "Neutral/hedging strategies"

        # Risk management table
        risk_mgmt_data = [
            ['Risk Factor', 'Recommendation', 'Rationale'],
            ['Position Size', position_size, f'Based on {vrp_percentile:.0f}th percentile VRP'],
            ['Risk Level', risk_level, f'Current IV: {current_iv:.1%}, VRP: {current_vrp_20:+.1%}'],
            ['Strategy Focus', strategy_focus, 'Aligned with VRP regime'],
            ['Stop Loss', f'{current_iv * 1.5:.1%}' if current_vrp_20 > 0 else f'{current_iv * 0.7:.1%}', 'Volatility-based stop loss'],
            ['Profit Target', f'{abs(current_vrp_20) * 0.6:.1%}', '60% of current VRP as target'],
            ['Time Decay', '30-45 days to expiration', 'Optimal theta decay period'],
            ['Delta Hedging', 'Daily for large positions', 'Maintain delta neutrality'],
        ]

        risk_table = Table(risk_mgmt_data, colWidths=[2*inch, 2.5*inch, 2.5*inch])
        risk_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkred),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightpink),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightpink, colors.white]),
            ('WORDWRAP', (0, 0), (-1, -1), True),
        ]))

        story.append(risk_table)
        story.append(Spacer(1, 0.2*inch))

        # Scenario analysis
        story.append(Paragraph("VRP Scenario Analysis", self.styles['SubSection']))

        scenario_text = f"""
        <b>Bull Market Scenario:</b> VRP typically compresses as realized volatility decreases.
        Monitor for volatility selling opportunities when VRP > 5%.<br/><br/>
        <b>Bear Market Scenario:</b> VRP can turn negative as realized volatility spikes.
        Prepare for volatility buying opportunities when VRP < -5%.<br/><br/>
        <b>Sideways Market:</b> VRP tends to be more stable. Focus on theta decay strategies
        and maintain delta neutrality.<br/><br/>
        <b>Current Market Assessment:</b> Based on {current_vrp_20:+.1%} VRP, the market is currently
        in a {('high volatility premium' if current_vrp_20 > 0.05 else 'low volatility premium' if current_vrp_20 < -0.05 else 'neutral volatility')} regime.
        """
        story.append(Paragraph(scenario_text, self.styles['Normal']))

        return story

    def _create_money_market_section(self, money_market_analysis: Dict[str, Any]) -> List:
        """Create money market analysis section."""
        story = []
        
        story.append(Paragraph("Fed Net Liquidity Assessment & Market Impact Analysis", self.styles['SectionHeader']))

        # Enhanced Fed Net Liquidity explanation
        liquidity_explanation = """
        Fed Net Liquidity measures the amount of liquidity the Federal Reserve is providing to
        financial markets. This critical metric is calculated as Fed Assets minus Treasury General
        Account (TGA) balance minus Reverse Repo operations, adjusted for Treasury yield levels.
        <br/><br/>
        <b>Key Components:</b><br/>
        • <b>Fed Assets:</b> Total Federal Reserve balance sheet assets<br/>
        • <b>TGA Balance:</b> Treasury's cash account at the Fed (liquidity drain when high)<br/>
        • <b>Reverse Repo:</b> Overnight reverse repurchase agreements (liquidity absorption)<br/>
        • <b>Yield Adjustment:</b> Accounts for interest rate environment impact<br/><br/>
        <b>Market Impact:</b> Higher liquidity typically supports risk assets and can compress volatility,
        while liquidity drains often coincide with increased market stress and higher VRP.
        """
        story.append(Paragraph(liquidity_explanation, self.styles['Normal']))
        story.append(Spacer(1, 0.3*inch))
        
        # Current conditions
        story.append(Paragraph("Current Liquidity Conditions", self.styles['SubSection']))
        
        liquidity_data = [
            ['Metric', 'Value'],
            ['Market Bias', money_market_analysis.get('bias', 'UNKNOWN')],
            ['Confidence Level', money_market_analysis.get('confidence', 'UNKNOWN')],
            ['Liquidity Condition', money_market_analysis.get('liquidity_condition', 'UNKNOWN')],
            ['Liquidity Trend', money_market_analysis.get('liquidity_trend', 'UNKNOWN')],
            ['TGA Impact', money_market_analysis.get('tga_impact', 'UNKNOWN')],
            ['RRP Impact', money_market_analysis.get('rrp_impact', 'UNKNOWN')],
        ]
        
        liquidity_table = Table(liquidity_data, colWidths=[3.5*inch, 3.5*inch])
        liquidity_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),  # Header font
            ('FONTSIZE', (0, 1), (-1, -1), 10),  # Data font
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgrey, colors.white])  # Alternating rows
        ]))
        
        story.append(liquidity_table)
        story.append(Spacer(1, 0.2*inch))
        
        # Trading implications
        bias = money_market_analysis.get('bias', 'UNKNOWN')
        if bias == 'BULLISH':
            implications = "Improving liquidity conditions suggest support for risk assets. Consider bullish positioning."
        elif bias == 'BEARISH':
            implications = "Deteriorating liquidity conditions suggest headwinds for risk assets. Consider defensive positioning."
        else:
            implications = "Neutral liquidity conditions suggest mixed signals. Monitor for changes in trend."
        
        story.append(Paragraph(f"<b>Trading Implications:</b> {implications}", self.styles['Normal']))

        # Add liquidity-VRP correlation analysis
        story.append(Spacer(1, 0.3*inch))
        story.append(Paragraph("Liquidity-VRP Correlation Analysis", self.styles['SubSection']))

        liquidity_vrp_analysis = """
        <b>Historical Relationship:</b> Fed Net Liquidity and VRP typically exhibit an inverse correlation.
        When liquidity is abundant, VRP tends to be compressed as market stress is low. Conversely,
        liquidity drains often coincide with elevated VRP as market participants demand higher premiums
        for volatility protection.<br/><br/>
        <b>Current Assessment:</b> Monitor the relationship between liquidity conditions and VRP levels
        for potential regime changes. Significant divergences may signal upcoming market stress or
        policy shifts.<br/><br/>
        <b>Trading Strategy Integration:</b><br/>
        • <b>High Liquidity + High VRP:</b> Potential volatility selling opportunity<br/>
        • <b>Low Liquidity + Low VRP:</b> Potential volatility buying opportunity<br/>
        • <b>Divergent Signals:</b> Exercise caution and reduce position sizes
        """
        story.append(Paragraph(liquidity_vrp_analysis, self.styles['Normal']))

        story.append(PageBreak())
        return story

    def _create_option_signals_section(self, option_signals_analysis: Dict[str, Any]) -> List:
        """Create option signals analysis section."""
        story = []
        
        story.append(Paragraph("Advanced Option Strike Level Analysis", self.styles['SectionHeader']))
        
        # Option analysis summary
        signal = option_signals_analysis.get('signal', 'UNKNOWN')
        confidence = option_signals_analysis.get('confidence', 'UNKNOWN')
        data_source = option_signals_analysis.get('data_source', 'Unknown')
        position_type = option_signals_analysis.get('position_type', 'N/A')
        recommended_expiry = option_signals_analysis.get('recommended_expiry', 'N/A')
        position_size = option_signals_analysis.get('position_size', 'N/A')
        
        signals_data = [
            ['Metric', 'Value'],
            ['Primary Signal', signal],
            ['Signal Confidence', confidence],
            ['Position Type', position_type],
            ['Recommended Expiry', recommended_expiry],
            ['Position Size', position_size],
            ['Data Source', data_source],
            ['Analysis Date', datetime.now().strftime("%Y-%m-%d")],
        ]
        
        if 'rationale' in option_signals_analysis:
            signals_data.append(['Rationale', option_signals_analysis['rationale']])
        
        signals_table = Table(signals_data, colWidths=[2.5*inch, 4*inch])
        signals_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),  # Header font
            ('FONTSIZE', (0, 1), (-1, -1), 10),  # Data font
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgrey, colors.white])  # Alternating rows
        ]))
        
        story.append(signals_table)
        story.append(Spacer(1, 0.2*inch))
        
        # Signal interpretation
        if signal == 'NO_DATA':
            interpretation = "No option data available for analysis. Signals based on other market factors only."
        elif confidence == 'HIGH':
            interpretation = f"High confidence {signal.lower()} signal suggests strong directional bias."
        elif confidence == 'MEDIUM':
            interpretation = f"Medium confidence {signal.lower()} signal suggests moderate directional bias."
        else:
            interpretation = "Low confidence signal suggests mixed or unclear market conditions."
        
        story.append(Paragraph(f"<b>Signal Interpretation:</b> {interpretation}", self.styles['Normal']))

        # Add comprehensive trading signal analysis
        story.append(Spacer(1, 0.3*inch))
        story.extend(self._create_trading_signal_analysis(option_signals_analysis))

        story.append(PageBreak())
        return story

    def _create_trading_signal_analysis(self, option_signals_analysis: Dict[str, Any]) -> List:
        """Create comprehensive trading signal generation analysis."""
        story = []

        story.append(Paragraph("Advanced Trading Signal Generation", self.styles['SubSection']))

        # Signal confidence analysis
        signal = option_signals_analysis.get('signal', 'UNKNOWN')
        confidence = option_signals_analysis.get('confidence', 'UNKNOWN')

        # Multi-factor signal analysis
        signal_factors = [
            ['Signal Component', 'Value', 'Weight', 'Contribution'],
            ['Gamma Wall Position', option_signals_analysis.get('gamma_wall_signal', 'N/A'), '25%', 'Structural support/resistance'],
            ['Vanna Flow', option_signals_analysis.get('vanna_flow', 'N/A'), '20%', 'Volatility-spot correlation'],
            ['Put/Call Skew', option_signals_analysis.get('skew_signal', 'N/A'), '15%', 'Market sentiment indicator'],
            ['Volume Profile', option_signals_analysis.get('volume_signal', 'N/A'), '15%', 'Liquidity concentration'],
            ['Greeks Imbalance', option_signals_analysis.get('greeks_signal', 'N/A'), '15%', 'Dealer positioning'],
            ['Term Structure', option_signals_analysis.get('term_structure', 'N/A'), '10%', 'Time decay dynamics'],
        ]

        signal_table = Table(signal_factors, colWidths=[2*inch, 1.5*inch, 1*inch, 2.5*inch])
        signal_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightblue, colors.white]),
        ]))

        story.append(signal_table)
        story.append(Spacer(1, 0.2*inch))

        # Signal strength interpretation
        if confidence == 'HIGH':
            strength_text = "High confidence signals indicate strong alignment across multiple factors. Consider larger position sizes with appropriate risk management."
        elif confidence == 'MEDIUM':
            strength_text = "Medium confidence signals suggest moderate alignment. Use standard position sizing with enhanced monitoring."
        else:
            strength_text = "Low confidence signals indicate conflicting factors. Consider smaller positions or wait for clearer signals."

        story.append(Paragraph(f"<b>Signal Strength Assessment:</b> {strength_text}", self.styles['Normal']))

        # Risk-adjusted recommendations
        story.append(Spacer(1, 0.2*inch))
        story.append(Paragraph("Risk-Adjusted Trading Recommendations", self.styles['SubSection']))

        risk_recommendations = f"""
        <b>Primary Strategy:</b> {signal} bias with {confidence.lower()} confidence level<br/>
        <b>Entry Timing:</b> Monitor for confirmation signals and favorable VRP levels<br/>
        <b>Position Management:</b> Use systematic profit-taking and stop-loss levels<br/>
        <b>Hedging Considerations:</b> Maintain appropriate delta and vega hedges<br/>
        <b>Market Regime Awareness:</b> Adjust strategy based on volatility regime changes
        """
        story.append(Paragraph(risk_recommendations, self.styles['Normal']))

        return story

    def _create_volatility_details_section(self, volatility_metrics: Dict[str, Any]) -> List:
        """Create detailed volatility analysis section."""
        story = []
        
        story.append(Paragraph("Detailed Volatility Analysis", self.styles['SectionHeader']))
        
        # Volatility components
        story.append(Paragraph("Volatility Components", self.styles['SubSection']))
        
        vol_components = """
        <b>Historical Volatility (HV):</b> Measures actual price movement over a specific period.<br/>
        <b>Implied Volatility (IV):</b> Market's expectation of future volatility derived from option prices.<br/>
        <b>Volatility Risk Premium (VRP):</b> The difference between IV and HV, representing the premium paid for volatility protection.
        """
        story.append(Paragraph(vol_components, self.styles['Normal']))
        story.append(Spacer(1, 0.2*inch))
        
        # Multiple timeframe analysis
        story.append(Paragraph("Multi-Timeframe Volatility Analysis", self.styles['SubSection']))
        
        timeframes = ['10', '20', '30', '60']
        vol_analysis_data = [['Timeframe', 'Historical Vol', 'VRP', 'Interpretation']]
        
        for tf in timeframes:
            hv_key = f'current_hv_{tf}'
            vrp_key = f'current_vrp_{tf}'
            
            hv = volatility_metrics.get(hv_key, 0)
            vrp = volatility_metrics.get(vrp_key, 0)
            
            if vrp > 0.03:
                interp = "Options expensive"
            elif vrp < -0.03:
                interp = "Options cheap"
            else:
                interp = "Fair value"
            
            vol_analysis_data.append([
                f'{tf}-day',
                f'{hv:.1%}',
                f'{vrp:.1%}',
                interp
            ])
        
        vol_analysis_table = Table(vol_analysis_data, colWidths=[1.8*inch, 1.8*inch, 1.8*inch, 2.1*inch])
        vol_analysis_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),  # Larger header font
            ('FONTSIZE', (0, 1), (-1, -1), 10),  # Good data font size
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgrey, colors.white])  # Alternating rows
        ]))
        
        story.append(vol_analysis_table)
        
        story.append(PageBreak())
        return story

    def _create_charts_section(self, plots_generated: Dict[str, str]) -> List:
        """Create charts section with embedded images."""
        story = []
        
        story.append(Paragraph("Charts & Visualizations", self.styles['SectionHeader']))
        
        # Add each chart if the file exists
        chart_titles = {
            'vrp_trend': 'VRP Trend Analysis',
            'volatility_comparison': 'Historical vs Implied Volatility',
            'strike_levels': 'Option Strike Levels',
            'gamma_wall': 'Gamma Wall Analysis',
            'vanna_exposure': 'Vanna (dDelta/dVol) Exposure Analysis',
            'charm_exposure': 'Charm (dDelta/dTime) Exposure Analysis',
            'volga_exposure': 'Volga (dVega/dVol) Exposure Analysis',
            'options_flow': 'Advanced Options Flow Analysis',
            'put_call': 'Put/Call Ratio Analysis',
            'liquidity': 'Money Market Liquidity Indicators (Last 6 Months)',
            'treasury_auctions': 'Treasury Auction Analysis'
        }
        
        for chart_key, chart_title in chart_titles.items():
            chart_path = plots_generated.get(chart_key)
            if chart_path and os.path.exists(chart_path):
                story.append(Paragraph(chart_title, self.styles['SubSection']))
                
                try:
                    # Add image with proper sizing
                    img = Image(chart_path, width=6*inch, height=4*inch)
                    story.append(img)
                    story.append(Spacer(1, 0.3*inch))
                except Exception as e:
                    story.append(Paragraph(f"Chart could not be loaded: {str(e)}", self.styles['Normal']))
                    story.append(Spacer(1, 0.2*inch))
        
        return story

    def _create_macro_analysis_section(self, macro_analysis: str) -> List:
        """Create macro analysis section."""
        story = []
        
        story.append(PageBreak())
        story.append(Paragraph("Macro & Geopolitical Analysis", self.styles['SectionHeader']))
        
        # Split the analysis into paragraphs and format
        paragraphs = macro_analysis.split('\n\n')
        
        for para in paragraphs:
            if para.strip():
                # Handle markdown-style headers
                if para.startswith('# '):
                    story.append(Paragraph(para[2:], self.styles['SectionHeader']))
                elif para.startswith('## '):
                    story.append(Paragraph(para[3:], self.styles['SubSection']))
                elif para.startswith('**') and para.endswith('**'):
                    story.append(Paragraph(f"<b>{para[2:-2]}</b>", self.styles['Normal']))
                else:
                    # Convert markdown-style bold text properly
                    formatted_para = para
                    # Handle **text** bold formatting
                    formatted_para = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', formatted_para)
                    story.append(Paragraph(formatted_para, self.styles['Normal']))
                story.append(Spacer(1, 0.1*inch))
        
        return story

    def _create_trading_narrative_section(self, trading_narrative: str) -> List:
        """Create trading narrative section."""
        story = []
        
        story.append(PageBreak())
        story.append(Paragraph("Trading Narrative Analysis", self.styles['SectionHeader']))
        
        # Split the narrative into paragraphs and format
        paragraphs = trading_narrative.split('\n\n')
        
        for para in paragraphs:
            if para.strip():
                # Handle markdown-style headers
                if para.startswith('# '):
                    story.append(Paragraph(para[2:], self.styles['SectionHeader']))
                elif para.startswith('## '):
                    story.append(Paragraph(para[3:], self.styles['SubSection']))
                elif para.startswith('**') and para.endswith('**'):
                    story.append(Paragraph(f"<b>{para[2:-2]}</b>", self.styles['Normal']))
                else:
                    # Convert markdown-style bold text and bullet points properly
                    formatted_para = para
                    # Handle **text** bold formatting
                    formatted_para = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', formatted_para)
                    if para.startswith('• '):
                        formatted_para = f"• {formatted_para[2:]}"
                    story.append(Paragraph(formatted_para, self.styles['Normal']))
                story.append(Spacer(1, 0.1*inch))
        
        return story

    def _create_error_summary_section(self, error_summary: List[str]) -> List:
        """Create error summary section."""
        story = []
        
        if error_summary:
            story.append(PageBreak())
            story.append(Paragraph("Analysis Notes & Limitations", self.styles['SectionHeader']))
            
            story.append(Paragraph("The following issues were encountered during analysis:", self.styles['Normal']))
            story.append(Spacer(1, 0.1*inch))
            
            for i, error in enumerate(error_summary, 1):
                story.append(Paragraph(f"{i}. {error}", self.styles['Normal']))
            
            story.append(Spacer(1, 0.2*inch))
            story.append(Paragraph(
                "These limitations should be considered when interpreting the analysis results.",
                self.styles['Normal']
            ))
        
        return story

    def _create_options_flow_section(self, options_flow_analysis: Dict[str, Any]) -> List:
        """Create advanced options flow analysis section."""
        story = []
        
        story.append(Paragraph("Advanced Options Flow Analysis", self.styles['SectionHeader']))
        
        # Flow analysis explanation
        flow_explanation = """
        Advanced options flow analysis examines dealer positioning across multiple time horizons using 
        second-order Greeks (vanna, charm, volga, color) to identify market structure and directional bias. 
        This analysis helps understand how market makers are positioned and potential flow pressures.
        """
        story.append(Paragraph(flow_explanation, self.styles['Normal']))
        story.append(Spacer(1, 0.2*inch))
        
        # Flow signals summary
        flow_signals = options_flow_analysis.get('flow_signals', {})
        story.append(Paragraph("Flow Signals Summary", self.styles['SubSection']))
        
        flow_data = [
            ['Metric', 'Value'],
            ['Primary Signal', flow_signals.get('primary_signal', 'Unknown')],
            ['Confidence Level', flow_signals.get('confidence', 'Unknown')],
            ['Dominant Time Horizon', flow_signals.get('time_horizon', 'Unknown')],
        ]
        
        flow_table = Table(flow_data, colWidths=[3*inch, 4*inch])
        flow_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgrey, colors.white])
        ]))
        
        story.append(flow_table)
        story.append(Spacer(1, 0.2*inch))
        
        # Key factors
        key_factors = flow_signals.get('key_factors', [])
        if key_factors:
            story.append(Paragraph("Key Factors", self.styles['SubSection']))
            for factor in key_factors:
                story.append(Paragraph(f"• {factor}", self.styles['Normal']))
            story.append(Spacer(1, 0.2*inch))
        
        # Risk factors
        risk_factors = flow_signals.get('risk_factors', [])
        if risk_factors:
            story.append(Paragraph("Risk Factors", self.styles['SubSection']))
            for risk in risk_factors:
                story.append(Paragraph(f"⚠️ {risk}", self.styles['Normal']))
            story.append(Spacer(1, 0.2*inch))
        
        # Aggregate Greek exposures
        aggregate_metrics = options_flow_analysis.get('aggregate_metrics', {})
        agg_exposures = aggregate_metrics.get('aggregate_exposures', {})
        
        if agg_exposures:
            story.append(Paragraph("Aggregate Greek Exposures", self.styles['SubSection']))
            
            greek_data = [['Greek', 'Total Exposure', 'Short Term', 'Long Term']]
            
            for greek, data in agg_exposures.items():
                if isinstance(data, dict):
                    greek_name = greek.replace('net_dealer_', '').replace('_', ' ').title()
                    greek_data.append([
                        greek_name,
                        f"{data.get('total', 0):,.0f}",
                        f"{data.get('short_term', 0):,.0f}",
                        f"{data.get('long_term', 0):,.0f}"
                    ])
            
            greek_table = Table(greek_data, colWidths=[1.8*inch, 1.8*inch, 1.8*inch, 1.8*inch])
            greek_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgreen, colors.white])
            ]))
            
            story.append(greek_table)
        
        story.append(PageBreak())
        return story

    def _create_put_call_section(self, put_call_analysis: Dict[str, Any]) -> List:
        """Create put/call ratio analysis section."""
        story = []
        
        story.append(Paragraph("Put/Call Ratio Analysis", self.styles['SectionHeader']))
        
        # P/C analysis explanation
        pc_explanation = """
        Put/Call ratio analysis examines the relationship between put and call option activity across 
        different moneyness levels and Greek weightings. This provides insights into market sentiment, 
        hedging activity, and potential contrarian signals.
        """
        story.append(Paragraph(pc_explanation, self.styles['Normal']))
        story.append(Spacer(1, 0.2*inch))
        
        # Sentiment signals
        sentiment_signals = put_call_analysis.get('sentiment_signals', {})
        story.append(Paragraph("Sentiment Analysis", self.styles['SubSection']))
        
        sentiment_data = [
            ['Metric', 'Value'],
            ['Primary Sentiment', sentiment_signals.get('primary_sentiment', 'Unknown')],
            ['Confidence Level', sentiment_signals.get('confidence', 'Unknown')],
            ['Flow Pressure', sentiment_signals.get('flow_pressure', 'Unknown')],
        ]
        
        sentiment_table = Table(sentiment_data, colWidths=[3*inch, 4*inch])
        sentiment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkorange),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightyellow),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightyellow, colors.white])
        ]))
        
        story.append(sentiment_table)
        story.append(Spacer(1, 0.2*inch))
        
        # Key indicators
        key_indicators = sentiment_signals.get('key_indicators', [])
        if key_indicators:
            story.append(Paragraph("Key Indicators", self.styles['SubSection']))
            for indicator in key_indicators:
                story.append(Paragraph(f"• {indicator}", self.styles['Normal']))
            story.append(Spacer(1, 0.2*inch))
        
        # Contrarian signals
        contrarian_signals = sentiment_signals.get('contrarian_signals', [])
        if contrarian_signals:
            story.append(Paragraph("Contrarian Signals", self.styles['SubSection']))
            for signal in contrarian_signals:
                story.append(Paragraph(f"🔄 {signal}", self.styles['Normal']))
            story.append(Spacer(1, 0.2*inch))
        
        # Overall P/C ratios
        overall_ratios = put_call_analysis.get('overall_ratios', {})
        if overall_ratios:
            story.append(Paragraph("Put/Call Ratios", self.styles['SubSection']))
            
            ratio_data = [['Metric', 'Ratio', 'Interpretation']]
            
            ratio_interpretations = {
                'pc_volume_ratio': 'Volume-based sentiment',
                'pc_oi_ratio': 'Open interest positioning',
                'pc_gamma_ratio': 'Gamma-weighted activity',
                'pc_vega_ratio': 'Volatility exposure',
                'pc_delta_ratio': 'Directional exposure',
                'pc_vanna_ratio': 'Vol/spot correlation'
            }
            
            for ratio_key, interpretation in ratio_interpretations.items():
                if ratio_key in overall_ratios:
                    ratio_value = overall_ratios[ratio_key]
                    sentiment = "Bearish" if ratio_value > 1.2 else "Bullish" if ratio_value < 0.8 else "Neutral"
                    ratio_data.append([
                        ratio_key.replace('pc_', 'P/C ').replace('_', ' ').title(),
                        f"{ratio_value:.2f}",
                        f"{interpretation} ({sentiment})"
                    ])
            
            ratio_table = Table(ratio_data, colWidths=[2.3*inch, 1.2*inch, 3.7*inch])
            ratio_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkred),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightpink),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightpink, colors.white])
            ]))
            
            story.append(ratio_table)
            story.append(Spacer(1, 0.2*inch))
        
        # Flow analysis
        flow_analysis = put_call_analysis.get('flow_analysis', {})
        if flow_analysis:
            story.append(Paragraph("Directional Flow Analysis", self.styles['SubSection']))
            
            flow_text = f"""
            <b>OTM P/C Ratio:</b> {flow_analysis.get('otm_ratio', 0):.2f} (Directional bets)<br/>
            <b>ITM P/C Ratio:</b> {flow_analysis.get('itm_ratio', 0):.2f} (Hedging activity)<br/>
            <b>Directional Bias:</b> {flow_analysis.get('directional_bias', 'Unknown')}<br/>
            <b>Net Vanna Flow:</b> {flow_analysis.get('net_vanna_flow', 0):,.0f}
            """
            story.append(Paragraph(flow_text, self.styles['Normal']))
        
        story.append(PageBreak())
        return story

    def _create_treasury_auction_section(self, treasury_auction_analysis: Dict[str, Any],
                                        treasury_auction_table: Optional[pd.DataFrame]) -> List:
        """Create treasury auction analysis section."""
        story = []
        
        story.append(Paragraph("Treasury Auction Analysis", self.styles['SectionHeader']))
        
        # Treasury auction explanation
        auction_explanation = """
        Treasury auctions are conducted by the U.S. Treasury to issue government securities. 
        Key metrics include bid-to-cover ratios (indicating demand strength), competitive vs. non-competitive 
        bidding patterns, and yield outcomes. Strong bid-to-cover ratios (>2.5) typically indicate healthy 
        investor appetite for Treasury securities.
        """
        story.append(Paragraph(auction_explanation, self.styles['Normal']))
        story.append(Spacer(1, 0.2*inch))
        
        # Key auction metrics
        if treasury_auction_analysis:
            story.append(Paragraph("Key Auction Metrics", self.styles['SubSection']))
            
            metrics_data = [
                ['Metric', 'Value', 'Assessment'],
                [
                    'Average Bid-to-Cover Ratio',
                    f"{treasury_auction_analysis.get('avg_bid_to_cover', 0):.2f}",
                    'Strong' if treasury_auction_analysis.get('avg_bid_to_cover', 0) > 2.8 else 
                    'Average' if treasury_auction_analysis.get('avg_bid_to_cover', 0) > 2.2 else 'Weak'
                ],
                [
                    'Average High Yield',
                    f"{treasury_auction_analysis.get('avg_yield', 0):.3f}%",
                    'Current market rate'
                ],
                [
                    'Total Amount Issued',
                    f"${treasury_auction_analysis.get('total_issued_billions', 0):.1f}B",
                    'Recent issuance volume'
                ],
                [
                    'Bid-to-Cover Trend',
                    treasury_auction_analysis.get('bid_to_cover_trend', 'N/A'),
                    'Recent demand pattern'
                ]
            ]
            
            metrics_table = Table(metrics_data, colWidths=[2.5*inch, 1.5*inch, 2.5*inch])
            metrics_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            story.append(metrics_table)
            story.append(Spacer(1, 0.3*inch))
        
        # Recent auction details table
        if treasury_auction_table is not None and not treasury_auction_table.empty:
            story.append(Paragraph("Recent Treasury Auctions", self.styles['SubSection']))
            
            # Convert DataFrame to table data
            table_data = [treasury_auction_table.columns.tolist()]
            for _, row in treasury_auction_table.iterrows():
                table_data.append([str(val) for val in row])
            
            # Create table with appropriate column widths
            col_widths = [1.0*inch, 1.2*inch, 0.8*inch, 1.2*inch, 1.2*inch, 1.0*inch, 1.1*inch]
            
            auction_table = Table(table_data, colWidths=col_widths)
            auction_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgrey, colors.white])
            ]))
            
            story.append(auction_table)
            story.append(Spacer(1, 0.3*inch))
        
        # Security type and term breakdowns
        if treasury_auction_analysis and 'security_breakdown' in treasury_auction_analysis:
            story.append(Paragraph("Security Type Distribution", self.styles['SubSection']))
            
            sec_breakdown = treasury_auction_analysis['security_breakdown']
            term_breakdown = treasury_auction_analysis.get('term_breakdown', {})
            
            breakdown_text = "Recent auction composition:\n"
            
            if sec_breakdown:
                breakdown_text += "\nSecurity Types:\n"
                for sec_type, count in sec_breakdown.items():
                    percentage = (count / treasury_auction_analysis['total_auctions']) * 100
                    breakdown_text += f"• {sec_type}: {count} auctions ({percentage:.1f}%)\n"
            
            if term_breakdown:
                breakdown_text += "\nSecurity Terms:\n"
                for term, count in term_breakdown.items():
                    percentage = (count / treasury_auction_analysis['total_auctions']) * 100
                    breakdown_text += f"• {term}: {count} auctions ({percentage:.1f}%)\n"
            
            story.append(Paragraph(breakdown_text, self.styles['Normal']))
        
        # Market assessment
        if treasury_auction_analysis:
            story.append(Paragraph("Market Assessment", self.styles['SubSection']))
            
            avg_btc = treasury_auction_analysis.get('avg_bid_to_cover', 0)
            btc_trend = treasury_auction_analysis.get('bid_to_cover_trend', 'N/A')
            
            assessment_text = ""
            if avg_btc > 2.8:
                assessment_text += "🟢 STRONG DEMAND: Above-average bid-to-cover ratios indicate healthy investor appetite for Treasury securities.\n\n"
            elif avg_btc > 2.2:
                assessment_text += "🟡 MODERATE DEMAND: Average bid-to-cover ratios suggest normal market conditions.\n\n"
            else:
                assessment_text += "🔴 WEAK DEMAND: Below-average bid-to-cover ratios may indicate reduced investor interest.\n\n"
            
            if btc_trend == "IMPROVING":
                assessment_text += "📈 IMPROVING TREND: Recent auctions show strengthening demand patterns."
            elif btc_trend == "DECLINING":
                assessment_text += "📉 DECLINING TREND: Recent auctions show weakening demand patterns."
            else:
                assessment_text += "➡️ STABLE TREND: Consistent demand patterns across recent auctions."
            
            story.append(Paragraph(assessment_text, self.styles['Normal']))
        
        story.append(PageBreak())
        return story 