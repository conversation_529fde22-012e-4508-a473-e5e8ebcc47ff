"""
HTML Report Generator
Generates comprehensive HTML reports with embedded charts.
"""

import os
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional, List


class HTMLReportGenerator:
    """Generates HTML reports for analysis results."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def generate_report(self, money_market_analysis: Dict[str, Any], 
                       option_signals_analysis: Dict[str, Any],
                       volatility_metrics: Optional[Dict[str, Any]] = None,
                       volatility_table: Optional[pd.DataFrame] = None,
                       plots_generated: Optional[Dict[str, str]] = None,
                       error_summary: Optional[List[str]] = None,
                       options_flow_analysis: Optional[Dict[str, Any]] = None,
                       put_call_analysis: Optional[Dict[str, Any]] = None) -> str:
        """Generate comprehensive HTML report."""
        
        # Get analysis data
        mm = money_market_analysis or {}
        opt = option_signals_analysis or {}
        vol_metrics = volatility_metrics or {}
        flow_analysis = options_flow_analysis or {}
        pc_analysis = put_call_analysis or {}
        plots = plots_generated or {}
        errors = error_summary or []
        
        # Generate HTML content
        html_content = self._generate_html_header()
        html_content += self._generate_summary_section(mm, opt, vol_metrics, flow_analysis, pc_analysis)
        html_content += self._generate_money_market_section(mm)
        html_content += self._generate_option_signals_section(opt)
        
        if flow_analysis and 'error' not in flow_analysis:
            html_content += self._generate_options_flow_section(flow_analysis)
        
        if pc_analysis and 'error' not in pc_analysis:
            html_content += self._generate_put_call_section(pc_analysis)
        
        if vol_metrics:
            html_content += self._generate_volatility_section(vol_metrics, volatility_table)
        
        html_content += self._generate_charts_section(plots)
        
        if errors:
            html_content += self._generate_errors_section(errors)
            
        html_content += self._generate_html_footer()
        
        # Save report
        report_path = os.path.join(self.output_dir, "checklist_report.html")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(html_content)
            
        print(f"HTML report generated: {report_path}")
        return report_path
        
    def _generate_html_header(self) -> str:
        """Generate HTML header with CSS styling."""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market Analysis Checklist Report</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; border-left: 4px solid #3498db; padding-left: 15px; margin-top: 30px; }}
        h3 {{ color: #2c3e50; margin-top: 20px; }}
        .summary {{ background-color: #ecf0f1; padding: 20px; border-radius: 8px; margin-bottom: 30px; }}
        .signal-box {{ padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold; }}
        .bullish {{ background-color: #d5f4e6; border-left: 5px solid #27ae60; }}
        .bearish {{ background-color: #fadbd8; border-left: 5px solid #e74c3c; }}
        .neutral {{ background-color: #fef9e7; border-left: 5px solid #f39c12; }}
        .unknown {{ background-color: #eaecee; border-left: 5px solid #95a5a6; }}
        .high-confidence {{ border-width: 5px; }}
        .medium-confidence {{ border-width: 3px; }}
        .low-confidence {{ border-width: 2px; }}
        .data-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
        .data-table th, .data-table td {{ border: 1px solid #bdc3c7; padding: 12px; text-align: left; }}
        .data-table th {{ background-color: #34495e; color: white; }}
        .data-table tr:nth-child(even) {{ background-color: #f8f9fa; }}
        .images {{ display: flex; flex-wrap: wrap; justify-content: space-around; margin-top: 20px; }}
        .image-container {{ width: 48%; margin-bottom: 15px; text-align: center; }}
        .image-container img {{ max-width: 100%; height: auto; border: 1px solid #bdc3c7; border-radius: 5px; }}
        .error-section {{ background-color: #fadbd8; padding: 15px; border-radius: 5px; margin-top: 20px; }}
        .timestamp {{ text-align: center; color: #7f8c8d; margin-top: 30px; font-style: italic; }}
        .simulated-note {{ color: #e67e22; font-style: italic; font-size: 0.9em; margin-top: 5px; }}
        @media (max-width: 768px) {{ .image-container {{ width: 100%; }} }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Market Analysis Checklist Report</h1>
        <div class="timestamp">Generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</div>
"""

    def _generate_summary_section(self, mm: Dict[str, Any], opt: Dict[str, Any], 
                                 vol_metrics: Dict[str, Any], flow_analysis: Dict[str, Any],
                                 pc_analysis: Dict[str, Any]) -> str:
        """Generate executive summary section."""
        # Determine overall bias
        mm_bias = mm.get("bias", "UNKNOWN")
        opt_signal = opt.get("signal", "UNKNOWN")
        
        # Get confidence levels
        mm_confidence = mm.get("confidence", "NONE")
        opt_confidence = opt.get("confidence", "NONE")
        
        # Determine CSS classes
        mm_class = self._get_signal_class(mm_bias, mm_confidence)
        opt_class = self._get_signal_class(opt_signal, opt_confidence)
        
        html = f"""
        <div class="summary">
            <h2>Executive Summary</h2>
            <div class="signal-box {mm_class}">
                <strong>Money Market Bias:</strong> {mm_bias} (Confidence: {mm_confidence})
                <br><small>{mm.get('liquidity_condition', 'Unknown')} liquidity conditions</small>
            </div>
            <div class="signal-box {opt_class}">
                <strong>Option Signal:</strong> {opt_signal} (Confidence: {opt_confidence})
                <br><small>{opt.get('rationale', 'No rationale provided')}</small>
            </div>
        """
        
        # Add VRP summary if available
        if vol_metrics:
            current_vrp = vol_metrics.get('current_vrp_20', 0)
            vrp_percentile = vol_metrics.get('vrp_percentile', 50)
            vrp_class = "bullish" if current_vrp < 0 else "bearish" if current_vrp > 0.05 else "neutral"
            
            html += f"""
            <div class="signal-box {vrp_class}">
                <strong>VRP Analysis:</strong> {current_vrp:.1%} (Percentile: {vrp_percentile:.0f}th)
                <br><small>{'IV Discount' if current_vrp < 0 else 'IV Premium' if current_vrp > 0 else 'Neutral'}</small>
            </div>
            """
        
        # Add options flow summary if available
        if flow_analysis and 'error' not in flow_analysis:
            flow_signals = flow_analysis.get('flow_signals', {})
            flow_signal = flow_signals.get('primary_signal', 'NEUTRAL')
            flow_confidence = flow_signals.get('confidence', 'LOW')
            flow_class = self._get_signal_class(flow_signal, flow_confidence)
            
            html += f"""
            <div class="signal-box {flow_class}">
                <strong>Options Flow Signal:</strong> {flow_signal} (Confidence: {flow_confidence})
                <br><small>Time Horizon: {flow_signals.get('time_horizon', 'Unknown')}</small>
            </div>
            """
        
        # Add put/call summary if available
        if pc_analysis and 'error' not in pc_analysis:
            sentiment_signals = pc_analysis.get('sentiment_signals', {})
            sentiment = sentiment_signals.get('primary_sentiment', 'NEUTRAL')
            sentiment_confidence = sentiment_signals.get('confidence', 'LOW')
            sentiment_class = self._get_signal_class(sentiment, sentiment_confidence)
            
            html += f"""
            <div class="signal-box {sentiment_class}">
                <strong>Put/Call Sentiment:</strong> {sentiment} (Confidence: {sentiment_confidence})
                <br><small>Flow Pressure: {sentiment_signals.get('flow_pressure', 'Unknown')}</small>
            </div>
            """
        
        html += "</div>"
        return html
        
    def _generate_money_market_section(self, mm: Dict[str, Any]) -> str:
        """Generate money market analysis section."""
        html = f"""
        <h2>Money Market Analysis</h2>
        <h3>Liquidity Conditions</h3>
        <p><strong>Current Condition:</strong> {mm.get('liquidity_condition', 'Unknown')}</p>
        <p><strong>Trend:</strong> {mm.get('liquidity_trend', 'Unknown')}</p>
        <p><strong>Fed Net Liquidity:</strong> ${mm.get('current_fed_net_liquidity', 0):,.0f}B</p>
        
        <h3>Component Analysis</h3>
        <p><strong>TGA Impact:</strong> {mm.get('tga_impact', 'Unknown')}</p>
        <p><strong>RRP Impact:</strong> {mm.get('rrp_impact', 'Unknown')}</p>
        <p><strong>Treasury Auctions:</strong> {mm.get('treasury_auction_assessment', 'Unknown')}</p>
        
        <h3>Trading Recommendations</h3>
        <p><strong>Recommended Position Type:</strong> {mm.get('position_type', 'N/A')}</p>
        <p><strong>Recommended Expiry:</strong> {mm.get('recommended_expiry', 'N/A')}</p>
        """
        return html
        
    def _generate_option_signals_section(self, opt: Dict[str, Any]) -> str:
        """Generate option signals analysis section."""
        data_source = opt.get('data_source', 'Unknown')
        
        html = f"""
        <h2>Option Signals Analysis</h2>
        <h3>Current Market Structure</h3>
        <p><strong>Current Price:</strong> ${opt.get('current_price', 0):,.2f}</p>
        <p><strong>Gamma Exposure:</strong> {opt.get('gamma_exposure', 'Unknown')}</p>
        <p><strong>Gamma Trend:</strong> {opt.get('gamma_trend', 'Unknown')}</p>
        <p><strong>Vega Trend:</strong> {opt.get('vega_trend', 'Unknown')}</p>
        
        <h3>Strike Level Analysis</h3>
        """
        
        # Add wall information
        if opt.get('approaching_call_wall'):
            html += f"<p><strong>⚠️ Approaching Call Wall:</strong> ${opt.get('nearest_call_wall', 0):,.0f}</p>"
        if opt.get('approaching_put_wall'):
            html += f"<p><strong>⚠️ Approaching Put Wall:</strong> ${opt.get('nearest_put_wall', 0):,.0f}</p>"
            
        html += f"""
        <h3>Greek Analysis</h3>
        <p><strong>Net Gamma:</strong> {opt.get('net_gamma', 0):,.0f}</p>
        <p><strong>Net Vega:</strong> {opt.get('net_vega', 0):,.0f}</p>
        <p><strong>Average Implied Volatility:</strong> {opt.get('avg_implied_vol', 0):.1%}</p>
        
        <h3>Position Recommendations</h3>
        <p><strong>Position Type:</strong> {opt.get('position_type', 'N/A')}</p>
        <p><strong>Recommended Expiry:</strong> {opt.get('recommended_expiry', 'N/A')}</p>
        <p><strong>Position Size:</strong> {opt.get('position_size', 'N/A')}</p>
        
        <p class="simulated-note">Option analysis based on {data_source} data.</p>
        """
        return html
        
    def _generate_volatility_section(self, vol_metrics: Dict[str, Any], 
                                   volatility_table: Optional[pd.DataFrame]) -> str:
        """Generate volatility analysis section."""
        html = f"""
        <h2>Volatility & VRP Analysis</h2>
        <h3>Current Metrics</h3>
        <p><strong>20-Day Historical Volatility:</strong> {vol_metrics.get('current_hv_20', 0):.1%}</p>
        <p><strong>Implied Volatility:</strong> {vol_metrics.get('current_iv', 0):.1%}</p>
        <p><strong>20-Day VRP:</strong> {vol_metrics.get('current_vrp_20', 0):.1%}</p>
        <p><strong>VRP Percentile:</strong> {vol_metrics.get('vrp_percentile', 0):.0f}th</p>
        """
        
        # Add volatility table if available
        if volatility_table is not None and not volatility_table.empty:
            html += "<h3>Volatility Summary Table</h3>"
            html += volatility_table.to_html(classes="data-table", escape=False, index=False)
            
        return html
        
    def _generate_charts_section(self, plots: Dict[str, str]) -> str:
        """Generate charts section."""
        html = """
        <h2>Charts & Visualizations</h2>
        <div class="images">
        """
        
        # Add liquidity chart
        html += '<div class="image-container"><h3>Money Market Indicators</h3>'
        if plots.get("liquidity") and os.path.exists(plots["liquidity"]):
            html += f'<img src="{os.path.basename(plots["liquidity"])}" alt="Money Market Indicators">'
        else:
            html += '<p><i>Liquidity plot could not be generated due to data issues.</i></p>'
        html += '</div>'
        
        # Add strike levels chart
        html += '<div class="image-container"><h3>Option Strike Levels</h3>'
        if plots.get("strike_levels") and os.path.exists(plots["strike_levels"]):
            html += f'<img src="{os.path.basename(plots["strike_levels"])}" alt="Option Strike Levels">'
        else:
            html += '<p><i>Strike levels plot could not be generated due to data issues.</i></p>'
        html += '</div>'
        
        # Add VRP trend chart
        html += '<div class="image-container"><h3>VRP Trend</h3>'
        if plots.get("vrp_trend") and os.path.exists(plots["vrp_trend"]):
            html += f'<img src="{os.path.basename(plots["vrp_trend"])}" alt="VRP Trend">'
        else:
            html += '<p><i>VRP trend plot could not be generated due to data issues.</i></p>'
        html += '</div>'
        
        # Add volatility comparison chart
        html += '<div class="image-container"><h3>Volatility Comparison</h3>'
        if plots.get("volatility_comparison") and os.path.exists(plots["volatility_comparison"]):
            html += f'<img src="{os.path.basename(plots["volatility_comparison"])}" alt="Volatility Comparison">'
        else:
            html += '<p><i>Volatility comparison plot could not be generated due to data issues.</i></p>'
        html += '</div>'
        
        # Add gamma wall chart
        html += '<div class="image-container"><h3>Gamma Wall Analysis</h3>'
        if plots.get("gamma_wall") and os.path.exists(plots["gamma_wall"]):
            html += f'<img src="{os.path.basename(plots["gamma_wall"])}" alt="Gamma Wall Analysis">'
        else:
            html += '<p><i>Gamma wall plot could not be generated due to data issues.</i></p>'
        html += '</div>'
        
        # Add options flow chart
        html += '<div class="image-container"><h3>Options Flow Analysis</h3>'
        if plots.get("options_flow") and os.path.exists(plots["options_flow"]):
            html += f'<img src="{os.path.basename(plots["options_flow"])}" alt="Options Flow Analysis">'
        else:
            html += '<p><i>Options flow plot could not be generated due to data issues.</i></p>'
        html += '</div>'
        
        # Add put/call analysis chart
        html += '<div class="image-container"><h3>Put/Call Analysis</h3>'
        if plots.get("put_call") and os.path.exists(plots["put_call"]):
            html += f'<img src="{os.path.basename(plots["put_call"])}" alt="Put/Call Analysis">'
        else:
            html += '<p><i>Put/call analysis plot could not be generated due to data issues.</i></p>'
        html += '</div>'
        
        html += '</div>'
        return html
        
    def _generate_errors_section(self, errors: List[str]) -> str:
        """Generate errors section."""
        html = """
        <div class="error-section">
            <h3>⚠️ Warnings & Errors</h3>
            <ul>
        """
        for error in errors:
            html += f"<li>{error}</li>"
        html += "</ul></div>"
        return html
        
    def _generate_html_footer(self) -> str:
        """Generate HTML footer."""
        return """
    </div>
</body>
</html>
"""
        
    def _get_signal_class(self, signal: str, confidence: str) -> str:
        """Get CSS class for signal styling."""
        # Determine base class
        if "BULLISH" in signal.upper() or "LONG" in signal.upper():
            base_class = "bullish"
        elif "BEARISH" in signal.upper() or "SHORT" in signal.upper():
            base_class = "bearish"
        elif "NEUTRAL" in signal.upper():
            base_class = "neutral"
        else:
            base_class = "unknown"
            
        # Add confidence class
        if confidence.upper() == "HIGH":
            confidence_class = "high-confidence"
        elif confidence.upper() == "MEDIUM":
            confidence_class = "medium-confidence"
        else:
            confidence_class = "low-confidence"
            
        return f"{base_class} {confidence_class}"
    
    def _generate_options_flow_section(self, flow_analysis: Dict[str, Any]) -> str:
        """Generate options flow analysis section."""
        flow_signals = flow_analysis.get('flow_signals', {})
        aggregate_metrics = flow_analysis.get('aggregate_metrics', {})
        
        html = f"""
        <h2>Advanced Options Flow Analysis</h2>
        <h3>Flow Signals</h3>
        <p><strong>Primary Signal:</strong> {flow_signals.get('primary_signal', 'Unknown')}</p>
        <p><strong>Confidence:</strong> {flow_signals.get('confidence', 'Unknown')}</p>
        <p><strong>Time Horizon:</strong> {flow_signals.get('time_horizon', 'Unknown')}</p>
        
        <h3>Key Factors</h3>
        """
        
        key_factors = flow_signals.get('key_factors', [])
        if key_factors:
            html += "<ul>"
            for factor in key_factors:
                html += f"<li>{factor}</li>"
            html += "</ul>"
        else:
            html += "<p>No key factors identified.</p>"
        
        # Risk factors
        risk_factors = flow_signals.get('risk_factors', [])
        if risk_factors:
            html += "<h3>Risk Factors</h3><ul>"
            for risk in risk_factors:
                html += f"<li>⚠️ {risk}</li>"
            html += "</ul>"
        
        # Aggregate exposures
        if aggregate_metrics:
            agg_exposures = aggregate_metrics.get('aggregate_exposures', {})
            if agg_exposures:
                html += """
                <h3>Aggregate Greek Exposures</h3>
                <table class="data-table">
                    <tr><th>Greek</th><th>Total Exposure</th><th>Short Term</th><th>Long Term</th></tr>
                """
                
                for greek, data in agg_exposures.items():
                    if isinstance(data, dict):
                        greek_name = greek.replace('net_dealer_', '').replace('_', ' ').title()
                        html += f"""
                        <tr>
                            <td>{greek_name}</td>
                            <td>{data.get('total', 0):,.0f}</td>
                            <td>{data.get('short_term', 0):,.0f}</td>
                            <td>{data.get('long_term', 0):,.0f}</td>
                        </tr>
                        """
                
                html += "</table>"
        
        return html
    
    def _generate_put_call_section(self, pc_analysis: Dict[str, Any]) -> str:
        """Generate put/call ratio analysis section."""
        sentiment_signals = pc_analysis.get('sentiment_signals', {})
        overall_ratios = pc_analysis.get('overall_ratios', {})
        flow_analysis = pc_analysis.get('flow_analysis', {})
        
        html = f"""
        <h2>Put/Call Ratio Analysis</h2>
        <h3>Sentiment Signals</h3>
        <p><strong>Primary Sentiment:</strong> {sentiment_signals.get('primary_sentiment', 'Unknown')}</p>
        <p><strong>Confidence:</strong> {sentiment_signals.get('confidence', 'Unknown')}</p>
        <p><strong>Flow Pressure:</strong> {sentiment_signals.get('flow_pressure', 'Unknown')}</p>
        
        <h3>Key Indicators</h3>
        """
        
        key_indicators = sentiment_signals.get('key_indicators', [])
        if key_indicators:
            html += "<ul>"
            for indicator in key_indicators:
                html += f"<li>{indicator}</li>"
            html += "</ul>"
        else:
            html += "<p>No key indicators identified.</p>"
        
        # Contrarian signals
        contrarian_signals = sentiment_signals.get('contrarian_signals', [])
        if contrarian_signals:
            html += "<h3>Contrarian Signals</h3><ul>"
            for signal in contrarian_signals:
                html += f"<li>🔄 {signal}</li>"
            html += "</ul>"
        
        # Overall ratios table
        if overall_ratios:
            html += """
            <h3>Put/Call Ratios</h3>
            <table class="data-table">
                <tr><th>Metric</th><th>Ratio</th><th>Interpretation</th></tr>
            """
            
            ratio_interpretations = {
                'pc_volume_ratio': 'Volume-based sentiment',
                'pc_oi_ratio': 'Open interest positioning',
                'pc_gamma_ratio': 'Gamma-weighted activity',
                'pc_vega_ratio': 'Volatility exposure',
                'pc_delta_ratio': 'Directional exposure',
                'pc_vanna_ratio': 'Vol/spot correlation'
            }
            
            for ratio_key, interpretation in ratio_interpretations.items():
                if ratio_key in overall_ratios:
                    ratio_value = overall_ratios[ratio_key]
                    sentiment = "Bearish" if ratio_value > 1.2 else "Bullish" if ratio_value < 0.8 else "Neutral"
                    html += f"""
                    <tr>
                        <td>{ratio_key.replace('pc_', 'P/C ').replace('_', ' ').title()}</td>
                        <td>{ratio_value:.2f}</td>
                        <td>{interpretation} ({sentiment})</td>
                    </tr>
                    """
            
            html += "</table>"
        
        # Flow analysis
        if flow_analysis:
            html += f"""
            <h3>Flow Analysis</h3>
            <p><strong>OTM P/C Ratio:</strong> {flow_analysis.get('otm_ratio', 0):.2f} (Directional bets)</p>
            <p><strong>ITM P/C Ratio:</strong> {flow_analysis.get('itm_ratio', 0):.2f} (Hedging activity)</p>
            <p><strong>Directional Bias:</strong> {flow_analysis.get('directional_bias', 'Unknown')}</p>
            <p><strong>Net Vanna Flow:</strong> {flow_analysis.get('net_vanna_flow', 0):,.0f}</p>
            """
        
        return html 