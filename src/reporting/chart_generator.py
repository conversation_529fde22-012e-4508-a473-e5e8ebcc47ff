"""
Chart Generator
Handles all chart and visualization generation.
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, Any, Optional, List
from datetime import datetime


class ChartGenerator:
    """Generates charts and visualizations for analysis results."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def plot_liquidity_indicators(self, liquidity_df: pd.DataFrame, save_path: Optional[str] = None) -> Optional[str]:
        """Plot key liquidity indicators."""
        if liquidity_df.empty:
            print("Cannot plot liquidity indicators due to missing data.")
            return None

        fig, axs = plt.subplots(3, 1, figsize=(12, 15), sharex=True)
        fig.suptitle("Money Market Liquidity Indicators", fontsize=16)

        # Plot Fed Net Liquidity
        if "fed_net_liquidity" in liquidity_df.columns and not liquidity_df["fed_net_liquidity"].isnull().all():
            axs[0].plot(liquidity_df.index, liquidity_df["fed_net_liquidity"], 
                       color="blue", linewidth=2, label="Fed Net Liquidity")
            axs[0].axhline(y=0, color="r", linestyle="--", alpha=0.5)
            axs[0].set_title("Fed Net Liquidity")
            axs[0].set_ylabel("$ Billions (Scaled)")
            axs[0].grid(True)
            axs[0].legend()
        else:
            axs[0].text(0.5, 0.5, "Fed Net Liquidity Data Missing", 
                       horizontalalignment="center", verticalalignment="center", 
                       transform=axs[0].transAxes)
            axs[0].set_title("Fed Net Liquidity (Data Missing)")

        # Plot TGA Balance
        if "tga_balance" in liquidity_df.columns and not liquidity_df["tga_balance"].isnull().all():
            axs[1].plot(liquidity_df.index, liquidity_df["tga_balance"], 
                       color="green", linewidth=2, label="Treasury General Account")
            axs[1].set_title("Treasury General Account Balance")
            axs[1].set_ylabel("$ Billions")
            axs[1].grid(True)
            axs[1].legend()
        else:
            axs[1].text(0.5, 0.5, "TGA Data Missing", 
                       horizontalalignment="center", verticalalignment="center", 
                       transform=axs[1].transAxes)
            axs[1].set_title("Treasury General Account Balance (Data Missing)")

        # Plot RRP Usage
        if "rrp_amount" in liquidity_df.columns and not liquidity_df["rrp_amount"].isnull().all():
            axs[2].plot(liquidity_df.index, liquidity_df["rrp_amount"], 
                       color="purple", linewidth=2, label="Reverse Repo Facility Usage")
            axs[2].set_title("Reverse Repo Facility Usage")
            axs[2].set_ylabel("$ Billions")
            axs[2].grid(True)
            axs[2].legend()
        else:
            axs[2].text(0.5, 0.5, "RRP Data Missing", 
                       horizontalalignment="center", verticalalignment="center", 
                       transform=axs[2].transAxes)
            axs[2].set_title("Reverse Repo Facility Usage (Data Missing)")

        # Format x-axis
        axs[2].xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
        plt.xticks(rotation=45)
        plt.tight_layout(rect=[0, 0.03, 1, 0.97])

        if save_path:
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"Liquidity plot saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving liquidity plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

    def plot_strike_levels(self, option_chain: Dict[str, Any], strike_levels: Dict[str, Any], 
                          save_path: Optional[str] = None) -> Optional[str]:
        """Plot option strike levels and walls."""
        if not option_chain or not strike_levels:
            print("Cannot plot strike levels due to missing data.")
            return None

        calls_df = pd.DataFrame(option_chain.get("calls", []))
        puts_df = pd.DataFrame(option_chain.get("puts", []))
        current_price = strike_levels.get("current_price")
        
        if calls_df.empty or puts_df.empty or not current_price:
            print("Insufficient data for strike level plot.")
            return None

        # Filter data
        price_buffer = 250
        max_call_strike = current_price + price_buffer
        min_put_strike = current_price - price_buffer
        
        calls_filtered = calls_df[
            (calls_df["strike"] >= current_price) & 
            (calls_df["strike"] <= max_call_strike)
        ]
        puts_filtered = puts_df[
            (puts_df["strike"] <= current_price) & 
            (puts_df["strike"] >= min_put_strike)
        ]

        if calls_filtered.empty and puts_filtered.empty:
            print("No relevant option data for plotting.")
            return None

        # Create figure
        fig = plt.figure(figsize=(18, 14))
        ax1 = plt.subplot2grid((10, 2), (0, 0), rowspan=6)  # Calls
        ax2 = plt.subplot2grid((10, 2), (0, 1), rowspan=6)  # Puts

        # Calculate bar width
        all_strikes = pd.concat([calls_filtered["strike"], puts_filtered["strike"]]).sort_values().unique()
        strike_width = np.median(np.diff(all_strikes)) * 0.4 if len(all_strikes) > 1 else 10

        # Get wall data
        call_walls = strike_levels.get("call_walls", [])
        put_walls = strike_levels.get("put_walls", [])
        data_source = strike_levels.get("data_source", "Unknown")

        # Plot calls
        if not calls_filtered.empty:
            ax1.bar(calls_filtered["strike"], calls_filtered["openInterest"], 
                   width=strike_width, color="green", alpha=0.7, label=f"Call OI ({data_source})")

        ax1.axvline(x=current_price, color="blue", linestyle="--", linewidth=2, 
                   label=f"Current Price: {current_price:.2f}")

        # Plot call walls
        for i, wall in enumerate(call_walls):
            strike_val = wall["strike"]
            oi_val = wall["openInterest"]
            if current_price <= strike_val <= max_call_strike:
                ax1.axvline(x=strike_val, color="darkgreen", linestyle="-", linewidth=4, alpha=0.9, 
                          label=f"Call Wall {i+1}: {strike_val:.0f}")

        ax1.set_xlim(current_price, max_call_strike)
        ax1.set_title(f"CALL OPTIONS - Resistance Levels\n{option_chain.get('option_ticker', 'Unknown')} ({data_source})", 
                     fontsize=14, fontweight='bold', color='darkgreen')
        ax1.set_xlabel("Strike Price", fontsize=12)
        ax1.set_ylabel("Open Interest", fontsize=12)
        ax1.legend(loc="upper left", fontsize=9)
        ax1.grid(True, alpha=0.3)

        # Plot puts
        if not puts_filtered.empty:
            ax2.bar(puts_filtered["strike"], puts_filtered["openInterest"], 
                   width=strike_width, color="red", alpha=0.7, label=f"Put OI ({data_source})")

        ax2.axvline(x=current_price, color="blue", linestyle="--", linewidth=2, 
                   label=f"Current Price: {current_price:.2f}")

        # Plot put walls
        for i, wall in enumerate(put_walls):
            strike_val = wall["strike"]
            oi_val = wall["openInterest"]
            if min_put_strike <= strike_val <= current_price:
                ax2.axvline(x=strike_val, color="darkred", linestyle="-", linewidth=4, alpha=0.9, 
                          label=f"Put Wall {i+1}: {strike_val:.0f}")

        ax2.set_xlim(min_put_strike, current_price)
        ax2.set_title(f"PUT OPTIONS - Support Levels\n{option_chain.get('option_ticker', 'Unknown')} ({data_source})", 
                     fontsize=14, fontweight='bold', color='darkred')
        ax2.set_xlabel("Strike Price", fontsize=12)
        ax2.set_ylabel("Open Interest", fontsize=12)
        ax2.legend(loc="upper left", fontsize=9)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            try:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"Strike level plot saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving strike level plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

    def plot_gamma_wall(self, option_chain: Dict[str, Any], strike_levels: Dict[str, Any],
                       save_path: Optional[str] = None) -> Optional[str]:
        """Plot gamma wall analysis with actual option data."""
        if not option_chain:
            print("Cannot plot gamma wall due to missing option chain data.")
            return None

        try:
            fig, ax = plt.subplots(figsize=(12, 8))

            calls = option_chain.get('calls', [])
            puts = option_chain.get('puts', [])
            current_price = option_chain.get('current_price', 0)

            if not calls and not puts:
                ax.text(0.5, 0.5, "No option data available for Gamma Wall analysis",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title("Gamma Wall Analysis", fontsize=16, fontweight='bold')
                if save_path:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    plt.close(fig)
                    return save_path
                return None

            # Calculate gamma exposure by strike
            strikes = []
            gamma_exposure = []

            # Process calls
            for call in calls:
                strike = call.get('strike', 0)
                open_interest = call.get('open_interest', 0)
                if strike > 0 and open_interest > 0:
                    # Simplified gamma calculation (peaks at ATM)
                    moneyness = strike / current_price if current_price > 0 else 1
                    gamma = np.exp(-0.5 * ((moneyness - 1) / 0.1) ** 2) * open_interest
                    strikes.append(strike)
                    gamma_exposure.append(gamma)

            # Process puts (negative gamma for dealers)
            for put in puts:
                strike = put.get('strike', 0)
                open_interest = put.get('open_interest', 0)
                if strike > 0 and open_interest > 0:
                    moneyness = strike / current_price if current_price > 0 else 1
                    gamma = -np.exp(-0.5 * ((moneyness - 1) / 0.1) ** 2) * open_interest
                    strikes.append(strike)
                    gamma_exposure.append(gamma)

            if strikes:
                # Sort by strike for better visualization
                sorted_data = sorted(zip(strikes, gamma_exposure))
                strikes, gamma_exposure = zip(*sorted_data)

                # Create bar chart
                colors = ['red' if g < 0 else 'green' for g in gamma_exposure]
                ax.bar(strikes, gamma_exposure, alpha=0.7, color=colors, width=25)

                # Add current price line
                ax.axvline(x=current_price, color='blue', linestyle='--', linewidth=2,
                          label=f'Current Price: ${current_price:.0f}')

                # Find and mark significant gamma levels
                max_gamma_idx = np.argmax(np.abs(gamma_exposure))
                max_gamma_strike = strikes[max_gamma_idx]
                ax.axvline(x=max_gamma_strike, color='orange', linestyle=':', linewidth=2,
                          label=f'Max Gamma: ${max_gamma_strike:.0f}')

                ax.set_xlabel('Strike Price', fontsize=12)
                ax.set_ylabel('Net Dealer Gamma Exposure', fontsize=12)
                ax.set_title('Gamma Wall Analysis - Net Dealer Positioning', fontsize=16, fontweight='bold')
                ax.legend()
                ax.grid(True, alpha=0.3)

                # Add explanatory text
                ax.text(0.02, 0.98, 'Green: Positive Gamma (Support)\nRed: Negative Gamma (Resistance)',
                       transform=ax.transAxes, verticalalignment='top', fontsize=10,
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            else:
                ax.text(0.5, 0.5, "Insufficient option data for Gamma Wall analysis",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title("Gamma Wall Analysis", fontsize=16, fontweight='bold')

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"Gamma wall plot saved to {save_path}")
                plt.close(fig)
                return save_path
            else:
                plt.show()
                return None

        except Exception as e:
            print(f"Error creating gamma wall plot: {e}")
            return None

    def plot_vrp_trend(self, volatility_data: pd.DataFrame, ticker: str, 
                      save_path: Optional[str] = None) -> Optional[str]:
        """Plot VRP trend chart."""
        if volatility_data.empty or "vrp_20" not in volatility_data.columns:
            print("No VRP data available for plotting.")
            return None

        fig, ax = plt.subplots(figsize=(12, 8))
        
        dates = volatility_data.index
        vrp_20 = volatility_data["vrp_20"]
        
        ax.plot(dates, vrp_20, color='purple', linewidth=2, label='20-Day VRP')
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5, label='Zero Line')
        ax.axhline(y=vrp_20.mean(), color='red', linestyle=':', alpha=0.7, 
                  label=f'Average VRP ({vrp_20.mean():.1%})')
        
        # Fill areas
        ax.fill_between(dates, vrp_20, 0, where=(vrp_20 > 0), alpha=0.3, color='red', 
                       label='IV > HV (Premium)')
        ax.fill_between(dates, vrp_20, 0, where=(vrp_20 < 0), alpha=0.3, color='green', 
                       label='HV > IV (Discount)')
        
        ax.set_title(f'{ticker} - Volatility Risk Premium (20-Day) Trend', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('VRP (IV - HV)', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # Format axes
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"VRP trend chart saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving VRP trend chart: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

    def plot_volatility_comparison(self, volatility_data: pd.DataFrame, ticker: str, 
                                 save_path: Optional[str] = None) -> Optional[str]:
        """Plot historical vs implied volatility comparison."""
        if volatility_data.empty or "hv_20" not in volatility_data.columns:
            print("No volatility data available for plotting.")
            return None

        fig, ax = plt.subplots(figsize=(12, 8))
        
        dates = volatility_data.index
        hv_20 = volatility_data["hv_20"]
        iv = volatility_data.get("implied_vol", pd.Series([0.2] * len(dates), index=dates))
        
        # Plot volatilities
        ax.plot(dates, hv_20, color='blue', linewidth=2, label='20-Day Historical Volatility')
        ax.plot(dates, iv, color='orange', linewidth=2, label='Implied Volatility', linestyle='--')
        
        # Fill areas
        ax.fill_between(dates, hv_20, iv, where=(iv > hv_20), alpha=0.3, color='red', 
                       label='IV Premium (IV > HV)')
        ax.fill_between(dates, hv_20, iv, where=(hv_20 > iv), alpha=0.3, color='green',
                       label='IV Discount (HV > IV)')
        
        # Add average lines
        ax.axhline(y=hv_20.mean(), color='blue', linestyle=':', alpha=0.7, 
                  label=f'Avg HV ({hv_20.mean():.1%})')
        ax.axhline(y=iv.mean(), color='orange', linestyle=':', alpha=0.7,
                  label=f'Avg IV ({iv.mean():.1%})')
        
        ax.set_title(f'{ticker} - Historical vs Implied Volatility', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Volatility', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # Format axes
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"Volatility comparison chart saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving volatility comparison chart: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None 