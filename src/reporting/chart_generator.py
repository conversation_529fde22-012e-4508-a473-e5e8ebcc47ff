"""
Chart Generator
Handles all chart and visualization generation.
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, Any, Optional, List
from datetime import datetime


class ChartGenerator:
    """Generates charts and visualizations for analysis results."""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def plot_liquidity_indicators(self, liquidity_df: pd.DataFrame, save_path: Optional[str] = None) -> Optional[str]:
        """Plot key liquidity indicators."""
        if liquidity_df.empty:
            print("Cannot plot liquidity indicators due to missing data.")
            return None

        fig, axs = plt.subplots(3, 1, figsize=(12, 15), sharex=True)
        fig.suptitle("Money Market Liquidity Indicators", fontsize=16)

        # Plot Fed Net Liquidity
        if "fed_net_liquidity" in liquidity_df.columns and not liquidity_df["fed_net_liquidity"].isnull().all():
            axs[0].plot(liquidity_df.index, liquidity_df["fed_net_liquidity"], 
                       color="blue", linewidth=2, label="Fed Net Liquidity")
            axs[0].axhline(y=0, color="r", linestyle="--", alpha=0.5)
            axs[0].set_title("Fed Net Liquidity")
            axs[0].set_ylabel("$ Billions (Scaled)")
            axs[0].grid(True)
            axs[0].legend()
        else:
            axs[0].text(0.5, 0.5, "Fed Net Liquidity Data Missing", 
                       horizontalalignment="center", verticalalignment="center", 
                       transform=axs[0].transAxes)
            axs[0].set_title("Fed Net Liquidity (Data Missing)")

        # Plot TGA Balance
        if "tga_balance" in liquidity_df.columns and not liquidity_df["tga_balance"].isnull().all():
            axs[1].plot(liquidity_df.index, liquidity_df["tga_balance"], 
                       color="green", linewidth=2, label="Treasury General Account")
            axs[1].set_title("Treasury General Account Balance")
            axs[1].set_ylabel("$ Billions")
            axs[1].grid(True)
            axs[1].legend()
        else:
            axs[1].text(0.5, 0.5, "TGA Data Missing", 
                       horizontalalignment="center", verticalalignment="center", 
                       transform=axs[1].transAxes)
            axs[1].set_title("Treasury General Account Balance (Data Missing)")

        # Plot RRP Usage
        if "rrp_amount" in liquidity_df.columns and not liquidity_df["rrp_amount"].isnull().all():
            axs[2].plot(liquidity_df.index, liquidity_df["rrp_amount"], 
                       color="purple", linewidth=2, label="Reverse Repo Facility Usage")
            axs[2].set_title("Reverse Repo Facility Usage")
            axs[2].set_ylabel("$ Billions")
            axs[2].grid(True)
            axs[2].legend()
        else:
            axs[2].text(0.5, 0.5, "RRP Data Missing", 
                       horizontalalignment="center", verticalalignment="center", 
                       transform=axs[2].transAxes)
            axs[2].set_title("Reverse Repo Facility Usage (Data Missing)")

        # Format x-axis
        axs[2].xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
        plt.xticks(rotation=45)
        plt.tight_layout(rect=[0, 0.03, 1, 0.97])

        if save_path:
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"Liquidity plot saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving liquidity plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

    def plot_strike_levels(self, option_chain: Dict[str, Any], strike_levels: Dict[str, Any], 
                          save_path: Optional[str] = None) -> Optional[str]:
        """Plot option strike levels and walls."""
        if not option_chain or not strike_levels:
            print("Cannot plot strike levels due to missing data.")
            return None

        calls_df = pd.DataFrame(option_chain.get("calls", []))
        puts_df = pd.DataFrame(option_chain.get("puts", []))
        current_price = strike_levels.get("current_price")
        
        if calls_df.empty or puts_df.empty or not current_price:
            print("Insufficient data for strike level plot.")
            return None

        # Filter data
        price_buffer = 250
        max_call_strike = current_price + price_buffer
        min_put_strike = current_price - price_buffer
        
        calls_filtered = calls_df[
            (calls_df["strike"] >= current_price) & 
            (calls_df["strike"] <= max_call_strike)
        ]
        puts_filtered = puts_df[
            (puts_df["strike"] <= current_price) & 
            (puts_df["strike"] >= min_put_strike)
        ]

        if calls_filtered.empty and puts_filtered.empty:
            print("No relevant option data for plotting.")
            return None

        # Create figure
        fig = plt.figure(figsize=(18, 14))
        ax1 = plt.subplot2grid((10, 2), (0, 0), rowspan=6)  # Calls
        ax2 = plt.subplot2grid((10, 2), (0, 1), rowspan=6)  # Puts

        # Calculate bar width
        all_strikes = pd.concat([calls_filtered["strike"], puts_filtered["strike"]]).sort_values().unique()
        strike_width = np.median(np.diff(all_strikes)) * 0.4 if len(all_strikes) > 1 else 10

        # Get wall data
        call_walls = strike_levels.get("call_walls", [])
        put_walls = strike_levels.get("put_walls", [])
        data_source = strike_levels.get("data_source", "Unknown")

        # Plot calls
        if not calls_filtered.empty:
            ax1.bar(calls_filtered["strike"], calls_filtered["openInterest"], 
                   width=strike_width, color="green", alpha=0.7, label=f"Call OI ({data_source})")

        ax1.axvline(x=current_price, color="blue", linestyle="--", linewidth=2, 
                   label=f"Current Price: {current_price:.2f}")

        # Plot call walls
        for i, wall in enumerate(call_walls):
            strike_val = wall["strike"]
            oi_val = wall["openInterest"]
            if current_price <= strike_val <= max_call_strike:
                ax1.axvline(x=strike_val, color="darkgreen", linestyle="-", linewidth=4, alpha=0.9, 
                          label=f"Call Wall {i+1}: {strike_val:.0f}")

        ax1.set_xlim(current_price, max_call_strike)
        ax1.set_title(f"CALL OPTIONS - Resistance Levels\n{option_chain.get('option_ticker', 'Unknown')} ({data_source})", 
                     fontsize=14, fontweight='bold', color='darkgreen')
        ax1.set_xlabel("Strike Price", fontsize=12)
        ax1.set_ylabel("Open Interest", fontsize=12)
        ax1.legend(loc="upper left", fontsize=9)
        ax1.grid(True, alpha=0.3)

        # Plot puts
        if not puts_filtered.empty:
            ax2.bar(puts_filtered["strike"], puts_filtered["openInterest"], 
                   width=strike_width, color="red", alpha=0.7, label=f"Put OI ({data_source})")

        ax2.axvline(x=current_price, color="blue", linestyle="--", linewidth=2, 
                   label=f"Current Price: {current_price:.2f}")

        # Plot put walls
        for i, wall in enumerate(put_walls):
            strike_val = wall["strike"]
            oi_val = wall["openInterest"]
            if min_put_strike <= strike_val <= current_price:
                ax2.axvline(x=strike_val, color="darkred", linestyle="-", linewidth=4, alpha=0.9, 
                          label=f"Put Wall {i+1}: {strike_val:.0f}")

        ax2.set_xlim(min_put_strike, current_price)
        ax2.set_title(f"PUT OPTIONS - Support Levels\n{option_chain.get('option_ticker', 'Unknown')} ({data_source})", 
                     fontsize=14, fontweight='bold', color='darkred')
        ax2.set_xlabel("Strike Price", fontsize=12)
        ax2.set_ylabel("Open Interest", fontsize=12)
        ax2.legend(loc="upper left", fontsize=9)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            try:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"Strike level plot saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving strike level plot: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

    def plot_gamma_wall(self, option_chain: Dict[str, Any], strike_levels: Dict[str, Any],
                       save_path: Optional[str] = None) -> Optional[str]:
        """Plot gamma wall analysis with actual option data."""
        if not option_chain:
            print("Cannot plot gamma wall due to missing option chain data.")
            return None

        try:
            fig, ax = plt.subplots(figsize=(12, 8))

            calls = option_chain.get('calls', [])
            puts = option_chain.get('puts', [])
            current_price = option_chain.get('current_price', 0)

            if not calls and not puts:
                ax.text(0.5, 0.5, "No option data available for Gamma Wall analysis",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title("Gamma Wall Analysis", fontsize=16, fontweight='bold')
                if save_path:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    plt.close(fig)
                    return save_path
                return None

            # Calculate realistic gamma exposure by strike
            strikes = []
            gamma_exposure = []
            call_gamma = []
            put_gamma = []

            # Process calls (positive gamma for dealers when they're short)
            for call in calls:
                strike = call.get('strike', 0)
                open_interest = call.get('open_interest', 0)
                volume = call.get('volume', 0)

                if strike > 0 and (open_interest > 0 or volume > 0):
                    moneyness = strike / current_price if current_price > 0 else 1

                    # More realistic gamma calculation - peaks at ATM, falls off for OTM/ITM
                    if 0.85 <= moneyness <= 1.15:
                        # Gamma profile using Black-Scholes approximation
                        distance_from_atm = abs(moneyness - 1.0)
                        gamma_multiplier = np.exp(-25 * distance_from_atm**2)  # Sharp peak at ATM

                        # Scale by open interest (assume dealers are net short calls)
                        base_exposure = open_interest * 100  # Contract multiplier
                        gamma = gamma_multiplier * base_exposure * 0.01

                        strikes.append(strike)
                        gamma_exposure.append(gamma)  # Positive = dealers need to buy when price rises
                        call_gamma.append(gamma)
                        put_gamma.append(0)

            # Process puts (negative gamma for dealers when they're short)
            for put in puts:
                strike = put.get('strike', 0)
                open_interest = put.get('open_interest', 0)
                volume = put.get('volume', 0)

                if strike > 0 and (open_interest > 0 or volume > 0):
                    moneyness = strike / current_price if current_price > 0 else 1

                    if 0.85 <= moneyness <= 1.15:
                        distance_from_atm = abs(moneyness - 1.0)
                        gamma_multiplier = np.exp(-25 * distance_from_atm**2)

                        # Scale by open interest (assume dealers are net short puts)
                        base_exposure = open_interest * 100
                        gamma = -gamma_multiplier * base_exposure * 0.01  # Negative for puts

                        # Check if this strike already exists from calls
                        if strike in strikes:
                            idx = strikes.index(strike)
                            gamma_exposure[idx] += gamma  # Net gamma at this strike
                            put_gamma[idx] = gamma
                        else:
                            strikes.append(strike)
                            gamma_exposure.append(gamma)
                            call_gamma.append(0)
                            put_gamma.append(gamma)

            if strikes:
                # Sort by strike for better visualization
                sorted_data = sorted(zip(strikes, gamma_exposure, call_gamma, put_gamma))
                strikes, gamma_exposure, call_gamma, put_gamma = zip(*sorted_data)

                # Create stacked bar chart to show call vs put gamma
                width = min(25, (max(strikes) - min(strikes)) / len(strikes) * 0.8)

                # Plot call gamma (positive)
                call_bars = ax.bar(strikes, call_gamma, alpha=0.8, color='green', width=width,
                                  label='Call Gamma (Support)')

                # Plot put gamma (negative)
                put_bars = ax.bar(strikes, put_gamma, alpha=0.8, color='red', width=width,
                                 label='Put Gamma (Resistance)')

                # Add current price line
                ax.axvline(x=current_price, color='blue', linestyle='--', linewidth=3,
                          label=f'Current Price: ${current_price:.0f}')

                # Find and mark significant gamma levels
                max_pos_gamma_idx = np.argmax(call_gamma) if any(g > 0 for g in call_gamma) else 0
                max_neg_gamma_idx = np.argmin(put_gamma) if any(g < 0 for g in put_gamma) else 0

                if call_gamma[max_pos_gamma_idx] > 0:
                    max_pos_strike = strikes[max_pos_gamma_idx]
                    ax.axvline(x=max_pos_strike, color='darkgreen', linestyle=':', linewidth=2,
                              label=f'Max Support: ${max_pos_strike:.0f}')

                if put_gamma[max_neg_gamma_idx] < 0:
                    max_neg_strike = strikes[max_neg_gamma_idx]
                    ax.axvline(x=max_neg_strike, color='darkred', linestyle=':', linewidth=2,
                              label=f'Max Resistance: ${max_neg_strike:.0f}')

                # Calculate and show net gamma
                net_gamma = sum(gamma_exposure)
                net_gamma_text = f"Net Gamma: {net_gamma:,.0f}"

                ax.set_xlabel('Strike Price ($)', fontsize=12)
                ax.set_ylabel('Dealer Gamma Exposure', fontsize=12)
                ax.set_title(f'Gamma Wall Analysis - {net_gamma_text}', fontsize=16, fontweight='bold')
                ax.legend(loc='upper right')
                ax.grid(True, alpha=0.3)

                # Add explanatory text with market implications
                gamma_regime = "Positive Gamma Regime" if net_gamma > 0 else "Negative Gamma Regime"
                market_impact = "Stabilizing (dealers buy dips, sell rallies)" if net_gamma > 0 else "Destabilizing (dealers sell dips, buy rallies)"

                ax.text(0.02, 0.98, f'{gamma_regime}\n{market_impact}\n\nGreen: Call Support Levels\nRed: Put Resistance Levels',
                       transform=ax.transAxes, verticalalignment='top', fontsize=10,
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.9))

                # Add value labels on significant bars
                for i, (strike, call_g, put_g) in enumerate(zip(strikes, call_gamma, put_gamma)):
                    if abs(call_g) > max(call_gamma) * 0.5:  # Label significant call gamma
                        ax.text(strike, call_g + max(call_gamma) * 0.02, f'{call_g:.0f}',
                               ha='center', va='bottom', fontsize=8, fontweight='bold')
                    if abs(put_g) > abs(min(put_gamma)) * 0.5:  # Label significant put gamma
                        ax.text(strike, put_g - abs(min(put_gamma)) * 0.02, f'{put_g:.0f}',
                               ha='center', va='top', fontsize=8, fontweight='bold')
            else:
                ax.text(0.5, 0.5, "Insufficient option data for Gamma Wall analysis",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)
                ax.set_title("Gamma Wall Analysis", fontsize=16, fontweight='bold')

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"Gamma wall plot saved to {save_path}")
                plt.close(fig)
                return save_path
            else:
                plt.show()
                return None

        except Exception as e:
            print(f"Error creating gamma wall plot: {e}")
            return None

    def plot_vrp_trend(self, volatility_data: pd.DataFrame, ticker: str, 
                      save_path: Optional[str] = None) -> Optional[str]:
        """Plot VRP trend chart."""
        if volatility_data.empty or "vrp_20" not in volatility_data.columns:
            print("No VRP data available for plotting.")
            return None

        fig, ax = plt.subplots(figsize=(12, 8))
        
        dates = volatility_data.index
        vrp_20 = volatility_data["vrp_20"]
        
        ax.plot(dates, vrp_20, color='purple', linewidth=2, label='20-Day VRP')
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5, label='Zero Line')
        ax.axhline(y=vrp_20.mean(), color='red', linestyle=':', alpha=0.7, 
                  label=f'Average VRP ({vrp_20.mean():.1%})')
        
        # Fill areas
        ax.fill_between(dates, vrp_20, 0, where=(vrp_20 > 0), alpha=0.3, color='red', 
                       label='IV > HV (Premium)')
        ax.fill_between(dates, vrp_20, 0, where=(vrp_20 < 0), alpha=0.3, color='green', 
                       label='HV > IV (Discount)')
        
        ax.set_title(f'{ticker} - Volatility Risk Premium (20-Day) Trend', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('VRP (IV - HV)', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # Format axes
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"VRP trend chart saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving VRP trend chart: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None

    def plot_volatility_comparison(self, volatility_data: pd.DataFrame, ticker: str, 
                                 save_path: Optional[str] = None) -> Optional[str]:
        """Plot historical vs implied volatility comparison."""
        if volatility_data.empty or "hv_20" not in volatility_data.columns:
            print("No volatility data available for plotting.")
            return None

        fig, ax = plt.subplots(figsize=(12, 8))
        
        dates = volatility_data.index
        hv_20 = volatility_data["hv_20"]
        iv = volatility_data.get("implied_vol", pd.Series([0.2] * len(dates), index=dates))
        
        # Plot volatilities
        ax.plot(dates, hv_20, color='blue', linewidth=2, label='20-Day Historical Volatility')
        ax.plot(dates, iv, color='orange', linewidth=2, label='Implied Volatility', linestyle='--')
        
        # Fill areas
        ax.fill_between(dates, hv_20, iv, where=(iv > hv_20), alpha=0.3, color='red', 
                       label='IV Premium (IV > HV)')
        ax.fill_between(dates, hv_20, iv, where=(hv_20 > iv), alpha=0.3, color='green',
                       label='IV Discount (HV > IV)')
        
        # Add average lines
        ax.axhline(y=hv_20.mean(), color='blue', linestyle=':', alpha=0.7, 
                  label=f'Avg HV ({hv_20.mean():.1%})')
        ax.axhline(y=iv.mean(), color='orange', linestyle=':', alpha=0.7,
                  label=f'Avg IV ({iv.mean():.1%})')
        
        ax.set_title(f'{ticker} - Historical vs Implied Volatility', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Volatility', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # Format axes
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"Volatility comparison chart saved to {save_path}")
                plt.close(fig)
                return save_path
            except Exception as e:
                print(f"Error saving volatility comparison chart: {e}")
                plt.close(fig)
                return None
        else:
            plt.show()
            return None 