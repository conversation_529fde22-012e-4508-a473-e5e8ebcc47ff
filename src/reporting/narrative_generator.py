"""
Narrative Generator
Sends checklist data to ChatGPT for narrative analysis and trading insights.
"""

import os
import pandas as pd
import requests
import json
from typing import Dict, Any, Optional
from datetime import datetime


class NarrativeGenerator:
    """Generates narrative analysis using ChatGPT API."""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.base_url = "https://api.openai.com/v1/chat/completions"
        
        if not self.api_key:
            print("⚠️ OpenAI API key not found. Set OPENAI_API_KEY in .env file for narrative generation.")
    
    def generate_narrative(self, option_signals_analysis: Dict[str, Any], 
                          money_market_analysis: Dict[str, Any] = None) -> str:
        """Generate narrative analysis from option signals and liquidity data."""
        
        if not self.api_key:
            return self._generate_fallback_narrative(option_signals_analysis, money_market_analysis)
        
        try:
            # Extract key metrics
            opt_signal = option_signals_analysis.get("signal", "UNKNOWN")
            opt_confidence = option_signals_analysis.get("confidence", "UNKNOWN")
            net_gamma = option_signals_analysis.get("net_gamma", 0)
            net_vega = option_signals_analysis.get("net_vega", 0)
            gamma_trend = option_signals_analysis.get("gamma_trend", "UNKNOWN")
            vega_trend = option_signals_analysis.get("vega_trend", "UNKNOWN")
            current_price = option_signals_analysis.get("current_price", 0)
            
            # Get strike levels if available
            strike_levels = option_signals_analysis.get("strike_levels", {})
            call_walls = strike_levels.get("call_walls", [])
            put_walls = strike_levels.get("put_walls", [])
            
            # Extract liquidity data if available
            liquidity_info = ""
            if money_market_analysis:
                liquidity_condition = money_market_analysis.get("liquidity_condition", "UNKNOWN")
                liquidity_trend = money_market_analysis.get("liquidity_trend", "UNKNOWN")
                bias = money_market_analysis.get("bias", "UNKNOWN")
                confidence = money_market_analysis.get("confidence", "UNKNOWN")
                liquidity_info = f"""
LIQUIDITY CONDITIONS:
- Market Bias: {bias} ({confidence} confidence)
- Liquidity Condition: {liquidity_condition}
- Liquidity Trend: {liquidity_trend}
"""
            
            # Create the prompt in the style the user wants
            prompt = f"""
You are a professional options trader who has just completed a comprehensive analysis of gamma and vanna exposure charts and their relationship to SPX price action. 

MARKET DATA:
- Current SPX Price: {current_price:,.2f}
- Net Gamma: {net_gamma:,.0f}
- Net Vega: {net_vega:,.0f}
- Gamma Trend: {gamma_trend}
- Vega Trend: {vega_trend}
- Option Signal: {opt_signal} (Confidence: {opt_confidence})
{liquidity_info}
STRIKE LEVELS:
Call Walls (Resistance):
{self._format_strike_levels(call_walls)}

Put Walls (Support):
{self._format_strike_levels(put_walls)}

Please provide your analysis in this EXACT format and style:

"I've completed a comprehensive analysis of the gamma and vanna exposure charts and their relationship to SPX price action. The analysis reveals [describe the market environment] with specific support and resistance levels driven by options positioning.

Key findings:

• [Finding about market maker positioning and gamma exposure]
• [Finding about support/resistance levels from put/call walls]  
• [Finding about current price position relative to gamma concentration]
• [Finding about most likely scenario with probability estimate]

The detailed analysis includes explanations of gamma/vanna mechanics, specific price levels, and trading implications."

Make sure to:
1. Start with "I've completed a comprehensive analysis..."
2. Use bullet points for key findings
3. Include specific price levels for support and resistance
4. Provide probability estimates
5. Keep it concise but informative
6. Focus on actionable insights
"""

            # Make API call to ChatGPT
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Try GPT-4-turbo first, fall back to GPT-3.5-turbo if needed
            models_to_try = ["gpt-4-turbo", "gpt-3.5-turbo"]
            
            for model in models_to_try:
                data = {
                    "model": model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a professional options trader with expertise in gamma/vanna analysis. Provide analysis in the exact format requested, starting with 'I've completed a comprehensive analysis...' and using bullet points for key findings."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 1000,
                    "temperature": 0.7
                }
                
                response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    narrative = result['choices'][0]['message']['content']
                    print(f"✅ Narrative generated successfully using {model}")
                    return narrative
                elif response.status_code == 403 and "does not have access to model" in response.text:
                    print(f"⚠️ No access to {model}, trying next model...")
                    continue
                else:
                    print(f"❌ ChatGPT API error with {model}: {response.status_code}")
                    break
            
            print("❌ All models failed, using fallback narrative")
            return self._generate_fallback_narrative(option_signals_analysis, money_market_analysis)
                
        except Exception as e:
            print(f"❌ Error generating narrative: {e}")
            return self._generate_fallback_narrative(option_signals_analysis, money_market_analysis)
    
    def _format_strike_levels(self, levels: list) -> str:
        """Format strike levels for the prompt."""
        if not levels:
            return "No significant levels identified"
            
        formatted = []
        for level in levels:
            strike = level.get("strike", 0)
            gamma = level.get("gamma", 0)
            vega = level.get("vega", 0)
            formatted.append(f"- Strike: {strike:,.0f}, Gamma: {gamma:,.0f}, Vega: {vega:,.0f}")
            
        return "\n".join(formatted)
    
    def _generate_fallback_narrative(self, option_signals_analysis: Dict[str, Any], 
                                   money_market_analysis: Dict[str, Any] = None) -> str:
        """Generate a fallback narrative when ChatGPT is not available."""
        
        # Extract key data
        opt_signal = option_signals_analysis.get("signal", "UNKNOWN")
        opt_confidence = option_signals_analysis.get("confidence", "UNKNOWN")
        net_gamma = option_signals_analysis.get("net_gamma", 0)
        net_vega = option_signals_analysis.get("net_vega", 0)
        gamma_trend = option_signals_analysis.get("gamma_trend", "UNKNOWN")
        vega_trend = option_signals_analysis.get("vega_trend", "UNKNOWN")
        current_price = option_signals_analysis.get("current_price", 0)
        
        # Get liquidity info if available
        liquidity_desc = ""
        if money_market_analysis:
            bias = money_market_analysis.get("bias", "UNKNOWN")
            liquidity_condition = money_market_analysis.get("liquidity_condition", "UNKNOWN")
            liquidity_desc = f" with {bias.lower()} liquidity conditions ({liquidity_condition.lower()})"
        
        narrative = f"""I've completed a comprehensive analysis of the gamma and vanna exposure charts and their relationship to SPX price action. The analysis reveals a {'bullish' if net_gamma > 0 else 'bearish' if net_gamma < 0 else 'neutral'} market environment{liquidity_desc} with specific support and resistance levels driven by options positioning.

Key findings:

• Market makers are {'net long' if net_gamma > 0 else 'net short' if net_gamma < 0 else 'balanced on'} gamma ({net_gamma:,.0f}), {'creating a volatility-dampening environment' if net_gamma > 0 else 'potentially amplifying volatility' if net_gamma < 0 else 'suggesting stable conditions'}
• Current price ({current_price:,.2f}) {'sits near peak gamma concentration' if abs(net_gamma) > 1000 else 'shows moderate gamma exposure'}, {'favoring range-bound trading' if net_gamma > 0 else 'allowing for larger moves' if net_gamma < 0 else 'suggesting neutral conditions'}
• {'Volatility support' if net_vega > 0 else 'Volatility resistance' if net_vega < 0 else 'Neutral volatility'} from vanna positioning ({net_vega:,.0f})
• Most likely scenario is {'continued range-bound trading' if net_gamma > 0 else 'increased volatility potential' if net_gamma < 0 else 'stable market conditions'} with {'60%' if abs(net_gamma) > 1000 else '50%'} probability

The detailed analysis includes explanations of gamma/vanna mechanics, specific price levels, and trading implications.

*Note: This analysis is generated automatically. ChatGPT API integration available with OPENAI_API_KEY.*"""
        
        return narrative.strip()
    
    def save_narrative(self, narrative: str, output_dir: str = "output") -> str:
        """Save narrative to file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"trading_narrative_{timestamp}.md"
            filepath = f"{output_dir}/{filename}"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(narrative)
            
            print(f"📝 Trading narrative saved to: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Error saving narrative: {e}")
            return "" 