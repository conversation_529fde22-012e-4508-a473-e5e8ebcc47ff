"""
Summary Table Generator
Creates comprehensive summary tables for market analysis.
"""

import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional


class SummaryTableGenerator:
    """Generates comprehensive summary tables for market analysis."""
    
    def __init__(self):
        pass
    
    def generate_comprehensive_summary(self, 
                                     money_market_analysis: Dict[str, Any],
                                     option_signals_analysis: Dict[str, Any],
                                     volatility_metrics: Dict[str, Any],
                                     additional_data: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Generate a comprehensive market analysis summary table."""
        
        # Helper function to safely get values
        def safe_get(data: dict, key: str, default: str = "N/A") -> str:
            value = data.get(key, default)
            if value is None or (isinstance(value, float) and pd.isna(value)):
                return default
            return str(value)
        
        # Helper function to format percentages
        def format_pct(value, default="N/A"):
            if value is None or (isinstance(value, float) and pd.isna(value)):
                return default
            try:
                return f"{float(value):.1%}"
            except:
                return default
        
        # Helper function to format currency
        def format_currency(value, default="N/A"):
            if value is None or (isinstance(value, float) and pd.isna(value)):
                return default
            try:
                return f"${float(value):,.0f}B"
            except:
                return default
        
        # Extract data
        mm = money_market_analysis
        opt = option_signals_analysis
        vol = volatility_metrics
        
        # Create comprehensive summary data
        summary_data = []
        
        # Market Overview Section
        summary_data.extend([
            ["MARKET OVERVIEW", "", ""],
            ["Analysis Date", datetime.now().strftime("%Y-%m-%d %H:%M"), ""],
            ["Market Bias", safe_get(mm, "bias", "UNKNOWN"), safe_get(mm, "confidence", "UNKNOWN")],
            ["Option Signal", safe_get(opt, "signal", "UNKNOWN"), safe_get(opt, "confidence", "UNKNOWN")],
            ["", "", ""],
        ])
        
        # Liquidity Conditions Section
        summary_data.extend([
            ["LIQUIDITY CONDITIONS", "", ""],
            ["Fed Net Liquidity", format_currency(mm.get("current_fed_net_liquidity")), ""],
            ["Liquidity Condition", safe_get(mm, "liquidity_condition"), ""],
            ["Liquidity Trend", safe_get(mm, "liquidity_trend"), ""],
            ["TGA Impact", safe_get(mm, "tga_impact"), ""],
            ["RRP Impact", safe_get(mm, "rrp_impact"), ""],
            ["Treasury Auctions", safe_get(mm, "treasury_auction_assessment"), ""],
            ["", "", ""],
        ])
        
        # Interest Rates Section
        if additional_data:
            summary_data.extend([
                ["INTEREST RATES", "", ""],
                ["2Y Treasury", format_pct(additional_data.get('treasury_2y').iloc[-1] if hasattr(additional_data.get('treasury_2y', pd.Series()), 'iloc') and len(additional_data.get('treasury_2y', pd.Series())) > 0 else None), ""],
                ["5Y Treasury", format_pct(additional_data.get('treasury_5y').iloc[-1] if hasattr(additional_data.get('treasury_5y', pd.Series()), 'iloc') and len(additional_data.get('treasury_5y', pd.Series())) > 0 else None), ""],
                ["10Y Treasury", format_pct(additional_data.get('treasury_10y').iloc[-1] if hasattr(additional_data.get('treasury_10y', pd.Series()), 'iloc') and len(additional_data.get('treasury_10y', pd.Series())) > 0 else None), ""],
                ["30Y Treasury", format_pct(additional_data.get('treasury_30y').iloc[-1] if hasattr(additional_data.get('treasury_30y', pd.Series()), 'iloc') and len(additional_data.get('treasury_30y', pd.Series())) > 0 else None), ""],
                ["Fed Funds Rate", format_pct(additional_data.get('fed_funds_rate').iloc[-1] if hasattr(additional_data.get('fed_funds_rate', pd.Series()), 'iloc') and len(additional_data.get('fed_funds_rate', pd.Series())) > 0 else None), ""],
                ["SOFR Rate", format_pct(additional_data.get('sofr_rate').iloc[-1] if hasattr(additional_data.get('sofr_rate', pd.Series()), 'iloc') and len(additional_data.get('sofr_rate', pd.Series())) > 0 else None), ""],
                ["", "", ""],
            ])
        
        # Volatility Analysis Section
        summary_data.extend([
            ["VOLATILITY ANALYSIS", "", ""],
            ["Current IV", format_pct(vol.get("current_iv")), ""],
            ["10-Day HV", format_pct(vol.get("current_hv_10")), ""],
            ["20-Day HV", format_pct(vol.get("current_hv_20")), ""],
            ["30-Day HV", format_pct(vol.get("current_hv_30")), ""],
            ["60-Day HV", format_pct(vol.get("current_hv_60")), ""],
            ["", "", ""],
        ])
        
        # VRP Analysis Section
        summary_data.extend([
            ["VRP ANALYSIS", "", ""],
            ["10-Day VRP", format_pct(vol.get("current_vrp_10")), ""],
            ["20-Day VRP", format_pct(vol.get("current_vrp_20")), ""],
            ["30-Day VRP", format_pct(vol.get("current_vrp_30")), ""],
            ["60-Day VRP", format_pct(vol.get("current_vrp_60")), ""],
            ["VRP Percentile", f"{vol.get('vrp_percentile', 0):.0f}th" if vol.get('vrp_percentile') is not None else "N/A", ""],
            ["", "", ""],
        ])
        
        # Option Greeks Section
        summary_data.extend([
            ["OPTION GREEKS", "", ""],
            ["Net Gamma", f"{opt.get('net_gamma', 0):,.0f}" if opt.get('net_gamma') is not None else "N/A", ""],
            ["Net Vega", f"{opt.get('net_vega', 0):,.0f}" if opt.get('net_vega') is not None else "N/A", ""],
            ["Gamma Trend", safe_get(opt, "gamma_trend"), ""],
            ["Vega Trend", safe_get(opt, "vega_trend"), ""],
            ["Avg Implied Vol", format_pct(opt.get("avg_implied_vol")), ""],
            ["", "", ""],
        ])
        
        # Trading Recommendations Section
        summary_data.extend([
            ["TRADING RECOMMENDATIONS", "", ""],
            ["Position Type", safe_get(opt, "position_type"), ""],
            ["Recommended Expiry", safe_get(opt, "recommended_expiry"), ""],
            ["Position Size", safe_get(opt, "position_size"), ""],
            ["Entry Timing", safe_get(opt, "entry_timing", "Monitor for optimal entry"), ""],
            ["Risk Level", safe_get(opt, "risk_level", "Medium"), ""],
            ["", "", ""],
        ])
        
        # Risk Factors Section
        summary_data.extend([
            ["RISK FACTORS", "", ""],
            ["Primary Risk", "Liquidity regime change" if mm.get("liquidity_condition") == "ABUNDANT" else "Continued liquidity stress", ""],
            ["Volatility Risk", "Vol compression risk" if vol.get("current_vrp_20", 0) > 0.05 else "Vol expansion risk" if vol.get("current_vrp_20", 0) < -0.05 else "Neutral vol environment", ""],
            ["Market Risk", "Gamma squeeze potential" if opt.get("gamma_trend") == "POSITIVE_GAMMA_WALL" else "Negative gamma drag" if opt.get("gamma_trend") == "NEGATIVE_GAMMA_WALL" else "Balanced gamma", ""],
            ["", "", ""],
        ])
        
        # Data Sources Section
        summary_data.extend([
            ["DATA SOURCES", "", ""],
            ["Option Data", safe_get(opt, "data_source", "Unknown"), ""],
            ["Price Data", "yfinance (^GSPC)", ""],
            ["Economic Data", "FRED API", ""],
            ["Treasury Data", "FiscalData API", ""],
            ["Auction Data", "Real" if "REAL" in safe_get(mm, "treasury_auction_assessment", "") else "Simulated", ""],
        ])
        
        # Create DataFrame
        df = pd.DataFrame(summary_data, columns=["Category", "Value", "Details"])
        
        return df
    
    def generate_executive_summary_table(self,
                                       money_market_analysis: Dict[str, Any],
                                       option_signals_analysis: Dict[str, Any],
                                       volatility_metrics: Dict[str, Any],
                                       options_flow_analysis: Optional[Dict[str, Any]] = None,
                                       put_call_analysis: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Generate a concise executive summary table with liquidity and treasury auction."""
        
        mm = money_market_analysis
        opt = option_signals_analysis
        vol = volatility_metrics
        flow = options_flow_analysis or {}
        pc = put_call_analysis or {}
        
        # Helper function to format percentages
        def format_pct(value):
            if value is None or pd.isna(value):
                return "N/A"
            try:
                return f"{float(value):.1%}"
            except:
                return "N/A"
        
        # Helper function to format currency
        def format_currency(value):
            if value is None or pd.isna(value):
                return "N/A"
            try:
                return f"${float(value):,.0f}B"
            except:
                return "N/A"
        
        # Create executive summary with all requested metrics
        exec_data = [
            ["Market Bias", mm.get("bias", "UNKNOWN"), mm.get("confidence", "UNKNOWN")],
            ["Option Signal", opt.get("signal", "UNKNOWN"), opt.get("confidence", "UNKNOWN")],
            ["Liquidity Condition", mm.get("liquidity_condition", "UNKNOWN"), mm.get("liquidity_trend", "UNKNOWN")],
            ["Fed Net Liquidity", format_currency(mm.get("current_fed_net_liquidity")), "Current Level"],
            ["TGA Impact", mm.get("tga_impact", "UNKNOWN"), "Treasury Account"],
            ["RRP Impact", mm.get("rrp_impact", "UNKNOWN"), "Reverse Repo"],
            ["Treasury Auction", mm.get("treasury_auction_assessment", "UNKNOWN"), "Recent Assessment"],
            ["Current IV", format_pct(vol.get("current_iv")), "Implied Volatility"],
            ["20-Day HV", format_pct(vol.get("current_hv_20")), "Historical Volatility"],
            ["20-Day VRP", format_pct(vol.get("current_vrp_20")), f"{vol.get('vrp_percentile', 0):.0f}th percentile" if vol.get('vrp_percentile') is not None else "N/A"],
            ["VRP Signal", "Elevated IV" if vol.get('current_vrp_20', 0) > 0.02 else "Depressed IV" if vol.get('current_vrp_20', 0) < -0.02 else "Neutral IV", "Options Pricing"]
        ]
        
        # Add advanced options analysis if available
        if flow and 'error' not in flow:
            flow_signals = flow.get('flow_signals', {})
            exec_data.extend([
                ["Flow Signal", flow_signals.get('primary_signal', 'UNKNOWN'), flow_signals.get('confidence', 'UNKNOWN')],
                ["Time Horizon", flow_signals.get('time_horizon', 'UNKNOWN'), "Dominant timeframe"]
            ])
        
        if pc and 'error' not in pc:
            sentiment_signals = pc.get('sentiment_signals', {})
            overall_ratios = pc.get('overall_ratios', {})
            exec_data.extend([
                ["P/C Sentiment", sentiment_signals.get('primary_sentiment', 'UNKNOWN'), sentiment_signals.get('confidence', 'UNKNOWN')],
                ["P/C Volume Ratio", f"{overall_ratios.get('pc_volume_ratio', 0):.2f}" if overall_ratios.get('pc_volume_ratio') is not None else "N/A", "Put/Call volume"],
                ["Flow Pressure", sentiment_signals.get('flow_pressure', 'UNKNOWN'), "Buying pressure"]
            ])
        
        # Add standard recommendations
        exec_data.extend([
            ["Position Type", opt.get("position_type", "N/A"), opt.get("position_size", "N/A")],
            ["Recommended Expiry", opt.get("recommended_expiry", "N/A"), "Option Timing"],
            ["Risk Assessment", "High" if mm.get("confidence") == "LOW" else "Medium" if mm.get("confidence") == "MEDIUM" else "Low", "Overall Risk Level"]
        ])
        
        df = pd.DataFrame(exec_data, columns=["Metric", "Value", "Details"])
        
        return df
    
    def save_summary_tables(self, comprehensive_table: pd.DataFrame, 
                          executive_table: pd.DataFrame, 
                          output_dir: str = "output") -> Dict[str, str]:
        """Save summary tables to files."""
        
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save comprehensive table
        comp_path = f"{output_dir}/comprehensive_summary_{timestamp}.csv"
        comprehensive_table.to_csv(comp_path, index=False)
        
        # Save executive table
        exec_path = f"{output_dir}/executive_summary_{timestamp}.csv"
        executive_table.to_csv(exec_path, index=False)
        
        return {
            "comprehensive": comp_path,
            "executive": exec_path
        }
    
    def generate_volatility_analysis_table(self, volatility_metrics: Dict[str, Any]) -> pd.DataFrame:
        """Generate the specific volatility analysis table format requested."""
        
        vol = volatility_metrics
        
        # Create volatility analysis table
        vol_data = []
        
        timeframes = [
            ("10-day", vol.get("current_hv_10"), vol.get("current_vrp_10")),
            ("20-day", vol.get("current_hv_20"), vol.get("current_vrp_20")),
            ("30-day", vol.get("current_hv_30"), vol.get("current_vrp_30")),
            ("60-day", vol.get("current_hv_60"), vol.get("current_vrp_60"))
        ]
        
        for timeframe, hv, vrp in timeframes:
            # Format historical vol
            if hv is None or pd.isna(hv):
                hv_str = "nan%"
            else:
                hv_str = f"{float(hv):.1%}"
            
            # Format VRP
            if vrp is None or pd.isna(vrp):
                vrp_str = "nan%"
            else:
                vrp_str = f"{float(vrp):.1%}"
            
            # Interpretation
            if vrp is not None and not pd.isna(vrp):
                if vrp > 0.02:  # 2%
                    interpretation = "Options expensive"
                elif vrp < -0.02:  # -2%
                    interpretation = "Options cheap"
                else:
                    interpretation = "Options fairly valued"
            else:
                interpretation = "Insufficient data"
            
            vol_data.append({
                "Timeframe": timeframe,
                "Historical Vol": hv_str,
                "VRP": vrp_str,
                "Interpretation": interpretation
            })
        
        return pd.DataFrame(vol_data)
    
    def generate_market_summary_table(self, 
                                    money_market_analysis: Dict[str, Any],
                                    option_signals_analysis: Dict[str, Any],
                                    volatility_metrics: Dict[str, Any]) -> pd.DataFrame:
        """Generate the specific market summary table format requested."""
        
        mm = money_market_analysis
        opt = option_signals_analysis
        vol = volatility_metrics
        
        # Create market summary table
        summary_data = [
            ["Market Bias", mm.get("bias", "UNKNOWN")],
            ["Confidence Level", mm.get("confidence", "UNKNOWN")],
            ["Liquidity Condition", mm.get("liquidity_condition", "UNKNOWN")],
            ["Liquidity Trend", mm.get("liquidity_trend", "UNKNOWN")],
            ["TGA Impact", mm.get("tga_impact", "UNKNOWN")],
            ["RRP Impact", mm.get("rrp_impact", "UNKNOWN")],
            ["Treasury Auction", mm.get("treasury_auction_assessment", "UNKNOWN")]
        ]
        
        df = pd.DataFrame(summary_data, columns=["Metric", "Value"])
        
        return df 