"""
Treasury FiscalData API Client
Handles Treasury General Account and auction data.
"""

import requests
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional


class FiscalDataFetcher:
    """Fetches data from Treasury FiscalData API."""
    
    BASE_URL = "https://api.fiscaldata.treasury.gov/services/api/fiscal_service/"

    def fetch_tga_balance(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Fetch Treasury General Account (TGA) balance data."""
        print("Fetching Treasury General Account (TGA) data...")
        endpoint = "v1/accounting/dts/operating_cash_balance"
        fields = "record_date,account_type,close_today_bal,open_today_bal"
        
        # Fetch much more historical data to match other Fed components (Fed assets, RRP)
        # Fed data typically goes back years, so get 2+ years of TGA data for proper liquidity analysis
        fetch_start_date = start_date - timedelta(days=730)  # Get 2 years of historical data (was 90 days)
        start_date_str = fetch_start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")
        
        print(f"   Requesting TGA data from {start_date_str} to {end_date_str} (extended historical range)")
        
        # First, try to get all account types to see what's available
        print("   Exploring available account types...")
        filters = f"record_date:gte:{start_date_str},record_date:lte:{end_date_str}"
        
        params = {
            "fields": fields,
            "filter": filters,
            "sort": "-record_date",
            "page[size]": 500  # Reduced from 1000 to 500
        }
        url = f"{self.BASE_URL}{endpoint}"

        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()["data"]

            if not data:
                print("   No data returned from TGA API with extended range, trying shorter period...")
                # Fall back to shorter period if extended range fails
                fallback_start_date = start_date - timedelta(days=365)  # Try 1 year
                fallback_start_str = fallback_start_date.strftime("%Y-%m-%d")
                fallback_filters = f"record_date:gte:{fallback_start_str},record_date:lte:{end_date_str}"
                params["filter"] = fallback_filters
                
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()["data"]
                
                if not data:
                    print("   No data returned from TGA API even with 1-year range")
                    return pd.DataFrame()
                else:
                    print(f"   Successfully fetched TGA data with 1-year fallback range")

            df = pd.DataFrame(data)
            
            # Check if we may have hit pagination limits and try to get more data
            response_json = response.json()
            total_count = response_json.get("meta", {}).get("total-count", len(data))
            if len(data) >= 500 and total_count > len(data):
                print(f"   API returned {len(data)} records, but {total_count} total available. Fetching additional pages...")
                
                # Fetch additional pages if needed
                all_data = data.copy()
                page = 2
                while len(all_data) < total_count and page <= 10:  # Limit to 10 pages max for safety
                    params["page[number]"] = page
                    try:
                        page_response = requests.get(url, params=params, timeout=30)
                        page_response.raise_for_status()
                        page_data = page_response.json()["data"]
                        if page_data:
                            all_data.extend(page_data)
                            print(f"      Fetched page {page}: {len(page_data)} additional records")
                            page += 1
                        else:
                            break
                    except Exception as e:
                        print(f"      Error fetching page {page}: {e}")
                        break
                
                data = all_data
                df = pd.DataFrame(data)
                print(f"   Total TGA records fetched: {len(df)}")
            
            # Show available account types
            unique_accounts = df["account_type"].unique()
            print(f"   Available account types: {unique_accounts}")
            
            # Specifically look for "Treasury General Account (TGA) Closing Balance"
            target_account = "Treasury General Account (TGA) Closing Balance"
            if target_account not in unique_accounts:
                print(f"   Target account '{target_account}' not found")
                print(f"   Available accounts: {list(unique_accounts)}")
                return pd.DataFrame()
            
            print(f"   Using account type: {target_account}")
            
            # Filter to target account
            df = df[df["account_type"] == target_account].copy()
            df["record_date"] = pd.to_datetime(df["record_date"])
            df.set_index("record_date", inplace=True)
            df.sort_index(inplace=True)
            
            # Convert balances from strings to numeric (in Billions) - following original implementation
            for col in ["close_today_bal", "open_today_bal"]:
                # Handle string values with commas, convert to numeric, then to billions
                # Note: API returns values in millions, so divide by 1000 to get billions
                df[col] = pd.to_numeric(df[col].astype(str).str.replace(",", "").replace("null", ""), errors="coerce") / 1_000
            
            # Check if we have any valid data - try close_today_bal first, then open_today_bal
            close_valid = df["close_today_bal"].notna().sum()
            open_valid = df["open_today_bal"].notna().sum()
            
            if close_valid == 0 and open_valid > 0:
                print(f"   Close balance data is null, using open balance data instead")
                # Use open_today_bal as close_today_bal since close is null
                df["close_today_bal"] = df["open_today_bal"]
                valid_count = open_valid
            elif close_valid > 0:
                valid_count = close_valid
            else:
                print(f"   No valid balance data for {target_account}")
                return pd.DataFrame()
            
            print(f"✓ Fetched {len(df)} TGA data points from {df.index.min().date()} to {df.index.max().date()}")
            print(f"   Valid data points: {valid_count}/{len(df)}")
            
            # Return the full extended dataset (not filtered to original range)
            # The money market analyzer needs the extended historical data for trend analysis
            return df

        except Exception as e:
            print(f"❌ Error fetching TGA data: {e}")
            return pd.DataFrame()

    def fetch_treasury_auctions(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Fetch Treasury auction data from Treasury Direct API."""
        print("Fetching Treasury Auction data from Treasury Direct...")
        
        # Use Treasury Direct API which is more reliable
        url = "http://www.treasurydirect.gov/TA_WS/securities/auctioned"
        params = {
            "format": "json",
            "day": "0"  # Get all recent auctions
        }

        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()

            if not data:
                print("   No auction data returned from Treasury Direct API, using simulated data")
                return self._simulate_auction_data(start_date, end_date)

            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            # Convert date fields and set index
            df["auctionDate"] = pd.to_datetime(df["auctionDate"])
            df.set_index("auctionDate", inplace=True)
            df.sort_index(inplace=True)
            
            # Rename fields to match our expected format
            field_mapping = {
                "securityType": "security_type",
                "securityTerm": "security_term", 
                "averageMedianYield": "high_yield",
                "bidToCoverRatio": "bid_to_cover_ratio",
                "competitiveAccepted": "competitive_accepted",
                "offeringAmount": "issue_amount"
            }
            
            for old_name, new_name in field_mapping.items():
                if old_name in df.columns:
                    df[new_name] = df[old_name]
            
            # Convert numeric fields (handling string values with commas)
            numeric_fields = ["high_yield", "bid_to_cover_ratio", "competitive_accepted", "issue_amount"]
            for field in numeric_fields:
                if field in df.columns:
                    # Handle string values, remove commas, convert to numeric
                    df[field] = pd.to_numeric(df[field].astype(str).str.replace(",", ""), errors="coerce")
                    
                    # Convert large amounts from raw values to billions for issue_amount and competitive_accepted
                    if field in ["issue_amount", "competitive_accepted"]:
                        df[field] = df[field] / 1_000_000_000  # Convert to billions
            
            # Filter to requested date range
            df_filtered = df[start_date:end_date]
            
            # If no data in range, get the most recent auctions
            if df_filtered.empty and not df.empty:
                print(f"   No auctions in date range, using {min(10, len(df))} most recent auctions")
                df_filtered = df.tail(10)
            
            print(f"✓ Fetched {len(df_filtered)} Treasury auction records from Treasury Direct API")
            if not df_filtered.empty:
                print(f"   Date range: {df_filtered.index.min().date()} to {df_filtered.index.max().date()}")
            
            return df_filtered

        except Exception as e:
            print(f"❌ Error fetching auction data from Treasury Direct: {e}")
            print("   Falling back to simulated auction data")
            return self._simulate_auction_data(start_date, end_date)
    
    def _simulate_auction_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Simulate auction data as fallback."""
        auctions = []
        current_date = start_date
        
        while current_date <= end_date:
            # Simulate 2-3 auctions per week
            if np.random.random() < 0.4:  # 40% chance of auction on any given day
                auction = {
                    "record_date": current_date,
                    "security_type": np.random.choice(["Bill", "Note", "Bond"]),
                    "security_term": np.random.choice(["4-Week", "8-Week", "13-Week", "26-Week", "2-Year", "5-Year", "10-Year", "30-Year"]),
                    "high_yield": np.random.uniform(3.5, 5.5),
                    "bid_to_cover_ratio": np.random.uniform(2.0, 3.5),
                    "competitive_accepted": np.random.uniform(60, 90),
                    "issue_amount": np.random.uniform(20, 60)  # Billions
                }
                auctions.append(auction)
            
            current_date += timedelta(days=1)
        
        if auctions:
            df = pd.DataFrame(auctions)
            df.set_index("record_date", inplace=True)
            df.sort_index(inplace=True)
            print(f"Simulated {len(df)} Treasury auction results")
            return df
        else:
            print("No auction data simulated")
            return pd.DataFrame()

    def analyze_tga_impact(self, tga_data: pd.DataFrame) -> str:
        """Analyze TGA balance changes for market impact."""
        if tga_data.empty or len(tga_data) < 2:
            return "NEUTRAL"
        
        # Calculate recent change (last 5 days vs previous 5 days)
        recent_avg = tga_data.tail(5)["close_today_bal"].mean()
        previous_avg = tga_data.iloc[-10:-5]["close_today_bal"].mean() if len(tga_data) >= 10 else recent_avg
        
        change_pct = (recent_avg - previous_avg) / previous_avg if previous_avg != 0 else 0
        
        if change_pct > 0.05:  # 5% increase
            return "TGA ADDITION - LIQUIDITY DRAIN"
        elif change_pct < -0.05:  # 5% decrease
            return "TGA SPENDING - LIQUIDITY ADDITION"
        else:
            return "NEUTRAL"

    def analyze_auction_demand(self, auction_data: pd.DataFrame) -> str:
        """Analyze Treasury auction demand."""
        if auction_data.empty:
            return "NO RECENT AUCTIONS"
        
        # Analyze recent auctions (last 5)
        recent_auctions = auction_data.tail(5) if len(auction_data) >= 5 else auction_data
        
        # Check if we have the required columns
        if "bid_to_cover_ratio" not in recent_auctions.columns or "competitive_accepted" not in recent_auctions.columns:
            return "INSUFFICIENT AUCTION DATA"
        
        # Calculate averages, handling NaN values
        avg_bid_to_cover = recent_auctions["bid_to_cover_ratio"].mean()
        avg_competitive = recent_auctions["competitive_accepted"].mean()
        
        if pd.isna(avg_bid_to_cover) or pd.isna(avg_competitive):
            return "INSUFFICIENT AUCTION DATA"
        
        data_source = "REAL" if len(auction_data) > 0 and not auction_data.index.name == "simulated" else "SIMULATED"
        
        if avg_bid_to_cover > 2.8 and avg_competitive > 80:
            return f"ABOVE AVERAGE DEMAND - SLIGHTLY BULLISH ({data_source})"
        elif avg_bid_to_cover < 2.2 or avg_competitive < 65:
            return f"BELOW AVERAGE DEMAND - SLIGHTLY BEARISH ({data_source})"
        else:
            return f"AVERAGE DEMAND - NEUTRAL ({data_source})"
    
    def create_auction_summary_table(self, auction_data: pd.DataFrame) -> pd.DataFrame:
        """Create a summary table of recent Treasury auctions (limited to 5 most recent)."""
        if auction_data.empty:
            return pd.DataFrame()

        # Get only the 5 most recent auctions to avoid clutter
        recent_auctions = auction_data.tail(5)

        summary_data = []
        for idx, row in recent_auctions.iterrows():
            auction_date = idx.strftime('%Y-%m-%d') if hasattr(idx, 'strftime') else str(idx)

            # Only include rows with meaningful data (skip if all key fields are missing)
            bid_to_cover = row.get('bid_to_cover_ratio')
            issue_amount = row.get('issue_amount')
            high_yield = row.get('high_yield')

            # Skip rows where all key metrics are missing or zero
            if (pd.isna(bid_to_cover) or bid_to_cover == 0) and \
               (pd.isna(issue_amount) or issue_amount == 0) and \
               (pd.isna(high_yield) or high_yield == 0):
                continue

            summary_data.append({
                'Date': auction_date,
                'Security Type': row.get('security_type', 'Bill'),
                'Term': row.get('security_term', '13-Week'),
                'Issue Amount': f"${issue_amount:.1f}B" if pd.notna(issue_amount) and issue_amount > 0 else '-',
                'Bid-to-Cover': f"{bid_to_cover:.2f}" if pd.notna(bid_to_cover) and bid_to_cover > 0 else '-',
                'High Yield': f"{high_yield:.2f}%" if pd.notna(high_yield) and high_yield > 0 else '-'
            })

        # If no valid data, create a simple summary row
        if not summary_data:
            avg_btc = auction_data['bid_to_cover_ratio'].mean() if 'bid_to_cover_ratio' in auction_data.columns else 2.5
            total_auctions = len(auction_data)

            summary_data.append({
                'Date': 'Recent Period',
                'Security Type': 'Mixed',
                'Term': 'Various',
                'Issue Amount': f"${auction_data.get('issue_amount', pd.Series([30])).sum():.0f}B",
                'Bid-to-Cover': f"{avg_btc:.2f}",
                'High Yield': f"{auction_data.get('high_yield', pd.Series([4.5])).mean():.2f}%"
            })

        return pd.DataFrame(summary_data)
    
    def analyze_auction_trends(self, auction_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze Treasury auction trends and performance metrics."""
        if auction_data.empty:
            return {"error": "No auction data available"}
        
        # Recent auctions analysis
        recent_auctions = auction_data.tail(10) if len(auction_data) >= 10 else auction_data
        
        # Calculate key metrics
        avg_bid_to_cover = recent_auctions['bid_to_cover_ratio'].mean() if 'bid_to_cover_ratio' in recent_auctions.columns else 0
        avg_yield = recent_auctions['high_yield'].mean() if 'high_yield' in recent_auctions.columns else 0
        total_issued = recent_auctions['issue_amount'].sum() if 'issue_amount' in recent_auctions.columns else 0
        total_competitive = recent_auctions['competitive_accepted'].sum() if 'competitive_accepted' in recent_auctions.columns else 0
        
        # Security type breakdown
        security_breakdown = recent_auctions['security_type'].value_counts().to_dict() if 'security_type' in recent_auctions.columns else {}
        
        # Term breakdown
        term_breakdown = recent_auctions['security_term'].value_counts().to_dict() if 'security_term' in recent_auctions.columns else {}
        
        # Bid-to-cover trend (last 5 vs previous 5)
        if len(recent_auctions) >= 6 and 'bid_to_cover_ratio' in recent_auctions.columns:
            recent_5_btc = recent_auctions.tail(5)['bid_to_cover_ratio'].mean()
            previous_5_btc = recent_auctions.iloc[-10:-5]['bid_to_cover_ratio'].mean() if len(recent_auctions) >= 10 else recent_5_btc
            btc_trend = "IMPROVING" if recent_5_btc > previous_5_btc else "DECLINING" if recent_5_btc < previous_5_btc else "STABLE"
        else:
            btc_trend = "INSUFFICIENT_DATA"
        
        return {
            "total_auctions": len(recent_auctions),
            "avg_bid_to_cover": avg_bid_to_cover,
            "avg_yield": avg_yield,
            "total_issued_billions": total_issued,
            "total_competitive_billions": total_competitive,
            "security_breakdown": security_breakdown,
            "term_breakdown": term_breakdown,
            "bid_to_cover_trend": btc_trend,
            "date_range": {
                "start": recent_auctions.index.min().strftime('%Y-%m-%d') if not recent_auctions.empty else None,
                "end": recent_auctions.index.max().strftime('%Y-%m-%d') if not recent_auctions.empty else None
            }
        }
    
    def plot_auction_analysis(self, auction_data: pd.DataFrame, save_path: str = None) -> Optional[str]:
        """Create comprehensive Treasury auction analysis charts."""
        if auction_data.empty:
            print("No auction data available for plotting")
            return None
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Treasury Auction Analysis', fontsize=16, fontweight='bold')
        
        # Recent auctions for detailed analysis
        recent_auctions = auction_data.tail(15) if len(auction_data) >= 15 else auction_data
        
        # 1. Bid-to-Cover Ratio Trend
        if 'bid_to_cover_ratio' in recent_auctions.columns:
            btc_data = recent_auctions['bid_to_cover_ratio'].dropna()
            if not btc_data.empty:
                ax1.plot(btc_data.index, btc_data.values, 'bo-', linewidth=2, markersize=6)
                ax1.axhline(y=btc_data.mean(), color='red', linestyle='--', alpha=0.7, 
                           label=f'Average: {btc_data.mean():.2f}')
                ax1.set_title('Bid-to-Cover Ratio Trend')
                ax1.set_ylabel('Bid-to-Cover Ratio')
                ax1.grid(True, alpha=0.3)
                ax1.legend()
                ax1.tick_params(axis='x', rotation=45)
        
        # 2. Issue Amount by Security Type
        if 'security_type' in recent_auctions.columns and 'issue_amount' in recent_auctions.columns:
            type_amounts = recent_auctions.groupby('security_type')['issue_amount'].sum()
            if not type_amounts.empty:
                colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
                wedges, texts, autotexts = ax2.pie(type_amounts.values, labels=type_amounts.index, 
                                                  autopct='%1.1f%%', colors=colors[:len(type_amounts)])
                ax2.set_title('Issue Amount by Security Type (Last 15 Auctions)')
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')
        
        # 3. High Yield Trend
        if 'high_yield' in recent_auctions.columns:
            yield_data = recent_auctions['high_yield'].dropna()
            if not yield_data.empty:
                ax3.plot(yield_data.index, yield_data.values, 'go-', linewidth=2, markersize=6)
                ax3.axhline(y=yield_data.mean(), color='red', linestyle='--', alpha=0.7, 
                           label=f'Average: {yield_data.mean():.2f}%')
                ax3.set_title('High Yield Trend')
                ax3.set_ylabel('Yield (%)')
                ax3.grid(True, alpha=0.3)
                ax3.legend()
                ax3.tick_params(axis='x', rotation=45)
        
        # 4. Auction Performance Summary (Bar Chart)
        if not recent_auctions.empty:
            # Create performance metrics
            performance_data = []
            metrics = ['Avg Bid-to-Cover', 'Avg Yield (%)', 'Total Issued ($B)', 'Total Competitive ($B)']
            
            avg_btc = recent_auctions['bid_to_cover_ratio'].mean() if 'bid_to_cover_ratio' in recent_auctions.columns else 0
            avg_yield = recent_auctions['high_yield'].mean() if 'high_yield' in recent_auctions.columns else 0
            total_issued = recent_auctions['issue_amount'].sum() if 'issue_amount' in recent_auctions.columns else 0
            total_competitive = recent_auctions['competitive_accepted'].sum() if 'competitive_accepted' in recent_auctions.columns else 0
            
            values = [avg_btc, avg_yield, total_issued, total_competitive]
            
            bars = ax4.bar(metrics, values, color=['skyblue', 'lightgreen', 'orange', 'lightcoral'])
            ax4.set_title('Auction Performance Summary (Last 15 Auctions)')
            ax4.set_ylabel('Value')
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                        f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
            
            ax4.tick_params(axis='x', rotation=45)
        
        # Adjust layout and save
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"Treasury auction analysis chart saved to {save_path}")
            return save_path
        else:
            plt.show()
            return None
    
    def generate_auction_analysis_report(self, auction_data: pd.DataFrame) -> str:
        """Generate a comprehensive text report of Treasury auction analysis."""
        if auction_data.empty:
            return "No Treasury auction data available for analysis."
        
        trends = self.analyze_auction_trends(auction_data)
        
        report = []
        report.append("=" * 80)
        report.append("TREASURY AUCTION ANALYSIS REPORT")
        report.append("=" * 80)
        
        # Summary statistics
        report.append(f"Analysis Period: {trends['date_range']['start']} to {trends['date_range']['end']}")
        report.append(f"Total Auctions Analyzed: {trends['total_auctions']}")
        report.append("")
        
        # Key metrics
        report.append("KEY AUCTION METRICS:")
        report.append(f"  Average Bid-to-Cover Ratio: {trends['avg_bid_to_cover']:.2f}")
        report.append(f"  Average High Yield: {trends['avg_yield']:.3f}%")
        report.append(f"  Total Amount Issued: ${trends['total_issued_billions']:.1f} Billion")
        report.append(f"  Total Competitive Accepted: ${trends['total_competitive_billions']:.1f} Billion")
        report.append(f"  Bid-to-Cover Trend: {trends['bid_to_cover_trend']}")
        report.append("")
        
        # Security type breakdown
        if trends['security_breakdown']:
            report.append("SECURITY TYPE BREAKDOWN:")
            for sec_type, count in trends['security_breakdown'].items():
                percentage = (count / trends['total_auctions']) * 100
                report.append(f"  {sec_type}: {count} auctions ({percentage:.1f}%)")
            report.append("")
        
        # Term breakdown
        if trends['term_breakdown']:
            report.append("SECURITY TERM BREAKDOWN:")
            for term, count in trends['term_breakdown'].items():
                percentage = (count / trends['total_auctions']) * 100
                report.append(f"  {term}: {count} auctions ({percentage:.1f}%)")
            report.append("")
        
        # Market assessment
        report.append("MARKET ASSESSMENT:")
        if trends['avg_bid_to_cover'] > 2.8:
            report.append("  🟢 STRONG DEMAND: Above-average bid-to-cover ratios indicate healthy investor appetite")
        elif trends['avg_bid_to_cover'] > 2.2:
            report.append("  🟡 MODERATE DEMAND: Average bid-to-cover ratios suggest normal market conditions")
        else:
            report.append("  🔴 WEAK DEMAND: Below-average bid-to-cover ratios may indicate reduced investor interest")
        
        if trends['bid_to_cover_trend'] == "IMPROVING":
            report.append("  📈 IMPROVING TREND: Recent auctions showing strengthening demand")
        elif trends['bid_to_cover_trend'] == "DECLINING":
            report.append("  📉 DECLINING TREND: Recent auctions showing weakening demand")
        else:
            report.append("  ➡️  STABLE TREND: Consistent demand patterns across recent auctions")
        
        report.append("=" * 80)
        
        return "\n".join(report) 