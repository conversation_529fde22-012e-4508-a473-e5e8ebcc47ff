"""
FRED API Client
Handles Federal Reserve Economic Data (FRED) API calls.
"""

import os
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional
from fredapi import Fred
from dotenv import load_dotenv

load_dotenv()


class FredDataFetcher:
    """Fetches data from FRED API."""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv('FRED_API_KEY')
        self.cache = {}
        self.cache_expiry = timedelta(hours=int(os.getenv('FRED_CACHE_HOURS', '4')))
        
        if not self.api_key:
            raise ValueError("FRED API key not found. Please set FRED_API_KEY in .env file")
        
        try:
            self.fred = Fred(api_key=self.api_key)
        except ValueError as e:
            print(f"Error initializing Fred API: {e}")
            self.fred = None

    def fetch_series(self, series_id: str, start_date: datetime, end_date: datetime) -> pd.Series:
        """Fetch a single FRED series with caching."""
        if self.fred is None:
            print("Fred API not initialized.")
            return pd.Series(dtype=float)

        cache_key = (series_id, start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d"))
        now = datetime.now()

        # Check cache
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if now - timestamp < self.cache_expiry:
                print(f"Using cached FRED data for {series_id}")
                return data.copy()

        print(f"Fetching FRED data for {series_id}...")
        try:
            data = self.fred.get_series(series_id, observation_start=start_date, observation_end=end_date)
            data = data.dropna()
            self.cache[cache_key] = (data.copy(), now)
            return data
        except Exception as e:
            print(f"Error fetching FRED series {series_id}: {e}")
            return pd.Series(dtype=float)

    def get_fed_balance_sheet(self, start_date: datetime, end_date: datetime) -> pd.Series:
        """Get Federal Reserve balance sheet data (WALCL)."""
        return self.fetch_series('WALCL', start_date, end_date)

    def get_reverse_repo(self, start_date: datetime, end_date: datetime) -> pd.Series:
        """Get Reverse Repo operations data (RRPONTSYD)."""
        return self.fetch_series('RRPONTSYD', start_date, end_date)

    def get_treasury_yield(self, start_date: datetime, end_date: datetime, maturity: str = '10Y') -> pd.Series:
        """Get Treasury yield data."""
        series_map = {
            '3M': 'DGS3MO',
            '6M': 'DGS6MO',
            '1Y': 'DGS1',
            '2Y': 'DGS2',
            '5Y': 'DGS5',
            '10Y': 'DGS10',
            '30Y': 'DGS30'
        }
        
        series_id = series_map.get(maturity, 'DGS10')
        return self.fetch_series(series_id, start_date, end_date)

    def get_unemployment_rate(self, start_date: datetime, end_date: datetime) -> pd.Series:
        """Get unemployment rate data (UNRATE)."""
        return self.fetch_series('UNRATE', start_date, end_date)

    def get_inflation_rate(self, start_date: datetime, end_date: datetime) -> pd.Series:
        """Get CPI inflation rate data (CPIAUCSL)."""
        return self.fetch_series('CPIAUCSL', start_date, end_date)

    def get_gdp(self, start_date: datetime, end_date: datetime) -> pd.Series:
        """Get GDP data (GDP)."""
        return self.fetch_series('GDP', start_date, end_date) 