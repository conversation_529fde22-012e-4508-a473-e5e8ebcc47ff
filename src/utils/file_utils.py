"""
File Utilities
Common file operations and path management functions.
"""

import os
import re
from pathlib import Path
from typing import Optional


def ensure_directory(directory_path: str) -> None:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        directory_path: Path to directory
    """
    Path(directory_path).mkdir(parents=True, exist_ok=True)


def safe_filename(filename: str, max_length: int = 255) -> str:
    """
    Create a safe filename by removing/replacing invalid characters.
    
    Args:
        filename: Original filename
        max_length: Maximum filename length
        
    Returns:
        Safe filename
    """
    # Remove invalid characters
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove multiple consecutive underscores
    safe_name = re.sub(r'_+', '_', safe_name)
    
    # Trim length
    if len(safe_name) > max_length:
        name, ext = os.path.splitext(safe_name)
        max_name_length = max_length - len(ext)
        safe_name = name[:max_name_length] + ext
    
    return safe_name


def get_output_path(base_dir: str, filename: str, create_dir: bool = True) -> str:
    """
    Get a full output path, ensuring the directory exists.
    
    Args:
        base_dir: Base output directory
        filename: Filename
        create_dir: Whether to create the directory if it doesn't exist
        
    Returns:
        Full output path
    """
    if create_dir:
        ensure_directory(base_dir)
    
    safe_name = safe_filename(filename)
    return os.path.join(base_dir, safe_name)


def get_unique_filename(filepath: str) -> str:
    """
    Get a unique filename by appending a number if the file exists.
    
    Args:
        filepath: Original file path
        
    Returns:
        Unique file path
    """
    if not os.path.exists(filepath):
        return filepath
    
    base, ext = os.path.splitext(filepath)
    counter = 1
    
    while True:
        new_path = f"{base}_{counter}{ext}"
        if not os.path.exists(new_path):
            return new_path
        counter += 1


def get_file_size_mb(filepath: str) -> float:
    """
    Get file size in megabytes.
    
    Args:
        filepath: Path to file
        
    Returns:
        File size in MB
    """
    if not os.path.exists(filepath):
        return 0.0
    
    size_bytes = os.path.getsize(filepath)
    return size_bytes / (1024 * 1024)


def cleanup_old_files(directory: str, pattern: str = "*", max_age_days: int = 7) -> int:
    """
    Clean up old files in a directory.
    
    Args:
        directory: Directory to clean
        pattern: File pattern to match
        max_age_days: Maximum age in days
        
    Returns:
        Number of files deleted
    """
    if not os.path.exists(directory):
        return 0
    
    import time
    from glob import glob
    
    cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
    files_deleted = 0
    
    for filepath in glob(os.path.join(directory, pattern)):
        if os.path.isfile(filepath) and os.path.getmtime(filepath) < cutoff_time:
            try:
                os.remove(filepath)
                files_deleted += 1
            except OSError:
                pass  # Ignore errors
    
    return files_deleted


def read_text_file(filepath: str, encoding: str = 'utf-8') -> Optional[str]:
    """
    Safely read a text file.
    
    Args:
        filepath: Path to file
        encoding: File encoding
        
    Returns:
        File contents or None if error
    """
    try:
        with open(filepath, 'r', encoding=encoding) as f:
            return f.read()
    except (OSError, UnicodeDecodeError):
        return None


def write_text_file(filepath: str, content: str, encoding: str = 'utf-8') -> bool:
    """
    Safely write a text file.
    
    Args:
        filepath: Path to file
        content: Content to write
        encoding: File encoding
        
    Returns:
        True if successful
    """
    try:
        # Ensure directory exists
        directory = os.path.dirname(filepath)
        if directory:
            ensure_directory(directory)
        
        with open(filepath, 'w', encoding=encoding) as f:
            f.write(content)
        return True
    except OSError:
        return False 