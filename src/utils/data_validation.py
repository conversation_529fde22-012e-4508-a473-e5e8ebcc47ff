"""
Data Validation Utilities
Functions to validate data integrity across the application.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime


def validate_dataframe(
    df: pd.DataFrame,
    required_columns: List[str],
    min_rows: int = 1,
    name: str = "DataFrame"
) -> None:
    """
    Validate a DataFrame has required structure and content.
    
    Args:
        df: DataFrame to validate
        required_columns: List of required column names
        min_rows: Minimum number of rows required
        name: Name for error messages
        
    Raises:
        ValueError: If validation fails
    """
    if df is None or df.empty:
        raise ValueError(f"{name} is empty or None")
    
    if len(df) < min_rows:
        raise ValueError(f"{name} has {len(df)} rows, minimum {min_rows} required")
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"{name} missing required columns: {missing_columns}")
    
    # Check for all-NaN columns
    nan_columns = [col for col in required_columns if df[col].isna().all()]
    if nan_columns:
        raise ValueError(f"{name} has all-NaN values in columns: {nan_columns}")


def validate_price_data(df: pd.DataFrame) -> None:
    """
    Validate price data DataFrame.
    
    Args:
        df: Price data DataFrame
        
    Raises:
        ValueError: If validation fails
    """
    required_columns = ['Open', 'High', 'Low', 'Close', 'Adj Close', 'Volume']
    validate_dataframe(df, required_columns, min_rows=30, name="Price data")
    
    # Check for negative prices
    price_columns = ['Open', 'High', 'Low', 'Close', 'Adj Close']
    for col in price_columns:
        if (df[col] <= 0).any():
            raise ValueError(f"Price data contains non-positive values in {col}")
    
    # Check for negative volume
    if (df['Volume'] < 0).any():
        raise ValueError("Price data contains negative volume")
    
    # Check for logical price relationships
    invalid_ohlc = (
        (df['High'] < df['Low']) |
        (df['High'] < df['Open']) |
        (df['High'] < df['Close']) |
        (df['Low'] > df['Open']) |
        (df['Low'] > df['Close'])
    )
    if invalid_ohlc.any():
        raise ValueError("Price data contains invalid OHLC relationships")


def validate_option_chain(option_chain: Dict[str, Any]) -> None:
    """
    Validate option chain data structure.
    
    Args:
        option_chain: Option chain dictionary
        
    Raises:
        ValueError: If validation fails
    """
    if not option_chain:
        raise ValueError("Option chain is empty or None")
    
    if 'calls' not in option_chain or 'puts' not in option_chain:
        raise ValueError("Option chain missing 'calls' or 'puts' data")
    
    calls = option_chain['calls']
    puts = option_chain['puts']
    
    if not calls and not puts:
        raise ValueError("Option chain has no call or put data")
    
    # Validate option data structure
    for option_type, options in [('calls', calls), ('puts', puts)]:
        if not options:
            continue
            
        required_fields = ['strike', 'bid', 'ask', 'last', 'volume', 'open_interest', 'implied_volatility']
        
        for i, option in enumerate(options[:5]):  # Check first 5 options
            missing_fields = [field for field in required_fields if field not in option]
            if missing_fields:
                raise ValueError(f"Option {option_type}[{i}] missing fields: {missing_fields}")
            
            # Validate numeric fields
            if option.get('strike', 0) <= 0:
                raise ValueError(f"Option {option_type}[{i}] has invalid strike: {option.get('strike')}")
            
            if option.get('bid', 0) < 0:
                raise ValueError(f"Option {option_type}[{i}] has negative bid: {option.get('bid')}")
            
            if option.get('ask', 0) < 0:
                raise ValueError(f"Option {option_type}[{i}] has negative ask: {option.get('ask')}")


def validate_volatility_data(volatility_metrics: Dict[str, Any]) -> None:
    """
    Validate volatility metrics data.
    
    Args:
        volatility_metrics: Volatility metrics dictionary
        
    Raises:
        ValueError: If validation fails
    """
    if not volatility_metrics:
        raise ValueError("Volatility metrics is empty or None")
    
    required_keys = ['current_price', 'historical_volatilities', 'implied_volatility']
    missing_keys = [key for key in required_keys if key not in volatility_metrics]
    if missing_keys:
        raise ValueError(f"Volatility metrics missing keys: {missing_keys}")
    
    # Validate current price
    current_price = volatility_metrics.get('current_price')
    if not current_price or current_price <= 0:
        raise ValueError(f"Invalid current price: {current_price}")
    
    # Validate historical volatilities
    hist_vol = volatility_metrics.get('historical_volatilities', {})
    if not hist_vol:
        raise ValueError("No historical volatilities provided")
    
    for window, vol in hist_vol.items():
        if vol is None or vol < 0 or vol > 5:  # Volatility should be reasonable
            raise ValueError(f"Invalid historical volatility for {window}: {vol}")
    
    # Validate implied volatility
    iv = volatility_metrics.get('implied_volatility')
    if iv is not None and (iv < 0 or iv > 5):
        raise ValueError(f"Invalid implied volatility: {iv}")


def validate_money_market_data(money_market_analysis: Dict[str, Any]) -> None:
    """
    Validate money market analysis data.
    
    Args:
        money_market_analysis: Money market analysis dictionary
        
    Raises:
        ValueError: If validation fails
    """
    if not money_market_analysis:
        raise ValueError("Money market analysis is empty or None")
    
    # Check for key sections
    expected_sections = ['treasury_analysis', 'rrp_analysis', 'tga_analysis']
    for section in expected_sections:
        if section not in money_market_analysis:
            raise ValueError(f"Money market analysis missing section: {section}")


def sanitize_numeric_value(value: Any, default: float = 0.0) -> float:
    """
    Sanitize a numeric value, handling NaN, inf, and non-numeric types.
    
    Args:
        value: Value to sanitize
        default: Default value if sanitization fails
        
    Returns:
        Sanitized numeric value
    """
    try:
        if pd.isna(value) or np.isinf(value):
            return default
        return float(value)
    except (ValueError, TypeError):
        return default


def clean_dataframe(df: pd.DataFrame, numeric_columns: Optional[List[str]] = None) -> pd.DataFrame:
    """
    Clean a DataFrame by handling missing values and infinite values.
    
    Args:
        df: DataFrame to clean
        numeric_columns: List of numeric columns to clean (if None, auto-detect)
        
    Returns:
        Cleaned DataFrame
    """
    df_clean = df.copy()
    
    if numeric_columns is None:
        numeric_columns = df_clean.select_dtypes(include=[np.number]).columns.tolist()
    
    # Replace infinite values with NaN
    df_clean[numeric_columns] = df_clean[numeric_columns].replace([np.inf, -np.inf], np.nan)
    
    # Forward fill then backward fill for time series data
    if isinstance(df_clean.index, pd.DatetimeIndex):
        df_clean[numeric_columns] = df_clean[numeric_columns].fillna(method='ffill').fillna(method='bfill')
    else:
        # For non-time series, fill with median
        for col in numeric_columns:
            if df_clean[col].isna().any():
                median_val = df_clean[col].median()
                df_clean[col] = df_clean[col].fillna(median_val)
    
    return df_clean 