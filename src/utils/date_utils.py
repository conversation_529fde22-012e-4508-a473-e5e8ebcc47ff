"""
Date Utilities
Common date operations and formatting functions.
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Tuple, List


def get_business_days(start_date: datetime, end_date: datetime) -> int:
    """
    Get the number of business days between two dates.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        Number of business days
    """
    return len(pd.bdate_range(start_date, end_date))


def format_date_range(start_date: datetime, end_date: datetime) -> str:
    """
    Format a date range for display.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        Formatted date range string
    """
    return f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"


def get_trading_days_back(days: int, from_date: datetime = None) -> datetime:
    """
    Get a date that is a certain number of trading days back.
    
    Args:
        days: Number of trading days to go back
        from_date: Reference date (default: today)
        
    Returns:
        Date that is 'days' trading days back
    """
    if from_date is None:
        from_date = datetime.now()
    
    # Approximate: trading days are roughly 5/7 of calendar days
    # Add buffer for weekends and holidays
    calendar_days = int(days * 1.5)
    start_estimate = from_date - timedelta(days=calendar_days)
    
    # Generate business days and take the exact number needed
    business_days = pd.bdate_range(end=from_date, periods=days + 1)
    return business_days[0].to_pydatetime()


def get_next_expiration_fridays(num_fridays: int = 8) -> List[datetime]:
    """
    Get the next N option expiration Fridays (3rd Friday of each month).
    
    Args:
        num_fridays: Number of expiration Fridays to return
        
    Returns:
        List of expiration Friday dates
    """
    today = datetime.now()
    fridays = []
    
    # Start from current month
    year = today.year
    month = today.month
    
    while len(fridays) < num_fridays:
        # Find third Friday of the month
        first_day = datetime(year, month, 1)
        first_friday = first_day + timedelta(days=(4 - first_day.weekday()) % 7)
        third_friday = first_friday + timedelta(days=14)
        
        # Only include future dates
        if third_friday > today:
            fridays.append(third_friday)
        
        # Move to next month
        month += 1
        if month > 12:
            month = 1
            year += 1
    
    return fridays[:num_fridays]


def is_market_hours(dt: datetime = None) -> bool:
    """
    Check if the given datetime is during market hours (9:30 AM - 4:00 PM ET, weekdays).
    
    Args:
        dt: Datetime to check (default: now)
        
    Returns:
        True if during market hours
    """
    if dt is None:
        dt = datetime.now()
    
    # Check if weekday
    if dt.weekday() >= 5:  # Saturday = 5, Sunday = 6
        return False
    
    # Check time (assuming ET timezone)
    market_open = dt.replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = dt.replace(hour=16, minute=0, second=0, microsecond=0)
    
    return market_open <= dt <= market_close


def get_market_calendar_range(start_date: datetime, end_date: datetime) -> pd.DatetimeIndex:
    """
    Get market trading days between two dates.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        DatetimeIndex of trading days
    """
    return pd.bdate_range(start_date, end_date)


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h" 