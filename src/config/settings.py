"""
Settings Configuration
Manages all application settings from environment variables.
"""

import os
from typing import Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class Settings:
    """Application settings loaded from environment variables."""
    
    # API Keys
    polygon_api_key: str
    fred_api_key: str
    
    # Default Configuration
    default_ticker: str = "SPX"
    default_output_dir: str = "output"
    default_days_back: int = 30
    
    # Analysis Parameters
    volatility_window_days: int = 252
    money_market_days: int = 90
    option_chain_price_range: float = 0.2
    
    # Report Settings
    enable_pdf_generation: bool = True
    enable_narrative_generation: bool = False
    chart_dpi: int = 300
    chart_style: str = "seaborn-v0_8"
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "logs/market_analysis.log"
    
    # Data Sources
    yfinance_timeout: int = 30
    polygon_timeout: int = 30
    fred_timeout: int = 30
    
    # Risk Management
    max_option_strikes: int = 100
    max_expiration_days: int = 365
    
    @classmethod
    def from_env(cls) -> 'Settings':
        """Create settings from environment variables."""
        
        # Required API keys
        polygon_key = os.getenv('POLYGON_API_KEY')
        fred_key = os.getenv('FRED_API_KEY')
        
        if not polygon_key:
            raise ValueError("POLYGON_API_KEY environment variable is required")
        if not fred_key:
            raise ValueError("FRED_API_KEY environment variable is required")
        
        return cls(
            # API Keys
            polygon_api_key=polygon_key,
            fred_api_key=fred_key,
            
            # Default Configuration
            default_ticker=os.getenv('DEFAULT_TICKER', 'SPX'),
            default_output_dir=os.getenv('DEFAULT_OUTPUT_DIR', 'output'),
            default_days_back=int(os.getenv('DEFAULT_DAYS_BACK', '30')),
            
            # Analysis Parameters
            volatility_window_days=int(os.getenv('VOLATILITY_WINDOW_DAYS', '252')),
            money_market_days=int(os.getenv('MONEY_MARKET_DAYS', '90')),
            option_chain_price_range=float(os.getenv('OPTION_CHAIN_PRICE_RANGE', '0.2')),
            
            # Report Settings
            enable_pdf_generation=os.getenv('ENABLE_PDF_GENERATION', 'true').lower() == 'true',
            enable_narrative_generation=os.getenv('ENABLE_NARRATIVE_GENERATION', 'false').lower() == 'true',
            chart_dpi=int(os.getenv('CHART_DPI', '300')),
            chart_style=os.getenv('CHART_STYLE', 'seaborn-v0_8'),
            
            # Logging
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            log_file=os.getenv('LOG_FILE', 'logs/market_analysis.log'),
            
            # Data Sources
            yfinance_timeout=int(os.getenv('YFINANCE_TIMEOUT', '30')),
            polygon_timeout=int(os.getenv('POLYGON_TIMEOUT', '30')),
            fred_timeout=int(os.getenv('FRED_TIMEOUT', '30')),
            
            # Risk Management
            max_option_strikes=int(os.getenv('MAX_OPTION_STRIKES', '100')),
            max_expiration_days=int(os.getenv('MAX_EXPIRATION_DAYS', '365'))
        )
    
    def validate(self) -> None:
        """Validate settings."""
        if self.default_days_back <= 0:
            raise ValueError("default_days_back must be positive")
        if self.volatility_window_days <= 0:
            raise ValueError("volatility_window_days must be positive")
        if self.money_market_days <= 0:
            raise ValueError("money_market_days must be positive")
        if not 0 < self.option_chain_price_range <= 1:
            raise ValueError("option_chain_price_range must be between 0 and 1")


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get the global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings.from_env()
        _settings.validate()
    return _settings 