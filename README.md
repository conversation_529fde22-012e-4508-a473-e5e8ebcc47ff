# Checklist Driver for Money Market Analysis and Trading Signals

This directory contains a practical driver for implementing the money market analysis checklist and generating actionable trading signals.

## Structure

- `checklist_driver.py` - Main driver script that implements the checklist logic
- `money_market_monitor.py` - Module for analyzing money market conditions
- `option_signals.py` - Module for generating option trading signals
- `references.txt` - References and sources
- `requirements.txt` - Required Python packages

## Usage

1. Run the checklist driver:
```
python checklist_driver.py
```

2. The driver will:
   - Fetch current money market data (TGA, RRP, Fed balance sheet)
   - Analyze liquidity conditions
   - Check Treasury auction schedule
   - Identify major option strike levels
   - Generate actionable trading signals
   - Output a daily checklist report

## Output

The driver generates:
- A daily checklist status report (HTML and PDF)
- Trading signal recommendations
- Money market condition alerts
- Option strike level visualization
