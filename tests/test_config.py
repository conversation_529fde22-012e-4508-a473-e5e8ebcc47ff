"""
Configuration Tests
Tests for the configuration management system.
"""

import os
import pytest
from unittest.mock import patch
from src.config.settings import Settings, get_settings


class TestSettings:
    """Test the Settings class."""
    
    def test_settings_from_env_with_required_keys(self):
        """Test settings creation with required API keys."""
        with patch.dict(os.environ, {
            'POLYGON_API_KEY': 'test_polygon_key',
            'FRED_API_KEY': 'test_fred_key'
        }):
            settings = Settings.from_env()
            assert settings.polygon_api_key == 'test_polygon_key'
            assert settings.fred_api_key == 'test_fred_key'
            assert settings.default_ticker == 'SPX'
    
    def test_settings_missing_polygon_key(self):
        """Test settings creation fails without Polygon API key."""
        with patch.dict(os.environ, {
            'FRED_API_KEY': 'test_fred_key'
        }, clear=True):
            with pytest.raises(ValueError, match="POLYGON_API_KEY"):
                Settings.from_env()
    
    def test_settings_missing_fred_key(self):
        """Test settings creation fails without FRED API key."""
        with patch.dict(os.environ, {
            'POLYGON_API_KEY': 'test_polygon_key'
        }, clear=True):
            with pytest.raises(ValueError, match="FRED_API_KEY"):
                Settings.from_env()
    
    def test_settings_with_custom_values(self):
        """Test settings with custom environment values."""
        with patch.dict(os.environ, {
            'POLYGON_API_KEY': 'test_polygon_key',
            'FRED_API_KEY': 'test_fred_key',
            'DEFAULT_TICKER': 'AAPL',
            'DEFAULT_DAYS_BACK': '60',
            'VOLATILITY_WINDOW_DAYS': '100'
        }):
            settings = Settings.from_env()
            assert settings.default_ticker == 'AAPL'
            assert settings.default_days_back == 60
            assert settings.volatility_window_days == 100
    
    def test_settings_validation_positive_values(self):
        """Test settings validation for positive values."""
        settings = Settings(
            polygon_api_key='test',
            fred_api_key='test',
            default_days_back=-1
        )
        with pytest.raises(ValueError, match="default_days_back must be positive"):
            settings.validate()
    
    def test_settings_validation_price_range(self):
        """Test settings validation for option price range."""
        settings = Settings(
            polygon_api_key='test',
            fred_api_key='test',
            option_chain_price_range=1.5
        )
        with pytest.raises(ValueError, match="option_chain_price_range must be between 0 and 1"):
            settings.validate()


def test_get_settings_singleton():
    """Test that get_settings returns the same instance."""
    with patch.dict(os.environ, {
        'POLYGON_API_KEY': 'test_polygon_key',
        'FRED_API_KEY': 'test_fred_key'
    }):
        # Clear the global settings
        import src.config.settings
        src.config.settings._settings = None
        
        settings1 = get_settings()
        settings2 = get_settings()
        
        assert settings1 is settings2 