#!/bin/bash

# Market Analysis Runner Script
# Usage: ./run_market_analysis.sh [narrative]
# 
# Prerequisites:
#   - Virtual environment at .venv/ with required packages installed
#   - Run: python -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt
# 
# Parameters:
#   narrative (optional) - Enable ChatGPT narrative generation
#
# Examples:
#   ./run_market_analysis.sh           # Run without narrative
#   ./run_market_analysis.sh narrative # Run with narrative

# Default parameters
TICKER="SPX"
DAYS="1000"
OUTPUT_DIR="output"

# Check if narrative parameter is provided
if [ "$1" = "narrative" ]; then
    NARRATIVE_FLAG="--narrative"
    echo "🤖 Running Market Analysis with ChatGPT Narrative Generation"
else
    NARRATIVE_FLAG=""
    echo "📊 Running Market Analysis (use 'narrative' parameter to enable ChatGPT)"
fi

# Display configuration
echo "Configuration:"
echo "  Ticker: $TICKER"
echo "  Days: $DAYS"
echo "  Output: $OUTPUT_DIR"
echo "  Narrative: $([ -n "$NARRATIVE_FLAG" ] && echo "Enabled" || echo "Disabled")"
echo ""

# Ensure output directory exists
mkdir -p "$OUTPUT_DIR"

# Activate virtual environment
if [ -d ".venv" ]; then
    echo "Activating virtual environment..."
    source .venv/bin/activate
    echo "✓ Virtual environment activated"
else
    echo "⚠️ Virtual environment not found at .venv/"
    echo "   Please create one with: python -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# Run the analysis
echo "Starting market analysis..."
python src/driver/main.py \
    --ticker "$TICKER" \
    --days "$DAYS" \
    --output-dir "$OUTPUT_DIR" \
    $NARRATIVE_FLAG \
    --analysis-type gamma_vanna

# Store the exit code
ANALYSIS_EXIT_CODE=$?

# Deactivate virtual environment
if [ -n "$VIRTUAL_ENV" ]; then
    deactivate
    echo "✓ Virtual environment deactivated"
fi

# Check if analysis was successful
if [ $ANALYSIS_EXIT_CODE -eq 0 ]; then
    echo ""
    echo "✅ Market analysis completed successfully!"
    echo ""
    echo "📁 Output files:"
    ls -la "$OUTPUT_DIR"/*.pdf "$OUTPUT_DIR"/*.html 2>/dev/null || echo "   No PDF/HTML files found"
    
    if [ -n "$NARRATIVE_FLAG" ]; then
        echo ""
        echo "📝 Narrative files:"
        ls -la "$OUTPUT_DIR"/*narrative*.md "$OUTPUT_DIR"/*macro*.md 2>/dev/null || echo "   No narrative files found"
    fi
else
    echo ""
    echo "❌ Market analysis failed!"
    exit 1
fi 