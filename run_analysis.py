#!/usr/bin/env python3
"""
Test Enhanced Market Analysis
Run the complete analysis with all new advanced components.
"""

import os
import sys
import argparse
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

from driver.main import MarketAnalysisDriver

def main():
    """Run enhanced market analysis test."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Enhanced Market Analysis')
    parser.add_argument('--ticker', default='SPX', help='Ticker symbol to analyze (default: SPX)')
    parser.add_argument('--output-dir', default='output', help='Output directory (default: output)')
    parser.add_argument('--narrative', action='store_true', help='Enable narrative generation')
    parser.add_argument('--days-back', type=int, default=30, help='Days of historical data (default: 30)')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("ENHANCED MARKET ANALYSIS TEST")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print()
    
    # Create output directory
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Initialize driver with enhanced analysis
        driver = MarketAnalysisDriver(
            ticker=args.ticker,  # Use command line argument
            days_back=args.days_back,
            output_dir=output_dir,
            enable_narrative=args.narrative,
            analysis_type="gamma_vanna"
        )
        
        # Run complete analysis
        results = driver.run_complete_analysis()
        
        if results["success"]:
            print("\n" + "=" * 80)
            print("ENHANCED ANALYSIS RESULTS SUMMARY")
            print("=" * 80)
            
            # Basic analysis
            print("\n📊 BASIC ANALYSIS:")
            opt = results["option_signals_analysis"]
            
            print(f"  Option Signal: {opt.get('signal', 'Unknown')} ({opt.get('confidence', 'Unknown')})")
            print(f"  Net Gamma: {opt.get('net_gamma', 0):,.0f}")
            print(f"  Net Vega: {opt.get('net_vega', 0):,.0f}")
            
            # Advanced options flow analysis
            print("\n🔄 ADVANCED OPTIONS FLOW ANALYSIS:")
            flow = results.get("options_flow_analysis", {})
            if flow and 'error' not in flow:
                flow_signals = flow.get('flow_signals', {})
                print(f"  Flow Signal: {flow_signals.get('primary_signal', 'Unknown')} ({flow_signals.get('confidence', 'Unknown')})")
                print(f"  Time Horizon: {flow_signals.get('time_horizon', 'Unknown')}")
                
                key_factors = flow_signals.get('key_factors', [])
                if key_factors:
                    print("  Key Factors:")
                    for factor in key_factors[:3]:  # Show top 3
                        print(f"    • {factor}")
                
                risk_factors = flow_signals.get('risk_factors', [])
                if risk_factors:
                    print("  Risk Factors:")
                    for risk in risk_factors:
                        print(f"    ⚠️ {risk}")
                        
                # Show aggregate exposures
                agg = flow.get('aggregate_metrics', {}).get('aggregate_exposures', {})
                if agg:
                    print("  Net Greek Exposures:")
                    for greek, data in list(agg.items())[:3]:  # Show top 3
                        if isinstance(data, dict):
                            greek_name = greek.replace('net_dealer_', '').upper()
                            print(f"    {greek_name}: {data.get('total', 0):,.0f}")
            else:
                print("  ❌ Options flow analysis failed or no data available")
            
            # Show generated reports and charts
            print("\n📄 GENERATED REPORTS:")
            print(f"  HTML Report: {results['html_report_path']}")
            
            # Show any errors
            if results["error_summary"]:
                print("\n⚠️ WARNINGS/ERRORS:")
                for error in results["error_summary"]:
                    print(f"  • {error}")
            
            print(f"\n✅ Enhanced analysis completed successfully!")
            print(f"📁 Output directory: {output_dir}")
            
        else:
            print(f"\n❌ Analysis failed: {results.get('error', 'Unknown error')}")
            if results.get("error_summary"):
                print("\nError details:")
                for error in results["error_summary"]:
                    print(f"  • {error}")
    
    except Exception as e:
        print(f"\n❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print(f"\nTest completed at: {datetime.now()}")

if __name__ == "__main__":
    main() 